#!/bin/bash

# Cấu h<PERSON>nh Telegram
BOT_TOKEN="**********************************************"
CHAT_ID="-1002623121529"
URL="http://66.135.16.218:8000/send-message"

get_service_name() {
    pid=$1
    # Dùng systemctl để tìm service nếu có
    systemctl status $(ps -p $pid -o unit= 2>/dev/null | awk '{print $1}') 2>/dev/null | grep "Loaded:" | awk '{print $2}' || echo "N/A"
}

# ========== LẤY TOP 3 CPU ==========
cpu_output=$(ps -eo pid,comm,%cpu --sort=-%cpu | head -n 4 | tail -n 3)
cpu_message="🔥 *Top 3 CPU usage*:"
while read -r line; do
    pid=$(echo $line | awk '{print $1}')
    proc=$(echo $line | awk '{print $2}')
    usage=$(echo $line | awk '{print $3}')
    service=$(get_service_name $pid)
    cpu_message+="\n• *$proc* (PID: \`$pid\`, CPU: *$usage%*, Service: \`$service\`)"
done <<< "$cpu_output"

# ========== LẤY TOP 3 RAM ==========
mem_output=$(ps -eo pid,comm,%mem --sort=-%mem | head -n 4 | tail -n 3)
mem_message="💾 *Top 3 RAM usage*:"
while read -r line; do
    pid=$(echo $line | awk '{print $1}')
    proc=$(echo $line | awk '{print $2}')
    usage=$(echo $line | awk '{print $3}')
    service=$(get_service_name $pid)
    mem_message+="\n• *$proc* (PID: \`$pid\`, RAM: *$usage%*, Service: \`$service\`)"
done <<< "$mem_output"

# ========== TỔNG HỢP & GỬI ==========
message="🔍 *High Resource Usage Detected*\n\n$cpu_message\n\n$mem_message"

curl -s -X POST "$URL" \
     -H "accept: application/json" \
     -H "Content-Type: application/json" \
     -d '{
        "chat_id": "'"$CHAT_ID"'",
        "message": "'"$message"'",
        "parse_mode": "Markdown",
        "token": "'"$BOT_TOKEN"'"
     }'
