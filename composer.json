{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ahmedabdelaal/filament-json-preview": "^1.0", "dotswan/filament-code-editor": "^1.1", "eightynine/filament-excel-import": "^3.1", "filament/filament": "^3.3", "hayderhatem/filament-excel-import": "^3.1", "ianw/quickchart": "^1.3", "laravel/framework": "^11.0", "laravel/nightwatch": "^1.7", "laravel/prompts": "^0.3.6", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.10.1", "lib/elasticsearch": "@dev", "nunomaduro/pokio": "^0.1", "pepperfm/filament-json": "^2.0", "phpoffice/phpspreadsheet": "^1.29", "spatie/fork": "^1.2", "symfony/dom-crawler": "^7.2", "tomatophp/filament-artisan": "^1.1", "valentin-morice/filament-json-column": "^2.0", "vladimir-yuldashev/laravel-queue-rabbitmq": "^14.1"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.43", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.5.3"}, "repositories": [{"type": "path", "url": "./lib/Elasticsearch"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}