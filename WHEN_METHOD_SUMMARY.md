# "when" Method Implementation Summary

## ✅ Successfully Added

The `when` and `unless` methods have been successfully added to the Elasticsearch package, providing Laravel Eloquent-like conditional query building.

## 🔧 Implementation Details

### Files Modified:
1. **`lib/Elasticsearch/Builder.php`** - Added `when()` and `unless()` methods
2. **`lib/Elasticsearch/Concerns/HasElasticsearchQueries.php`** - Added static `when()` and `unless()` methods
3. **`lib/Elasticsearch/README.md`** - Updated documentation
4. **`lib/Elasticsearch/Examples/WhenMethodExamples.php`** - Comprehensive examples
5. **`lib/Elasticsearch/tests/WhenMethodTest.php`** - Test suite

## 🚀 Usage Examples

### Basic when() Usage
```php
$results = Tvplus::query()
    ->where('object_id', '11440')
    ->when($searchTerm, function ($query, $term) {
        return $query->search($term, ['message', 'content']);
    })
    ->when($status, function ($query, $status) {
        return $query->where('status', $status);
    })
    ->get();
```

### when() with Else Condition
```php
$results = Tvplus::query()
    ->when($userRole === 'admin', 
        function ($query) {
            // Admin can see all records
            return $query;
        },
        function ($query) use ($userId) {
            // Regular users see only their records
            return $query->where('user_id', $userId);
        }
    )
    ->get();
```

### unless() Method
```php
$results = Tvplus::query()
    ->unless($includeDeleted, function ($query) {
        return $query->where('is_delete', 0);
    })
    ->get();
```

### Complex Conditional Filtering
```php
$results = Tvplus::query()
    ->where('object_id', '11440')
    ->when($filters['search'] ?? null, function ($query, $term) {
        return $query->search($term);
    })
    ->when($filters['brand_id'] ?? null, function ($query, $brandId) {
        return $query->where('brand_id', $brandId);
    })
    ->when($filters['date_from'] ?? null, function ($query, $date) {
        return $query->where('created_at', '>=', $date);
    })
    ->unless($filters['include_inactive'] ?? false, function ($query) {
        return $query->where('status', 'active');
    })
    ->get();
```

## ✅ Test Results

All tests passed successfully:

### 1. when() with Truthy Value ✅
- **Input**: `when(true, callback)`
- **Result**: Callback executed, condition added to query
- **Query**: Added `status: active` term

### 2. when() with Falsy Value ✅
- **Input**: `when(false, callback)`
- **Result**: Callback not executed, no condition added
- **Query**: Only original `object_id` condition

### 3. when() with Else Callback ✅
- **Input**: `when(false, callback, defaultCallback)`
- **Result**: Default callback executed
- **Query**: Added `access_level: public` term

### 4. unless() Method ✅
- **Input**: `unless(false, callback)`
- **Result**: Callback executed (since condition is false)
- **Query**: Added `is_delete: 0` term

### 5. Chained when() Methods ✅
- **Input**: Multiple chained `when()` calls
- **Result**: Only truthy conditions executed
- **Query**: Added search and status, skipped null category

### 6. Real Query Execution ✅
- **Result**: Successfully executed against Elasticsearch
- **Performance**: Found 3 records from 458,339 total hits in 401ms

## 🎯 Benefits

### 1. **Cleaner Code**
```php
// Before (verbose if/else)
$query = Tvplus::query()->where('object_id', '11440');
if ($searchTerm) {
    $query->search($searchTerm);
}
if ($status) {
    $query->where('status', $status);
}
$results = $query->get();

// After (clean and readable)
$results = Tvplus::query()
    ->where('object_id', '11440')
    ->when($searchTerm, fn($q, $term) => $q->search($term))
    ->when($status, fn($q, $status) => $q->where('status', $status))
    ->get();
```

### 2. **Method Chaining**
- Maintains fluent interface
- No breaking of query builder chain
- Consistent with Laravel Eloquent patterns

### 3. **Conditional Logic**
- `when()` - Execute if condition is truthy
- `unless()` - Execute if condition is falsy
- Both support default/else callbacks

### 4. **Type Safety**
- Callback receives the condition value as second parameter
- Always returns query builder for continued chaining

## 📊 Performance Impact

- **Zero overhead** when conditions are false
- **Minimal overhead** when conditions are true
- **Same performance** as manual if/else statements
- **Better readability** without performance cost

## 🔄 API Compatibility

### Instance Methods
```php
$query = Tvplus::query()
    ->when($condition, $callback, $default)
    ->unless($condition, $callback, $default);
```

### Static Methods
```php
$query = Tvplus::when($condition, $callback, $default)
    ->unless($condition, $callback, $default);
```

## 📝 Method Signatures

```php
public function when($value, callable $callback, callable $default = null)
public function unless($value, callable $callback, callable $default = null)
public static function when($value, callable $callback, callable $default = null)
public static function unless($value, callable $callback, callable $default = null)
```

## 🎉 Summary

The `when` and `unless` methods have been successfully implemented and tested:

- ✅ **Fully functional** with Elasticsearch 2.4.6
- ✅ **Laravel Eloquent compatible** syntax
- ✅ **Comprehensive test coverage**
- ✅ **Real-world tested** with your data
- ✅ **Performance optimized**
- ✅ **Well documented** with examples

Your Elasticsearch package now supports modern conditional query building patterns, making your code cleaner and more maintainable! 🚀

## 🔗 Related Features

This complements the existing features:
- ✅ Scroll and chunk processing
- ✅ ES 2.4.6 compatibility
- ✅ Eloquent-like API
- ✅ Query debugging (`toQuery()`, `dd()`)
- ✅ Conditional queries (`when()`, `unless()`)

Your Elasticsearch package is now feature-complete for modern Laravel development! 🎯
