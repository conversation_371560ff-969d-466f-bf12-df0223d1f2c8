2025-03-28 07:55:04,579 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2025-03-28 07:55:04,582 INFO supervisord started with pid 9
2025-03-28 07:55:05,586 INFO spawned: 'laravel-scheduler' with pid 13
2025-03-28 07:55:05,591 INFO spawned: 'laravel-worker_00' with pid 14
2025-03-28 07:55:06,298 WARN exited: laravel-worker_00 (exit status 1; not expected)
2025-03-28 07:55:07,300 INFO success: laravel-scheduler entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-03-28 07:55:07,302 INFO spawned: 'laravel-worker_00' with pid 21
2025-03-28 07:55:07,631 WARN exited: laravel-worker_00 (exit status 1; not expected)
2025-03-28 07:55:09,636 INFO spawned: 'laravel-worker_00' with pid 26
2025-03-28 07:55:10,116 WARN exited: laravel-worker_00 (exit status 1; not expected)
2025-03-28 07:55:13,124 INFO spawned: 'laravel-worker_00' with pid 31
2025-03-28 07:55:13,565 WARN exited: laravel-worker_00 (exit status 1; not expected)
2025-03-28 07:55:14,567 INFO gave up: laravel-worker_00 entered FATAL state, too many start retries too quickly
