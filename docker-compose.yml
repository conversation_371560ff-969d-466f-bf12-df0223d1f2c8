services:
    app:
        build: .
        container_name: laravel_app
        restart: unless-stopped
        working_dir: /var/www
        volumes:
            - .:/var/www
        networks:
            - laravel
    nginx:
        image: nginx:latest
        container_name: laravel_nginx
        restart: unless-stopped
        ports:
            - "8080:80"
        volumes:
            - .:/var/www
            - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
        depends_on:
            - app
        networks:
            - laravel

    mariadb:
        image: mariadb:latest
        container_name: laravel_mariadb
        restart: unless-stopped
        ports:
            - "3309:3306"
        environment:
            MYSQL_ROOT_PASSWORD: Pewpew@11
            MYSQL_DATABASE: n8n
            MYSQL_USER: root
            MYSQL_PASSWORD: Pewpew@11
        volumes:
            - mariadb_data:/var/lib/mysql
        networks:
            - laravel

networks:
    laravel:
        driver: bridge

volumes:
    mariadb_data:
