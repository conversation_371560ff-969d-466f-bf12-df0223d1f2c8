<?php

// Debug script to test Elasticsearch queries
require_once 'vendor/autoload.php';

use App\Models\ES\Tvplus;

// Test the query that's causing issues
echo "=== Testing Elasticsearch Query ===\n\n";

try {
    // First, let's see what query is being generated
    echo "1. Generated Query:\n";
    $query = Tvplus::query()
        ->where('object_id', '11440')
        ->where('brand_id', '10586')
        ->whereIn('sub_brand_service_id', [41797, 41796, 41795])
        ->toQuery();
    
    echo json_encode($query, JSON_PRETTY_PRINT) . "\n\n";

    // Test a simple query first
    echo "2. Testing simple count:\n";
    $count = Tvplus::count();
    echo "Total records: {$count}\n\n";

    // Test the specific query with regular get
    echo "3. Testing specific query with get():\n";
    $results = Tvplus::query()
        ->where('object_id', '11440')
        ->where('brand_id', '10586')
        ->whereIn('sub_brand_service_id', [41797, 41796, 41795])
        ->limit(5)
        ->get();
    
    echo "Found " . $results->count() . " records\n\n";

    // Test scroll chunk with smaller batch
    echo "4. Testing scrollChunk with small batch:\n";
    $processed = Tvplus::query()
        ->where('object_id', '11440')
        ->where('brand_id', '10586')
        ->whereIn('sub_brand_service_id', [41797, 41796, 41795])
        ->scrollChunk(10, function ($data, $page) {
            echo "Page {$page}: " . $data->count() . " records\n";
            if ($page >= 2) return false; // Stop after 2 pages
        });
    
    echo "Total processed: {$processed}\n";

} catch (\Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";
