@extends('fe.layouts.app')

@section('content')

    <!-- page title area start -->
    <section class="section-news-menu page__title p-relative pt-140 pb-140 p-relative"
             style="background-image: url('{{asset(@getConfig('banner_news'))?? ''}}');">
        <div class="container">
            <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12">
                    <div class="page__title-inner text-center">
                        <div class="page__title-breadcrumb">
                            <h1>{{__('messages.news')}}</h1>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item"><a
                                            href="{{ LaravelLocalization::localizeUrl(@route('home'))}}">{{__('messages.home')}}</a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">{{__('messages.news')}}</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <form method="get">
            <div class="form-search p-relative">
                <input type="text" name="q" value="{{get_search_param()}}" placeholder="{{__('messages.search_placeholder')}}">
                <button><i class="fa fa-search"></i></button>
            </div>
        </form>
    </section>
    <!-- page title area end -->
    <!-- section area end -->
    <section class="section-posts section-padding blog__area">
        <div class="container">
            <div class="row row-lg-reverse">
                <!-- empty($categoriesNews->toArray()) && -->
                @php
                    $isFullWidth = empty($banner_advertisements->toArray());
                @endphp
                <div class="{{ $isFullWidth ? 'col-lg-12' : 'col-lg-9' }}">
                    <div class="blog__wrapper">
                        <div class="row g-4">
                            @if($featurePost)
                                @if(!request('q'))
                                    <div class="col-md-12">
                                        <div class="item-post-4 p-relative">
                                            <div class="img-post">
                                                <a class="image" href="{{LaravelLocalization::localizeUrl(@$featurePost['slug'])}}">
                                                    @if(empty(@$featurePost['thumb']))
                                                        <img src="{{asset(@$featurePost['thumb'])}}" alt="{{@$featurePost['title']}}">
                                                    @else
                                                        <img src="{{asset('frontend/assets/img/news-default.jfif')}}" alt="">
                                                    @endif
                                                </a>
                                            </div>
                                            <div class="content">
                                                <div class="calendar">
                                                    <i class="far fa-calendar-day"></i>
                                                    <span>{{@$featurePost['published_at']}}</span>
                                                </div>
                                                <div class="lb-highlight">
                                                    <h3>{{ __('messages.featured') }}</h3>
                                                </div>
                                                <div class="title">
                                                    <h3>
                                                        <a href="{{LaravelLocalization::localizeUrl(@$featurePost['slug'])}}">
                                                            {{@$featurePost['title']}}
                                                        </a>
                                                    </h3>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @foreach ($newestPosts as $post)
                                        <div class="col-md-6" id="target-section">
                                            <div class="item-post-2">
                                                <div class="img-post">
                                                    <a class="image" href="{{ LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                        @if(!empty(@$post['thumb']))
                                                            <img src="{{ asset(@$post['thumb'])}}">
                                                        @else
                                                            <img src="{{asset('frontend/assets/img/news-default.jfif')}}" alt="">
                                                        @endif
                                                    </a>
                                                </div>
                                                <div class="content">
                                                    <div class="calendar">
                                                        <i class="far fa-calendar-day"></i>
                                                        <span> {{@$post['published_at']}} - </span>
                                                        <span class="post-highlight">{{ __('messages.latest') }}</span>
                                                    </div>
                                                    <div class="title">
                                                        <h3>
                                                            <a href="{{ LaravelLocalization::localizeUrl(@$post['slug']) }}">
                                                                {{ @$post['title'] }}
                                                            </a>
                                                        </h3>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                                @foreach ($newsPosts as $post)
                                    <div class="col-md-6">
                                        @if(isset($post['file']) )
                                            <div class="item-post">
                                                <div class="img-post">
                                                    <a class="image" href="{{ LaravelLocalization::localizeUrl(@$post['slug']) }}">
                                                        @if(preg_match('/\.docx$/i', @$post['file']))
                                                            <img src="{{ asset('frontend/assets/img/icon/doc02.png') }}">
                                                        @elseif(preg_match('/\.pdf$/i', @$post['file']))
                                                            <img src="{{ asset('frontend/assets/img/icon/pdf02.png') }}">
                                                        @endif
                                                    </a>
                                                </div>
                                                <div class="content">
                                                    <div class="title">
                                                        <h3>
                                                            <a href="{{ LaravelLocalization::localizeUrl(@$post['slug']) }}">
                                                                {{@$post['title']}}
                                                            </a>
                                                        </h3>
                                                    </div>
                                                </div>
                                            </div>
                                        @else
                                            <div class="item-post-3">
                                                <div class="img-post">
                                                    @if(!empty(@$post['thumb']))
                                                        <a class="image" href="{{ LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                            <img src="{{ asset(@$post['thumb'])}}">
                                                        </a>
                                                    @else
                                                        <a class="image" href="{{ LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                            <img src="{{asset('frontend/assets/img/news-default.jfif')}}" alt="">
                                                        </a>
                                                    @endif
                                                </div>
                                                <div class="content">
                                                    <div class="calendar">
                                                        <i class="far fa-calendar-day"></i>
                                                        <span>{{@$post['published_at']}}</span>
                                                    </div>
                                                    <div class="title">
                                                        <h3>
                                                            <a href="{{ LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                                {{@$post['title']}}
                                                            </a>
                                                        </h3>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            @else
                                @if(!request('q'))
                                    @foreach ($newestPosts->slice(0, 1) as $post)
                                        <div class="col-md-12">
                                            <div class="item-post-4 p-relative">
                                                <div class="img-post">
                                                    <a class="image" href="{{LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                        @if(!empty(@$post['thumb']))
                                                            <img src="{{ asset(@$post['thumb'])}}">
                                                        @else
                                                            <img src="{{asset('frontend/assets/img/news-default.jfif')}}" alt="">
                                                        @endif
                                                    </a>
                                                </div>
                                                <div class="content">
                                                    <div class="calendar">
                                                        <i class="far fa-calendar-day"></i>
                                                        <span>{{@$post['published_at']}}</span>
                                                    </div>
                                                    <div class="lb-highlight">
                                                        <h3>{{ __('messages.latest') }}</h3>
                                                    </div>
                                                    <div class="title">
                                                        <h3>
                                                            <a href="{{LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                                {{@$post['title']}}
                                                            </a>
                                                        </h3>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                    @foreach ($newestPosts->slice(1, 3) as $post)
                                        <div class="col-md-6" id="target-section">
                                            <div class="item-post-2">
                                                <div class="img-post">
                                                    <a class="image" href="{{ LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                        @if(!empty(@$post['thumb']))
                                                            <img src="{{ asset(@$post['thumb'])}}">
                                                        @else
                                                            <img src="{{asset('frontend/assets/img/news-default.jfif')}}" alt="">
                                                        @endif
                                                    </a>
                                                </div>
                                                <div class="content">
                                                    <div class="calendar">
                                                        <i class="far fa-calendar-day"></i>
                                                        <span> {{@$post['published_at']}} - </span>
                                                        <span class="post-highlight">{{ __('messages.latest') }}</span>
                                                    </div>
                                                    <div class="title">
                                                        <h3>
                                                            <a href="{{ LaravelLocalization::localizeUrl(@$post['slug']) }}">
                                                                {{ @$post['title'] }}
                                                            </a>
                                                        </h3>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                                @foreach ($newsPosts as $post)
                                    <div class="col-md-6">
                                        @if(isset($post['file']))
                                            <div class="item-post">
                                                <div class="img-post">
                                                    <a class="image" href="{{ LaravelLocalization::localizeUrl(@$post['slug']) }}">
                                                        @if(preg_match('/\.docx$/i', @$post['file']))
                                                            <img src="{{ asset('frontend/assets/img/icon/doc02.png') }}">
                                                        @elseif(preg_match('/\.pdf$/i', @$post['file']))
                                                            <img src="{{ asset('frontend/assets/img/icon/pdf02.png') }}">
                                                        @endif
                                                    </a>
                                                </div>
                                                <div class="content">
                                                    <div class="title">
                                                        <h3>
                                                            <a href="{{ LaravelLocalization::localizeUrl(@$post['slug']) }}">
                                                                {{@$post['title']}}
                                                            </a>
                                                        </h3>
                                                    </div>
                                                </div>
                                            </div>
                                        @else
                                            <div class="item-post-3">
                                                <div class="img-post">
                                                    @if(!empty(@$post['thumb']))
                                                        <a class="image" href="{{ LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                            <img src="{{ asset(@$post['thumb'])}}">
                                                        </a>
                                                    @else
                                                        <a class="image" href="{{ LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                            <img src="{{asset('frontend/assets/img/news-default.jfif')}}" alt="">
                                                        </a>
                                                    @endif
                                                </div>
                                                <div class="content">
                                                    <div class="calendar">
                                                        <i class="far fa-calendar-day"></i>
                                                        <span>{{@$post['published_at']}}</span>
                                                    </div>
                                                    <div class="title">
                                                        <h3>
                                                            <a href="{{ LaravelLocalization::localizeUrl(@$post['slug'])}}">
                                                                {{@$post['title']}}
                                                            </a>
                                                        </h3>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                    {{ $newsPosts->links('fe.layouts.pagination') }}
                    {{-- <div class="widget sidebar-wrapper mb-40 mt-5">--}}
                    {{-- <div class="title mb-25">--}}
                    {{-- <h3>Tags</h3>--}}
                    {{-- </div>--}}
                    {{-- <div class="tag">--}}
                    {{-- @foreach($tagALL as $tag)--}}
                    {{-- <a href="{{ LaravelLocalization::localizeUrl(@$tag['slug']) }}">{{ @$tag['title']
                        }}</a>--}}
                    {{-- @endforeach--}}
                    {{-- </div>--}}
                    {{-- </div>--}}
                </div>
                <div class="col-lg-3">
                    <!-- @if (!empty($categoriesNews->toArray()))
                        <div class="blog__sidebar-wrapper">
                            <div class="widget sidebar-wrapper mb-40">
                                <div class="title mb-25">
                                    <h3>{{__('messages.news')}}</h3>
                                </div>
                                <div class="sidebar-list">
                                    <ul>
                                        @foreach($categoriesNews as $post)
                            <li>
                                <a
                                    href="{{ LaravelLocalization::localizeUrl(@$post['slug']) }}">{{@$post['title']}}</a>
                                            </li>
                                        @endforeach
                        </ul>
                    </div>
                </div>
            </div>
@endif -->
                    <div class="mb-40 p-0">
                        <div class="banner-widget w-img">
                            @foreach($banner_advertisements as $banner)
                                @foreach($banner as $item)
                                    <a href="{{ @$item['link'] }}" target="_blank">
                                        <img src="{{ asset(@$item['banner']) }}" alt="banner" class="mb-4">
                                    </a>
                                @endforeach
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--news area start-->

@endsection
