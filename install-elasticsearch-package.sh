#!/bin/bash

echo "🚀 Installing Custom Elasticsearch Package for Laravel..."
echo ""

# Step 1: Remove old package if exists
echo "📦 Removing old Elasticsearch package..."
if composer show pdphilip/elasticsearch > /dev/null 2>&1; then
    composer remove pdphilip/elasticsearch
    echo "✅ Old package removed"
else
    echo "ℹ️  No old package found"
fi

# Step 2: Install dependencies
echo ""
echo "📦 Installing required dependencies..."
composer require guzzlehttp/guzzle

# Step 3: Update composer autoload
echo ""
echo "🔄 Updating composer autoload..."
composer dump-autoload

# Step 4: Install the local package
echo ""
echo "📦 Installing local Elasticsearch package..."
composer install

# Step 5: Check if service provider is registered
echo ""
echo "🔧 Checking service provider registration..."
if grep -q "Lib\\\\Elasticsearch\\\\ElasticsearchServiceProvider" config/app.php; then
    echo "✅ Service provider already registered"
else
    echo "⚠️  Please add the service provider to config/app.php:"
    echo "   'providers' => ["
    echo "       // Other providers..."
    echo "       Lib\\Elasticsearch\\ElasticsearchServiceProvider::class,"
    echo "   ],"
fi

# Step 6: Test connection
echo ""
echo "🔍 Testing Elasticsearch connection..."
if php artisan elasticsearch:manage info > /dev/null 2>&1; then
    echo "✅ Elasticsearch connection successful!"
else
    echo "⚠️  Could not connect to Elasticsearch. Please check your configuration."
    echo "   Make sure Elasticsearch 2.4.6 is running and check your .env file:"
    echo "   ES_HOSTS=http://localhost:9200"
fi

echo ""
echo "🎉 Installation complete!"
echo ""
echo "📚 Next steps:"
echo "1. Update your ES models to extend Lib\\Elasticsearch\\Model"
echo "2. Check the documentation in lib/Elasticsearch/README.md"
echo "3. See examples in lib/Elasticsearch/Examples/"
echo ""
echo "🛠️  Available commands:"
echo "   php artisan elasticsearch:manage info"
echo "   php artisan elasticsearch:manage list-indices"
echo "   php artisan elasticsearch:manage create-index --index=my_index"
echo ""
echo "Happy coding! 🚀"
