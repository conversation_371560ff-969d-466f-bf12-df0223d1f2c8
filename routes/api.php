<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::prefix('/page')->middleware(['page_content'])->group(function () {
    Route::get('/get', [\App\Http\Controllers\Api\PageContentController::class, 'getContent']);
    Route::post('/update/{id}', [\App\Http\Controllers\Api\PageContentController::class, 'updatePost']);
    Route::post('/cal-price/{id}', [\App\Http\Controllers\Api\PageContentController::class, 'calPrice']);
});
