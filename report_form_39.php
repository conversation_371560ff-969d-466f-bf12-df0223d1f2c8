<?php
ini_set('memory_limit', -1);
ini_set('max_execution_time', 0);
include_once('header.php');
include_once(APP_PATH . 'helper/dailyReport_functions.php');

require_once(APP_PATH . 'library/PHPExcel/PHPExcel.php');
require_once(APP_PATH . 'library/QuickChart.php');
require_once APP_PATH . "library/smtpmailer2.php";




class ReportForm39
{
    public function run($setting)
    {
        $begin_time = date("Y-m-d H:i:s");
        $timeStart = time();
        // echo "<pre> FORM 3\n";
        // print_r($setting);exit;
        $um = new HbaseModel();
        $chatID = '-*********';
        $token = 'bot5696362888:AAEo3FccSicN9q7Xc4Jcr20m1QUZXZeKWKg';

        // User Quick Chart
        $apiKeyQuickChart = 'q-hrcceah0ie6s4bbn9no37w24h139su6k';
        $accountID = 4195010;

        $list_object = [];

        $setting_id = $setting['id'];
        $start_date = $setting['start_date'];
        $end_date = $setting['end_date'];
        // echo $start_date, $end_date; exit;
        $date_report = $setting['date_report'];
        $brand_id = $setting['branch_id'];
        $brand_name = $setting['brand_name'];
        $object_id = $setting['list_subbrand'];
        $is_subbrand = true;
        $data_color = $setting['color'];
        $has_sheet_detail = $setting['detail'];
        $has_send_mail = $setting['has_send_mail'];
        $list_mail = $setting['list_mail'];
        $saved_folder = $setting['folder'];

        $channel_text = getChannelText('vi');
        $state_text = getStateText('vi');
        $state_text_text = getStateTextTrans();
        $domain_zalo_url = "beta-zalo.monitaz.com";

        $report_type_text = ucwords($setting['type']);
        $report_type_text_text = getReportTypeTextTrans($setting['type']);

        $active_channels = $setting['active_channels'];
        $limit_top_reaction = $setting['limit_top_reaction'];
        $limit_top_negative = $setting['limit_top_negative'];
        $data_check = $setting['data_check'];

        if ($setting_id == 143) {
            $typeReport = ' AM';
        } elseif ($setting_id == 68 || $setting_id == 146) {
            $typeReport = ' PM';
        } elseif ($setting_id == 144) {
            $typeReport = ' NOON';
        } else {
            $typeReport = '';
        }

        // print_r($object_id); exit;
        $has_sub_enemy = false;
        foreach ($object_id as $sub_brand_object) {
            $obj_arr = [];
            if (count($sub_brand_object) > 1) $has_sub_enemy = true;
            foreach ($sub_brand_object as $item) {
                $obj_arr[] = $um->getSubBrandById($item);
            }
            $list_object[] = $obj_arr;
        }

        // print_r(json_encode($list_object)); exit;
        $outputFile = $saved_folder . '/' . $brand_name . ' - ' . $report_type_text . ' Report ' . str_replace("/", ".", $date_report) . $typeReport . '.xlsx';
        $pathToOutputFile = PUBLIC_PATH . $outputFile;

        $outputFileJson = $saved_folder . '/' . $brand_name . ' - ' . $report_type_text . ' Report ' . str_replace("/", ".", $date_report) . $typeReport . '.json';
        $pathFileJson = PUBLIC_PATH . $outputFileJson;


        // print_r($pathToOutputFile); exit;

        // print_r($active_channels); exit;
        /* JSON QUERY & DATA */
        $get_data_query = function ($active_channels, $brand_id, $object_id, $start_date, $end_date, $data_check) use ($is_subbrand) {
            if ($active_channels['Facebook']) {
                $is_checked = isset($data_check['Facebook']) ? $data_check['Facebook']->is_checked : 0;
                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                $json_doc_social_neutral = __generateJsonSocial($brand_id, [$object_id], $start_date, $end_date, $state = 0, null, 'top_reaction', 50, $is_checked, $is_read, $is_subbrand);
                $data_query_social_neutral = _searchSocial($json_doc_social_neutral);
                $json_doc_social_positive = __generateJsonSocial($brand_id, [$object_id], $start_date, $end_date, $state = 1, null, 'top_reaction', 50, $is_checked, $is_read, $is_subbrand);
                $data_query_social_positive = _searchSocial($json_doc_social_positive);
                $json_doc_social_negative = __generateJsonSocial($brand_id, [$object_id], $start_date, $end_date, $state = 2, null, 'top_reaction', 50, $is_checked, $is_read, $is_subbrand);
                $data_query_social_negative = _searchSocial($json_doc_social_negative);
                $json_doc_facebook = __generateJsonSocial($brand_id, [$object_id], $start_date, $end_date, '', null, 'top_reaction', 50, $is_checked, $is_read, $is_subbrand);
                $data_query_facebook = _searchSocial($json_doc_facebook);
            }

            if ($active_channels['Youtube']) {
                $is_checked = isset($data_check['Youtube']) ? $data_check['Youtube']->is_checked : 0;
                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                $json_doc_youtube_neutral = __generateJsonYoutube($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_youtube_neutral = _searchYoutube($json_doc_youtube_neutral);
                $json_doc_youtube_positive = __generateJsonYoutube($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_youtube_positive = _searchYoutube($json_doc_youtube_positive);
                $json_doc_youtube_negative = __generateJsonYoutube($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_youtube_negative = _searchYoutube($json_doc_youtube_negative);
                $json_doc_youtube = __generateJsonYoutube($brand_id, [$object_id], $start_date, $end_date, '', 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_youtube = _searchYoutube($json_doc_youtube);
            }

            if ($active_channels['Ifollow']) {
                $is_checked = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_checked : 0;
                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                $json_doc_ifollow_neutral = __generateJsonIfollow($brand_id, [$object_id], $start_date, $end_date, '', 0, null, '', 50, $is_checked, $is_read, $is_subbrand);
                $data_query_ifollow_neutral = _searchIfollow($json_doc_ifollow_neutral);
                $json_doc_ifollow_positive = __generateJsonIfollow($brand_id, [$object_id], $start_date, $end_date, '', 1, null, '', 50, $is_checked, $is_read, $is_subbrand);
                $data_query_ifollow_positive = _searchIfollow($json_doc_ifollow_positive);
                $json_doc_ifollow_negative = __generateJsonIfollow($brand_id, [$object_id], $start_date, $end_date, '', 2, null, '', 50, $is_checked, $is_read, $is_subbrand);
                $data_query_ifollow_negative = _searchIfollow($json_doc_ifollow_negative);
                $json_doc_ifollow = __generateJsonIfollow($brand_id, [$object_id], $start_date, $end_date, '', '', null, '', 50, $is_checked, $is_read, $is_subbrand);
                $data_query_ifollow = _searchIfollow($json_doc_ifollow);
            }

            if ($active_channels['Tiktok']) {
                $is_checked = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_checked : 0;
                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                $json_doc_tiktok_neutral = __generateJsonTiktok($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_tiktok_neutral = _searchTiktok($json_doc_tiktok_neutral);
                $json_doc_tiktok_positive = __generateJsonTiktok($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_tiktok_positive = _searchTiktok($json_doc_tiktok_positive);
                $json_doc_tiktok_negative = __generateJsonTiktok($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_tiktok_negative = _searchTiktok($json_doc_tiktok_negative);
                $json_doc_tiktok = __generateJsonTiktok($brand_id, [$object_id], $start_date, $end_date, '', 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_tiktok = _searchTiktok($json_doc_tiktok);
            }

            if ($active_channels['Twitter']) {
                $is_checked = isset($data_check['Twitter']) ? $data_check['Twitter']->is_checked : 0;
                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                $json_doc_twitter_neutral = __generateJsonTwitter($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_twitter_neutral = _searchTwitter($json_doc_twitter_neutral);
                $json_doc_twitter_positive = __generateJsonTwitter($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_twitter_positive = _searchTwitter($json_doc_twitter_positive);
                $json_doc_twitter_negative = __generateJsonTwitter($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_twitter_negative = _searchTwitter($json_doc_twitter_negative);
                $json_doc_twitter = __generateJsonTwitter($brand_id, [$object_id], $start_date, $end_date, '', 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_twitter = _searchTwitter($json_doc_twitter);
            }

            if ($active_channels['Instagram']) {
                $is_checked = isset($data_check['Instagram']) ? $data_check['Instagram']->is_checked : 0;
                $json_doc_instagram_neutral = __generateJsonTwitter($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked);
                $data_query_instagram_neutral = _searchTwitter($json_doc_instagram_neutral);
                $json_doc_instagram_positive = __generateJsonTwitter($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked);
                $data_query_instagram_positive = _searchTwitter($json_doc_instagram_positive);
                $json_doc_instagram_negative = __generateJsonTwitter($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked);
                $data_query_instagram_negative = _searchTwitter($json_doc_instagram_negative);
                $json_doc_instagram = __generateJsonTwitter($brand_id, [$object_id], $start_date, $end_date, '', 50, 'top_reaction', $is_checked);
                $data_query_instagram = _searchTwitter($json_doc_instagram);
            }

            if ($active_channels['Zalo']) {
                $is_checked = 0;
                $json_doc_zalo_neutral = __generateJsonZalo($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_zalo_neutral = _searchZalo($json_doc_zalo_neutral);
                $json_doc_zalo_positive = __generateJsonZalo($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_zalo_positive = _searchZalo($json_doc_zalo_positive);
                $json_doc_zalo_negative = __generateJsonZalo($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_zalo_negative = _searchZalo($json_doc_zalo_negative);
                $json_doc_zalo = __generateJsonZalo($brand_id, [$object_id], $start_date, $end_date, '', 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_zalo = _searchZalo($json_doc_zalo);
            }

            if ($active_channels['Reviewapp']) {
                $is_checked = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_checked : 0;
                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                $json_doc_reviewapp_neutral = __generateJsonReviewapp($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_reviewapp_neutral = _searchReviewapp($json_doc_reviewapp_neutral);
                $json_doc_reviewapp_positive = __generateJsonReviewapp($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_reviewapp_positive = _searchReviewapp($json_doc_reviewapp_positive);
                $json_doc_reviewapp_negative = __generateJsonReviewapp($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_reviewapp_negative = _searchReviewapp($json_doc_reviewapp_negative);
                $json_doc_reviewapp = __generateJsonReviewapp($brand_id, [$object_id], $start_date, $end_date, '', 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_reviewapp = _searchReviewapp($json_doc_reviewapp);
            }

            if ($active_channels['Reviewmap']) {
                $is_checked = isset($data_check['Reviewmap']) ? $data_check['Reviewmap']->is_checked : 0;
                $json_doc_reviewmap_neutral = __generateJsonReviewmap($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_reviewmap_neutral = _searchReviewmap($json_doc_reviewmap_neutral);
                $json_doc_reviewmap_positive = __generateJsonReviewmap($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_reviewmap_positive = _searchReviewmap($json_doc_reviewmap_positive);
                $json_doc_reviewmap_negative = __generateJsonReviewmap($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_reviewmap_negative = _searchReviewmap($json_doc_reviewmap_negative);
                $json_doc_reviewmap = __generateJsonReviewmap($brand_id, [$object_id], $start_date, $end_date, '0, 1', 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_reviewmap = _searchReviewmap($json_doc_reviewmap);
            }

            if ($active_channels['Ecommerce']) {
                $is_checked = isset($data_check['Ecommerce']) ? $data_check['Ecommerce']->is_checked : 0;
                $json_doc_ecommerce_neutral = __generateJsonEcommerce($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, '', $is_checked, null, $is_subbrand);
                $data_query_ecommerce_neutral = _searchEcommerce($json_doc_ecommerce_neutral);
                $json_doc_ecommerce_positive = __generateJsonEcommerce($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, '', $is_checked, null, $is_subbrand);
                $data_query_ecommerce_positive = _searchEcommerce($json_doc_ecommerce_positive);
                $json_doc_ecommerce_negative = __generateJsonEcommerce($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, '', $is_checked, null, $is_subbrand);
                $data_query_ecommerce_negative = _searchEcommerce($json_doc_ecommerce_negative);
                $json_doc_ecommerce = __generateJsonEcommerce($brand_id, [$object_id], $start_date, $end_date, '', 50, '', $is_checked, null, $is_subbrand);
                $data_query_ecommerce = _searchEcommerce($json_doc_ecommerce);
            }

            if ($active_channels['Threads']) {
                $is_checked = isset($data_check['Threads']) ? $data_check['Threads']->is_checked : 0;
                $json_doc_threads_neutral = __generateJsonThreads($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_threads_neutral = _searchThreads($json_doc_threads_neutral);
                $json_doc_threads_positive = __generateJsonThreads($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_threads_positive = _searchThreads($json_doc_threads_positive);
                $json_doc_threads_negative = __generateJsonThreads($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_threads_negative = _searchThreads($json_doc_threads_negative);
                $json_doc_threads = __generateJsonThreads($brand_id, [$object_id], $start_date, $end_date, '', 50, 'top_reaction', $is_checked, null, $is_subbrand);
                $data_query_threads = _searchThreads($json_doc_threads);
            }

            if ($active_channels['Paper']) {
                $is_checked = isset($data_check['Paper']) ? $data_check['Paper']->is_checked : 0;
                $json_doc_paper_neutral = __generateJsonPaper($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, '', $is_checked, null, $is_subbrand);
                $data_query_paper_neutral = _searchPaper($json_doc_paper_neutral);
                $json_doc_paper_positive = __generateJsonPaper($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, '', $is_checked, null, $is_subbrand);
                $data_query_paper_positive = _searchPaper($json_doc_paper_positive);
                $json_doc_paper_negative = __generateJsonPaper($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, '', $is_checked, null, $is_subbrand);
                $data_query_paper_negative = _searchPaper($json_doc_paper_negative);
                $json_doc_paper = __generateJsonPaper($brand_id, [$object_id], $start_date, $end_date, '', 50, '', $is_checked, null, $is_subbrand);
                $data_query_paper = _searchPaper($json_doc_paper);
            }

            if ($active_channels['Tv']) {
                $is_checked = isset($data_check['Tv']) ? $data_check['Tv']->is_checked : 0;
                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                $json_doc_tv_neutral = __generateJsonTv($brand_id, [$object_id], $start_date, $end_date, $state = 0, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_tv_neutral = _searchTv($json_doc_tv_neutral);
                $json_doc_tv_positive = __generateJsonTv($brand_id, [$object_id], $start_date, $end_date, $state = 1, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_tv_positive = _searchTv($json_doc_tv_positive);
                $json_doc_tv_negative = __generateJsonTv($brand_id, [$object_id], $start_date, $end_date, $state = 2, 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_tv_negative = _searchTv($json_doc_tv_negative);
                $json_doc_tv = __generateJsonTv($brand_id, [$object_id], $start_date, $end_date, '', 50, 'top_reaction', $is_checked, $is_read, $is_subbrand);
                $data_query_tv = _searchTv($json_doc_tv);
            }

            return [
                'object_id' => $object_id,
                'neutral' => [
                    'Facebook' => $active_channels['Facebook'] ? $data_query_social_neutral : null,
                    'Youtube' => $active_channels['Youtube'] ? $data_query_youtube_neutral : null,
                    'Ifollow' => $active_channels['Ifollow'] ? $data_query_ifollow_neutral : null,
                    'Instagram' => $active_channels['Instagram'] ? $data_query_instagram_neutral : null,
                    'Zalo' => $active_channels['Zalo'] ? $data_query_zalo_neutral : null,
                    'Tiktok' => $active_channels['Tiktok'] ? $data_query_tiktok_neutral : null,
                    'Twitter' => $active_channels['Twitter'] ? $data_query_twitter_neutral : null,
                    'Reviewapp' => $active_channels['Reviewapp'] ? $data_query_reviewapp_neutral : null,
                    'Reviewmap' => $active_channels['Reviewmap'] ? $data_query_reviewmap_neutral : null,
                    'Ecommerce' => $active_channels['Ecommerce'] ? $data_query_ecommerce_neutral : null,
                    'Threads' => $active_channels['Threads'] ? $data_query_threads_neutral : null,
                    'Paper' => $active_channels['Paper'] ? $data_query_paper_neutral : null,
                    'Tv' => $active_channels['Tv'] ? $data_query_tv_neutral : null,
                ],
                'positive' => [
                    'Facebook' => $active_channels['Facebook'] ? $data_query_social_positive : null,
                    'Youtube' => $active_channels['Youtube'] ? $data_query_youtube_positive : null,
                    'Ifollow' => $active_channels['Ifollow'] ? $data_query_ifollow_positive : null,
                    'Instagram' => $active_channels['Instagram'] ? $data_query_instagram_positive : null,
                    'Zalo' => $active_channels['Zalo'] ? $data_query_zalo_positive : null,
                    'Tiktok' => $active_channels['Tiktok'] ? $data_query_tiktok_positive : null,
                    'Twitter' => $active_channels['Twitter'] ? $data_query_twitter_positive : null,
                    'Reviewapp' => $active_channels['Reviewapp'] ? $data_query_reviewapp_positive : null,
                    'Reviewmap' => $active_channels['Reviewmap'] ? $data_query_reviewmap_positive : null,
                    'Ecommerce' => $active_channels['Ecommerce'] ? $data_query_ecommerce_positive : null,
                    'Threads' => $active_channels['Threads'] ? $data_query_threads_positive : null,
                    'Paper' => $active_channels['Paper'] ? $data_query_paper_positive : null,
                    'Tv' => $active_channels['Tv'] ? $data_query_tv_positive : null,
                ],
                'negative' => [
                    'Facebook' => $active_channels['Facebook'] ? $data_query_social_negative : null,
                    'Youtube' => $active_channels['Youtube'] ? $data_query_youtube_negative : null,
                    'Ifollow' => $active_channels['Ifollow'] ? $data_query_ifollow_negative : null,
                    'Instagram' => $active_channels['Instagram'] ? $data_query_instagram_negative : null,
                    'Zalo' => $active_channels['Zalo'] ? $data_query_zalo_negative : null,
                    'Tiktok' => $active_channels['Tiktok'] ? $data_query_tiktok_negative : null,
                    'Twitter' => $active_channels['Twitter'] ? $data_query_twitter_negative : null,
                    'Reviewapp' => $active_channels['Reviewapp'] ? $data_query_reviewapp_negative : null,
                    'Reviewmap' => $active_channels['Reviewmap'] ? $data_query_reviewmap_negative : null,
                    'Ecommerce' => $active_channels['Ecommerce'] ? $data_query_ecommerce_negative : null,
                    'Threads' => $active_channels['Threads'] ? $data_query_threads_negative : null,
                    'Paper' => $active_channels['Paper'] ? $data_query_paper_negative : null,
                    'Tv' => $active_channels['Tv'] ? $data_query_tv_negative : null,
                ],
                'channel' => [
                    'Facebook' => $active_channels['Facebook'] ? $data_query_facebook : null,
                    'Youtube' => $active_channels['Youtube'] ? $data_query_youtube : null,
                    'Ifollow' => $active_channels['Ifollow'] ? $data_query_ifollow : null,
                    'Instagram' => $active_channels['Instagram'] ? $data_query_instagram : null,
                    'Zalo' => $active_channels['Zalo'] ? $data_query_zalo : null,
                    'Tiktok' => $active_channels['Tiktok'] ? $data_query_tiktok : null,
                    'Twitter' => $active_channels['Twitter'] ? $data_query_twitter : null,
                    'Reviewapp' => $active_channels['Reviewapp'] ? $data_query_reviewapp : null,
                    'Reviewmap' => $active_channels['Reviewmap'] ? $data_query_reviewmap : null,
                    'Ecommerce' => $active_channels['Ecommerce'] ? $data_query_ecommerce : null,
                    'Threads' => $active_channels['Threads'] ? $data_query_threads : null,
                    'Paper' => $active_channels['Paper'] ? $data_query_paper : null,
                    'Tv' => $active_channels['Tv'] ? $data_query_tv : null,
                ]
            ];
        };

        /* SỐ LƯỢNG CÁC SENTIMENT */
        $calc_sentiment_pecentage = function ($neutral, $positive, $negative) {
            $total_sentiment = $neutral + $positive + $negative;
            $percentage_negative = $total_sentiment ? round(($negative / $total_sentiment) * 100, 2) : 0;
            /*if ($negative && !$percentage_negative) {
                $percentage_negative = 1;
            }*/
            $percentage_positive = $total_sentiment ? round(($positive / $total_sentiment) * 100, 2) : 0;
            /*if ($positive && !$percentage_positive) {
                $percentage_positive = 1;
            }*/
            $percentage_neutral = $total_sentiment ? round(100 - $percentage_negative - $percentage_positive, 2) : 0;
            return ['neutral' => $percentage_neutral, 'positive' => $percentage_positive, 'negative' => $percentage_negative];
        };

        $get_sentiment = function ($data_query) {
            $quantity_social_neutral = $data_query['neutral']['Facebook'] ? $data_query['neutral']['Facebook']->hits->total : 0;
            $quantity_youtube_neutral = $data_query['neutral']['Youtube'] ? $data_query['neutral']['Youtube']->hits->total : 0;
            $quantity_ifollow_neutral = $data_query['neutral']['Ifollow'] ? $data_query['neutral']['Ifollow']->hits->total : 0;
            $quantity_instagram_neutral = $data_query['neutral']['Instagram'] ? $data_query['neutral']['Instagram']->hits->total : 0;
            $quantity_zalo_neutral = $data_query['neutral']['Zalo'] ? $data_query['neutral']['Zalo']->hits->total : 0;
            $quantity_tiktok_neutral = $data_query['neutral']['Tiktok'] ? $data_query['neutral']['Tiktok']->hits->total : 0;
            $quantity_twitter_neutral = $data_query['neutral']['Twitter'] ? $data_query['neutral']['Twitter']->hits->total : 0;
            $quantity_reviewapp_neutral = $data_query['neutral']['Reviewapp'] ? $data_query['neutral']['Reviewapp']->hits->total : 0;
            $quantity_reviewmap_neutral = $data_query['neutral']['Reviewmap'] ? $data_query['neutral']['Reviewmap']->hits->total : 0;
            $quantity_ecommerce_neutral = $data_query['neutral']['Ecommerce'] ? $data_query['neutral']['Ecommerce']->hits->total : 0;
            $quantity_threads_neutral = $data_query['neutral']['Threads'] ? $data_query['neutral']['Threads']->hits->total : 0;
            $quantity_paper_neutral = $data_query['neutral']['Paper'] ? $data_query['neutral']['Paper']->hits->total : 0;
            $quantity_tv_neutral = $data_query['neutral']['Tv'] ? $data_query['neutral']['Tv']->hits->total : 0;
            $total_neutral = $quantity_threads_neutral + $quantity_social_neutral + $quantity_youtube_neutral + $quantity_ifollow_neutral + $quantity_tiktok_neutral + $quantity_twitter_neutral + $quantity_reviewapp_neutral + $quantity_paper_neutral + $quantity_tv_neutral + $quantity_instagram_neutral + $quantity_zalo_neutral + $quantity_ecommerce_neutral;

            $quantity_social_positive = $data_query['positive']['Facebook'] ? $data_query['positive']['Facebook']->hits->total : 0;
            $quantity_youtube_positive = $data_query['positive']['Youtube'] ? $data_query['positive']['Youtube']->hits->total : 0;
            $quantity_ifollow_positive = $data_query['positive']['Ifollow'] ? $data_query['positive']['Ifollow']->hits->total : 0;
            $quantity_instagram_positive = $data_query['positive']['Instagram'] ? $data_query['positive']['Instagram']->hits->total : 0;
            $quantity_zalo_positive = $data_query['positive']['Zalo'] ? $data_query['positive']['Zalo']->hits->total : 0;
            $quantity_tiktok_positive = $data_query['positive']['Tiktok'] ? $data_query['positive']['Tiktok']->hits->total : 0;
            $quantity_twitter_positive = $data_query['positive']['Twitter'] ? $data_query['positive']['Twitter']->hits->total : 0;
            $quantity_reviewapp_positive = $data_query['positive']['Reviewapp'] ? $data_query['positive']['Reviewapp']->hits->total : 0;
            $quantity_reviewmap_positive = $data_query['positive']['Reviewmap'] ? $data_query['positive']['Reviewmap']->hits->total : 0;
            $quantity_ecommerce_positive = $data_query['positive']['Ecommerce'] ? $data_query['positive']['Ecommerce']->hits->total : 0;
            $quantity_threads_positive = $data_query['positive']['Threads'] ? $data_query['positive']['Threads']->hits->total : 0;
            $quantity_paper_positive = $data_query['neutral']['Paper'] ? $data_query['positive']['Paper']->hits->total : 0;
            $quantity_tv_positive = $data_query['neutral']['Tv'] ? $data_query['positive']['Tv']->hits->total : 0;
            $total_positive = $quantity_threads_positive + $quantity_social_positive + $quantity_youtube_positive + $quantity_ifollow_positive + $quantity_tiktok_positive + $quantity_twitter_positive + $quantity_reviewapp_positive + $quantity_paper_positive + $quantity_tv_positive + $quantity_instagram_positive + $quantity_zalo_positive + $quantity_ecommerce_positive;

            $quantity_social_negative = $data_query['negative']['Facebook'] ? $data_query['negative']['Facebook']->hits->total : 0;
            $quantity_youtube_negative = $data_query['negative']['Youtube'] ? $data_query['negative']['Youtube']->hits->total : 0;
            $quantity_ifollow_negative = $data_query['negative']['Ifollow'] ? $data_query['negative']['Ifollow']->hits->total : 0;
            $quantity_instagram_negative = $data_query['negative']['Instagram'] ? $data_query['negative']['Instagram']->hits->total : 0;
            $quantity_zalo_negative = $data_query['negative']['Zalo'] ? $data_query['negative']['Zalo']->hits->total : 0;
            $quantity_tiktok_negative = $data_query['negative']['Tiktok'] ? $data_query['negative']['Tiktok']->hits->total : 0;
            $quantity_twitter_negative = $data_query['negative']['Twitter'] ? $data_query['negative']['Twitter']->hits->total : 0;
            $quantity_reviewapp_negative = $data_query['negative']['Reviewapp'] ? $data_query['negative']['Reviewapp']->hits->total : 0;
            $quantity_reviewmap_negative = $data_query['negative']['Reviewmap'] ? $data_query['negative']['Reviewmap']->hits->total : 0;
            $quantity_ecommerce_negative = $data_query['negative']['Ecommerce'] ? $data_query['negative']['Ecommerce']->hits->total : 0;
            $quantity_threads_negative = $data_query['negative']['Threads'] ? $data_query['negative']['Threads']->hits->total : 0;
            $quantity_paper_negative = $data_query['negative']['Paper'] ? $data_query['negative']['Paper']->hits->total : 0;
            $quantity_tv_negative = $data_query['negative']['Tv'] ? $data_query['negative']['Tv']->hits->total : 0;
            $total_negative = $quantity_threads_negative + $quantity_social_negative + $quantity_youtube_negative + $quantity_ifollow_negative + $quantity_tiktok_negative + $quantity_twitter_negative + $quantity_reviewapp_negative + $quantity_paper_negative + $quantity_tv_negative + $quantity_instagram_negative + $quantity_zalo_negative + $quantity_ecommerce_negative;
            return ['neutral' => $total_neutral, 'positive' => $total_positive, 'negative' => $total_negative];
        };

        $get_channel = function ($data_query) {
            $channels = [];
            $total = 0;
            if ($data_query['channel']['Facebook']) {
                $channels['Facebook'] = $data_query['channel']['Facebook']->hits->total;
                $total += $channels['Facebook'];
            }
            if ($data_query['channel']['Youtube']) {
                $channels['Youtube'] = $data_query['channel']['Youtube']->hits->total;
                $total += $channels['Youtube'];
            }
            if ($data_query['channel']['Ifollow']) {
                $channels['Ifollow'] = $data_query['channel']['Ifollow']->hits->total;
                $total += $channels['Ifollow'];
            }
            if ($data_query['channel']['Instagram']) {
                $channels['Instagram'] = $data_query['channel']['Instagram']->hits->total;
                $total += $channels['Instagram'];
            }
            if ($data_query['channel']['Zalo']) {
                $channels['Zalo'] = $data_query['channel']['Zalo']->hits->total;
                $total += $channels['Zalo'];
            }
            if ($data_query['channel']['Tiktok']) {
                $channels['Tiktok'] = $data_query['channel']['Tiktok']->hits->total;
                $total += $channels['Tiktok'];
            }
            if ($data_query['channel']['Twitter']) {
                $channels['Twitter'] = $data_query['channel']['Twitter']->hits->total;
                $total += $channels['Twitter'];
            }
            if ($data_query['channel']['Reviewapp']) {
                $channels['Reviewapp'] = $data_query['channel']['Reviewapp']->hits->total;
                $total += $channels['Reviewapp'];
            }
            if ($data_query['channel']['Reviewmap']) {
                $channels['Reviewmap'] = $data_query['channel']['Reviewmap']->hits->total;
                $total += $channels['Reviewmap'];
            }
            if ($data_query['channel']['Ecommerce']) {
                $channels['Ecommerce'] = $data_query['channel']['Ecommerce']->hits->total;
                $total += $channels['Ecommerce'];
            }
            if ($data_query['channel']['Threads']) {
                $channels['Threads'] = $data_query['channel']['Threads']->hits->total;
                $total += $channels['Threads'];
            }
            if ($data_query['channel']['Paper']) {
                $channels['Paper'] = $data_query['channel']['Paper']->hits->total;
                $total += $channels['Paper'];
            }
            if ($data_query['channel']['Tv']) {
                $channels['Tv'] = $data_query['channel']['Tv']->hits->total;
                $total += $channels['Tv'];
            }

            $channels['Total'] = $total;
            return $channels;
        };

        // query, channel, sentiment of all objects
        $list_data_query = [];
        $list_channel = [];
        $list_sentiment = [];

        $totalOfAnotherBrand = 0;
        $neutralOfAnotherBrand = 0;
        $negativeOfAnotherBrand = 0;
        $positiveOfAnotherBrand = 0;
        $list_channel_and_sentiment_sub_primary = [
            'Thương hiệu BIM và lãnh đạo' => [
                'object_name' => 'Thương hiệu BIM và lãnh đạo',
            ],
            'Các dự án' => [
                'object_name' => 'Các dự án',
            ],
        ];
        $arraySubPrimaryPushed = [];
        foreach ($list_object as $sub_brand_object) {
            $list_data_query_sub = [];
            $list_channel_sub = [];
            $list_sentiment_sub = [];
            foreach ($sub_brand_object as $item) {
                $data_query = $get_data_query($active_channels, $brand_id, $item->id, $start_date, $end_date, $data_check);
                // echo "<pre>";print_r($data_query);
                $channel = $get_channel($data_query);
                // echo "<pre>channel\n";print_r($channel);
                $quantity_all_channel = [
                    'object_name' => $item->name,
                ];
                $total = 0;
                if (isset($channel['Facebook'])) {
                    $quantity_all_channel['Facebook'] = $channel['Facebook'];
                    $total += $channel['Facebook'];
                }
                if (isset($channel['Youtube'])) {
                    $quantity_all_channel['Youtube'] = $channel['Youtube'];
                    $total += $channel['Youtube'];
                }
                if (isset($channel['Ifollow'])) {
                    $quantity_all_channel['Ifollow'] = $channel['Ifollow'];
                    $total += $channel['Ifollow'];
                }
                if (isset($channel['Instagram'])) {
                    $quantity_all_channel['Instagram'] = $channel['Instagram'];
                    $total += $channel['Instagram'];
                }
                if (isset($channel['Zalo'])) {
                    $quantity_all_channel['Zalo'] = $channel['Zalo'];
                    $total += $channel['Zalo'];
                }
                if (isset($channel['Tiktok'])) {
                    $quantity_all_channel['Tiktok'] = $channel['Tiktok'];
                    $total += $channel['Tiktok'];
                }
                if (isset($channel['Twitter'])) {
                    $quantity_all_channel['Twitter'] = $channel['Twitter'];
                    $total += $channel['Twitter'];
                }
                if (isset($channel['Reviewapp'])) {
                    $quantity_all_channel['Reviewapp'] = $channel['Reviewapp'];
                    $total += $channel['Reviewapp'];
                }
                if (isset($channel['Reviewmap'])) {
                    $quantity_all_channel['Reviewmap'] = $channel['Reviewmap'];
                    $total += $channel['Reviewmap'];
                }
                if (isset($channel['Ecommerce'])) {
                    $quantity_all_channel['Ecommerce'] = $channel['Ecommerce'];
                    $total += $channel['Ecommerce'];
                }
                if (isset($channel['Threads'])) {
                    $quantity_all_channel['Threads'] = $channel['Threads'];
                    $total += $channel['Threads'];
                }
                if (isset($channel['Paper'])) {
                    $quantity_all_channel['Paper'] = $channel['Paper'];
                    $total += $channel['Paper'];
                }
                if (isset($channel['Tv'])) {
                    $quantity_all_channel['Tv'] = $channel['Tv'];
                    $total += $channel['Tv'];
                }
                $quantity_all_channel['Total'] = $total;

                $sentiment = $get_sentiment($data_query);
                $percentage = $calc_sentiment_pecentage($sentiment['neutral'], $sentiment['positive'], $sentiment['negative']);
                $array_sentiment = [
                    'Positive' => [
                        'object_name' => $item->name,
                        'quantity' => $sentiment['positive'],
                        'percentage' => $percentage['positive']
                    ],
                    'Negative' => [
                        'object_name' => $item->name,
                        'quantity' => $sentiment['negative'],
                        'percentage' => $percentage['negative']
                    ],
                    'Neutral' => [
                        'object_name' => $item->name,
                        'quantity' => $sentiment['neutral'],
                        'percentage' => $percentage['neutral']
                    ]
                ];

                $list_data_query_sub[] = $data_query;
                $list_channel_sub[] = $quantity_all_channel;
                $list_sentiment_sub[] = $array_sentiment;

                if ($item->id == 41777) {
                    $total = 0;
                    if (isset($channel['Facebook'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Facebook'] = $channel['Facebook'];
                        $total += $channel['Facebook'];
                    }
                    if (isset($channel['Youtube'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Youtube'] = $channel['Youtube'];
                        $total += $channel['Youtube'];
                    }
                    if (isset($channel['Ifollow'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Ifollow'] = $channel['Ifollow'];
                        $total += $channel['Ifollow'];
                    }
                    if (isset($channel['Instagram'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Instagram'] = $channel['Instagram'];
                        $total += $channel['Instagram'];
                    }
                    if (isset($channel['Zalo'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Zalo'] = $channel['Zalo'];
                        $total += $channel['Zalo'];
                    }
                    if (isset($channel['Tiktok'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Tiktok'] = $channel['Tiktok'];
                        $total += $channel['Tiktok'];
                    }
                    if (isset($channel['Twitter'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Twitter'] = $channel['Twitter'];
                        $total += $channel['Twitter'];
                    }
                    if (isset($channel['Reviewapp'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Reviewapp'] = $channel['Reviewapp'];
                        $total += $channel['Reviewapp'];
                    }
                    if (isset($channel['Reviewmap'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Reviewmap'] = $channel['Reviewmap'];
                        $total += $channel['Reviewmap'];
                    }
                    if (isset($channel['Ecommerce'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Ecommerce'] = $channel['Ecommerce'];
                        $total += $channel['Ecommerce'];
                    }
                    if (isset($channel['Threads'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Threads'] = $channel['Threads'];
                        $total += $channel['Threads'];
                    }
                    if (isset($channel['Paper'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Paper'] = $channel['Paper'];
                        $total += $channel['Paper'];
                    }
                    if (isset($channel['Tv'])) {
                        $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Tv'] = $channel['Tv'];
                        $total += $channel['Tv'];
                    }
                    $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['Total'] = $total;

                    $sentiment = $get_sentiment($data_query);
                    $percentage = $calc_sentiment_pecentage($sentiment['neutral'], $sentiment['positive'], $sentiment['negative']);
                    $array_sentiment = [
                        'Positive' => [
                            'object_name' => $item->name,
                            'quantity' => $sentiment['positive'],
                            'percentage' => $percentage['positive']
                        ],
                        'Negative' => [
                            'object_name' => $item->name,
                            'quantity' => $sentiment['negative'],
                            'percentage' => $percentage['negative']
                        ],
                        'Neutral' => [
                            'object_name' => $item->name,
                            'quantity' => $sentiment['neutral'],
                            'percentage' => $percentage['neutral']
                        ]
                    ];
                    $list_channel_and_sentiment_sub_primary['Thương hiệu BIM và lãnh đạo']['array_sentiment'] = $array_sentiment;
                } elseif (in_array($item->id, array(41797,41796,41795,41794,41793,41787,41788,41792,41791,41790,41789,41786,41785,41784,41783,41782,41781,41780,41779,41778))) {
                    if (in_array($item->id, $arraySubPrimaryPushed)) continue;
                    $arraySubPrimaryPushed[] = $item->id;
                    if (isset($channel['Facebook'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Facebook'] += $channel['Facebook'];
                        $totalOfAnotherBrand += $channel['Facebook'];
                    }
                    if (isset($channel['Youtube'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Youtube'] += $channel['Youtube'];
                        $totalOfAnotherBrand += $channel['Youtube'];
                    }
                    if (isset($channel['Ifollow'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Ifollow'] += $channel['Ifollow'];
                        $totalOfAnotherBrand += $channel['Ifollow'];
                    }
                    if (isset($channel['Instagram'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Instagram'] += $channel['Instagram'];
                        $totalOfAnotherBrand += $channel['Instagram'];
                    }
                    if (isset($channel['Zalo'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Zalo'] += $channel['Zalo'];
                        $totalOfAnotherBrand += $channel['Zalo'];
                    }
                    if (isset($channel['Tiktok'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Tiktok'] += $channel['Tiktok'];
                        $totalOfAnotherBrand += $channel['Tiktok'];
                    }
                    if (isset($channel['Twitter'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Twitter'] += $channel['Twitter'];
                        $totalOfAnotherBrand += $channel['Twitter'];
                    }
                    if (isset($channel['Reviewapp'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Reviewapp'] += $channel['Reviewapp'];
                        $totalOfAnotherBrand += $channel['Reviewapp'];
                    }
                    if (isset($channel['Reviewmap'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Reviewmap'] += $channel['Reviewmap'];
                        $totalOfAnotherBrand += $channel['Reviewmap'];
                    }
                    if (isset($channel['Ecommerce'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Ecommerce'] += $channel['Ecommerce'];
                        $totalOfAnotherBrand += $channel['Ecommerce'];
                    }
                    if (isset($channel['Threads'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Threads'] += $channel['Threads'];
                        $totalOfAnotherBrand += $channel['Threads'];
                    }
                    if (isset($channel['Paper'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Paper'] += $channel['Paper'];
                        $totalOfAnotherBrand += $channel['Paper'];
                    }
                    if (isset($channel['Tv'])) {
                        $list_channel_and_sentiment_sub_primary['Các dự án']['Tv'] += $channel['Tv'];
                        $totalOfAnotherBrand += $channel['Tv'];
                    }
                    $list_channel_and_sentiment_sub_primary['Các dự án']['Total'] = $totalOfAnotherBrand;

                    $neutralOfAnotherBrand += $sentiment['neutral'];
                    $negativeOfAnotherBrand += $sentiment['negative'];
                    $positiveOfAnotherBrand += $sentiment['positive'];
                }
            }
            $list_data_query[] = $list_data_query_sub;
            $list_channel[] = $list_channel_sub;
            $list_sentiment[] = $list_sentiment_sub;
        }

        $percentage = $calc_sentiment_pecentage($neutralOfAnotherBrand, $positiveOfAnotherBrand, $negativeOfAnotherBrand);
        $array_sentiment = [
            'Positive' => [
                'object_name' => 'Các dự án',
                'quantity' => $positiveOfAnotherBrand,
                'percentage' => $percentage['positive']
            ],
            'Negative' => [
                'object_name' => 'Các dự án',
                'quantity' => $negativeOfAnotherBrand,
                'percentage' => $percentage['negative']
            ],
            'Neutral' => [
                'object_name' => 'Các dự án',
                'quantity' => $neutralOfAnotherBrand,
                'percentage' => $percentage['neutral']
            ]
        ];
        $list_channel_and_sentiment_sub_primary['Các dự án']['array_sentiment'] = $array_sentiment;
        // pre($list_channel_and_sentiment_sub_primary);
        // die;

        $list_primary_data_query = [];
        $list_primary_quantity_all_channel = [];
        $list_primary_array_sentiment = [];
        // print_r('<pre>');
        foreach ($list_object as $key => $sub_brand_object) {
            $list_primary_data_query[] = $list_data_query[$key][0];
            $list_primary_quantity_all_channel[] = $list_channel[$key][0];
            $list_primary_array_sentiment[] = $list_sentiment[$key][0];
        }
        // echo "<pre>";
        // print_r($list_primary_quantity_all_channel);
        // exit;

        /* NGUỒN TIN TIÊU CỰC ĐÁNG LƯU Ý */
        $list_primary_data_ifollow_negative = [];
        $list_primary_data_social_negative = [];
        $list_primary_data_youtube_negative = [];
        $list_primary_data_tiktok_negative = [];
        $list_primary_data_twitter_negative = [];
        $list_primary_data_reviewapp_negative = [];
        $list_primary_data_reviewmap_negative = [];
        $list_primary_data_ecommerce_negative = [];
        $list_primary_data_threads_negative = [];
        $list_primary_data_instagram_negative = [];
        $list_primary_data_zalo_negative = [];
        $list_primary_data_paper_negative = [];
        $list_primary_data_tv_negative = [];

        foreach ($list_object as $key => $sub_brand_object) {
            // News
            if ($active_channels['Ifollow']) {
                $list_primary_data_ifollow_negative[] = array_slice($list_primary_data_query[$key]['negative']['Ifollow']->hits->hits, 0, $limit_top_negative['Ifollow']);
            } else {
                $list_primary_data_ifollow_negative[] = [];
            }

            // Facebook
            if ($active_channels['Facebook']) {
                $list_primary_data_social_negative[] = array_slice($list_primary_data_query[$key]['negative']['Facebook']->hits->hits, 0, $limit_top_negative['Facebook']);
            } else {
                $list_primary_data_social_negative[] = [];
            }

            // Youtube
            if ($active_channels['Youtube']) {
                $list_primary_data_youtube_negative[] = array_slice($list_primary_data_query[$key]['negative']['Youtube']->hits->hits, 0, $limit_top_negative['Youtube']);
            } else {
                $list_primary_data_youtube_negative[] = [];
            }

            // Instagram
            if ($active_channels['Instagram']) {
                $list_primary_data_instagram_negative[] = array_slice($list_primary_data_query[$key]['negative']['Instagram']->hits->hits, 0, $limit_top_negative['Instagram']);
            } else {
                $list_primary_data_instagram_negative[] = [];
            }

            // Tiktok
            if ($active_channels['Tiktok']) {
                $list_primary_data_tiktok_negative[] = array_slice($list_primary_data_query[$key]['negative']['Tiktok']->hits->hits, 0, $limit_top_negative['Tiktok']);
            } else {
                $list_primary_data_tiktok_negative[] = [];
            }

            // Twitter
            if ($active_channels['Twitter']) {
                $list_primary_data_twitter_negative[] = array_slice($list_primary_data_query[$key]['negative']['Twitter']->hits->hits, 0, $limit_top_negative['Twitter']);
            } else {
                $list_primary_data_twitter_negative[] = [];
            }

            // Reviewapp
            if ($active_channels['Reviewapp']) {
                $list_primary_data_reviewapp_negative[] = array_slice($list_primary_data_query[$key]['negative']['Reviewapp']->hits->hits, 0, $limit_top_negative['Reviewapp']);
            } else {
                $list_primary_data_reviewapp_negative[] = [];
            }

            // Reviewmap
            if ($active_channels['Reviewmap']) {
                $list_primary_data_reviewmap_negative = array_slice($list_primary_data_query[$key]['negative']['Reviewmap']->hits->hits, 0, $limit_top_negative['Reviewmap']);
            } else {
                $list_primary_data_reviewmap_negative[] = [];
            }

            // Zalo
            if ($active_channels['Zalo']) {
                $list_primary_data_zalo_negative[] = array_slice($list_primary_data_query[$key]['negative']['Zalo']->hits->hits, 0, $limit_top_negative['Instagram']);
            } else {
                $list_primary_data_zalo_negative[] = [];
            }

            // Ecommerce
            if ($active_channels['Ecommerce']) {
                $list_primary_data_ecommerce_negative = array_slice($list_primary_data_query[$key]['negative']['Ecommerce']->hits->hits, 0, $limit_top_negative['Ecommerce']);
            } else {
                $list_primary_data_ecommerce_negative[] = [];
            }

            // Threads
            if ($active_channels['Threads']) {
                $list_primary_data_threads_negative[] = array_slice($list_primary_data_query[$key]['negative']['Threads']->hits->hits, 0, $limit_top_negative['Threads']);
            } else {
                $list_primary_data_threads_negative[] = [];
            }

            // Paper
            if ($active_channels['Paper']) {
                $list_primary_data_paper_negative[] = array_slice($list_primary_data_query[$key]['negative']['Paper']->hits->hits, 0, $limit_top_negative['Paper']);
            } else {
                $list_primary_data_paper_negative[] = [];
            }

            // Tv
            if ($active_channels['Tv']) {
                $list_primary_data_tv_negative[] = array_slice($list_primary_data_query[$key]['negative']['Tv']->hits->hits, 0, $limit_top_negative['Tv']);
            } else {
                $list_primary_data_tv_negative[] = [];
            }
        }

        // print_r($list_primary_data_youtube_negative); exit;


        /* TOP NGUỒN TIN VỀ BRAND */
        $list_list_social_top_source = [];
        $list_list_ifollow_top_source = [];
        $list_list_youtube_top_source = [];
        $list_list_tiktok_top_source = [];
        $list_list_paper_top_source = [];
        $list_list_twitter_top_source = [];
        $list_list_threads_top_source = [];
        $list_list_instagram_top_source = [];
        $list_list_reviewmap_top_source = [];
        $list_list_zalo_top_source = [];
        $list_list_ecommerce_top_source = [];

        foreach ($list_object as $key => $sub_brand_object) {
            $object_primary = $object_id[$key][0];
            // Social
            $list_social_top_source = array();
            if ($active_channels['Facebook']) {
                $is_checked = isset($data_check['Facebook']) ? $data_check['Facebook']->is_checked : 0;
                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                $json_doc = __generateJsonTopSourceSocial($start_date, $end_date, $brand_id, [$object_primary], $is_checked, $is_read, $is_subbrand);
                $data_query = _searchSocial($json_doc);
                $source_generate_data = $data_query->aggregations->group_by_state->buckets;

                foreach ($source_generate_data as $row) {
                    $page_name = $row->top_tag_hits->hits->hits[0]->_source->page_name;
                    if (!isset($list_social_top_source[$page_name])) {
                        $list_social_top_source[$page_name] = (object)array(
                            'doc_count' => $row->doc_count,
                            'page_name' => $page_name,
                            'key' => $row->key
                        );
                    } else {
                        $list_social_top_source[$page_name] = (object)array(
                            'doc_count' => $row->doc_count + $list_social_top_source[$page_name]->doc_count,
                            'page_name' => $page_name,
                            'key' => $row->key
                        );
                    }
                }
                usort($list_social_top_source, function ($first, $second) {
                    return $first->doc_count < $second->doc_count;
                });
            }
            $list_list_social_top_source[] = $list_social_top_source;
            // Ifollow
            $list_ifollow_top_source = array();
            if ($active_channels['Ifollow']) {
                $is_checked = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_checked : 0;
                $is_read = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_read : 0;
                $json_doc = __generateJsonTopSourceIfollow($start_date, $end_date, $brand_id, [$object_primary], null, $is_checked, $is_read, $is_subbrand);
                $data_query = _searchIfollow($json_doc);
                $source_generate_data = $data_query->aggregations->group_by_state->buckets;

                foreach ($source_generate_data as $row) {
                    $domain_name = $row->top_tag_hits->hits->hits[0]->_source->web_page_name;
                    if (!isset($list_ifollow_top_source[$domain_name])) {
                        $list_ifollow_top_source[$domain_name] = (object)array(
                            'web_page_name' => $domain_name,
                            'doc_count' => $row->doc_count
                        );
                    } else {
                        $list_ifollow_top_source[$domain_name] = (object)array(
                            'web_page_name' => $domain_name,
                            'doc_count' => $row->doc_count
                        );
                    }
                }
                usort($list_ifollow_top_source, function ($first, $second) {
                    return $first->doc_count < $second->doc_count;
                });
            }
            $list_list_ifollow_top_source[] = $list_ifollow_top_source;
            // Youtube
            $list_youtube_top_source = array();
            if ($active_channels['Youtube']) {
                $is_checked = isset($data_check['Youtube']) ? $data_check['Youtube']->is_checked : 0;
                $is_read = isset($data_check['Youtube']) ? $data_check['Youtube']->is_read : 0;
                $json_doc_youtube = __generateJsonTopSourceYoutube($start_date, $end_date, $brand_id, [$object_primary], true, $is_checked, $is_read, $is_subbrand);
                $data_query_youtube = _searchYoutube($json_doc_youtube);

                $data_query_youtube = $data_query_youtube->hits->hits;

                $array_return = array();
                foreach ($data_query_youtube as $key => $v) {
                    $array_return[$v->_source->page_name][] = $v;
                }

                foreach ($array_return as $key => $item) {
                    $list_youtube_top_source[$key]['page_name'] = $item[0]->_source->page_name;
                    $list_youtube_top_source[$key]['count'] = count($item);
                    $list_youtube_top_source[$key]['page_id'] = $item[0]->_source->page_id;
                }

                usort($list_youtube_top_source, function ($item1, $item2) {
                    return $item1['count'] < $item2['count'];
                });
            }
            $list_list_youtube_top_source[] = $list_youtube_top_source;
            // Tiktok
            $list_tiktok_top_source = array();
            if ($active_channels['Tiktok']) {
                $is_checked = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_checked : 0;
                $is_read = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_read : 0;
                $json_doc_tiktok = __generateJsonTopSourceTiktok($start_date, $end_date, $brand_id, [$object_primary], true, $is_checked, $is_read, $is_subbrand);
                $data_query_tiktok = _searchTiktok($json_doc_tiktok);

                $data_query_tiktok = $data_query_tiktok->hits->hits;

                $array_return = array();
                foreach ($data_query_tiktok as $key => $v) {
                    $array_return[$v->_source->page_name][] = $v;
                }

                foreach ($array_return as $key => $item) {
                    $list_tiktok_top_source[$key]['page_name'] = $item[0]->_source->page_name;
                    $list_tiktok_top_source[$key]['count'] = count($item);
                    $list_tiktok_top_source[$key]['page_id'] = $item[0]->_source->page_id;
                }

                usort($list_tiktok_top_source, function ($item1, $item2) {
                    return $item1['count'] < $item2['count'];
                });
            }
            $list_list_tiktok_top_source[] = $list_tiktok_top_source;
            // Twitter
            $list_twitter_top_source = array();
            if ($active_channels['Twitter']) {
                $is_checked = isset($data_check['Twitter']) ? $data_check['Twitter']->is_checked : 0;
                $is_read = isset($data_check['Twitter']) ? $data_check['Twitter']->is_read : 0;
                $json_doc_twitter = __generateJsonTopSourceTwitter($start_date, $end_date, $brand_id, [$object_primary], true, $is_checked, $is_read, $is_subbrand);
                $data_query_twitter = _searchTwitter($json_doc_twitter);

                $data_query_twitter = $data_query_twitter->hits->hits;

                $array_return = array();
                foreach ($data_query_twitter as $key => $v) {
                    $array_return[$v->_source->page_name][] = $v;
                }

                foreach ($array_return as $key => $item) {
                    $list_twitter_top_source[$key]['page_name'] = $item[0]->_source->page_name;
                    $list_twitter_top_source[$key]['count'] = count($item);
                    $list_twitter_top_source[$key]['page_id'] = $item[0]->_source->page_id;
                }

                usort($list_twitter_top_source, function ($item1, $item2) {
                    return $item1['count'] < $item2['count'];
                });
            }
            $list_list_twitter_top_source[] = $list_twitter_top_source;
            // Instagram
            $list_instagram_top_source = array();
            if ($active_channels['Instagram']) {
                $is_checked = isset($data_check['Instagram']) ? $data_check['Instagram']->is_checked : 0;
                $json_doc_instagram = __generateJsonTopSourceInstagram($start_date, $end_date, $brand_id, [$object_primary], true, $is_checked);
                $data_query_instagram = _searchInstagram($json_doc_instagram);

                $data_query_instagram = $data_query_instagram->hits->hits;

                $array_return = array();
                foreach ($data_query_instagram as $key => $v) {
                    $array_return[$v->_source->page_name][] = $v;
                }

                foreach ($array_return as $key => $item) {
                    $list_instagram_top_source[$key]['page_name'] = $item[0]->_source->page_name;
                    $list_instagram_top_source[$key]['count'] = count($item);
                    $list_instagram_top_source[$key]['page_id'] = $item[0]->_source->page_id;
                }

                usort($list_instagram_top_source, function ($item1, $item2) {
                    return $item1['count'] < $item2['count'];
                });
            }
            $list_list_instagram_top_source[] = $list_instagram_top_source;

            // Reviewmap
            $list_reviewmap_top_source = array();
            if ($active_channels['Reviewmap']) {
                $is_checked = isset($data_check['Reviewmap']) ? $data_check['Reviewmap']->is_checked : 0;
                $json_doc_reviewmap = __generateJsonTopSourceReviewmap($start_date, $end_date, $brand_id, [$object_primary], true, $is_checked);
                $data_query_reviewmap = _searchReviewmap($json_doc_reviewmap);

                $data_query_reviewmap = $data_query_reviewmap->hits->hits;

                $array_return = array();
                foreach ($data_query_reviewmap as $key => $v) {
                    $array_return[$v->_source->page_name][] = $v;
                }

                foreach ($array_return as $key => $item) {
                    $list_reviewmap_top_source[$key]['page_name'] = $item[0]->_source->page_name;
                    $list_reviewmap_top_source[$key]['count'] = count($item);
                    $list_reviewmap_top_source[$key]['page_id'] = $item[0]->_source->page_id;
                }

                usort($list_reviewmap_top_source, function ($item1, $item2) {
                    return $item1['count'] < $item2['count'];
                });
            }
            $list_list_reviewmap_top_source[] = $list_reviewmap_top_source;

            // Zalo
            $list_zalo_top_source = array();
            if ($active_channels['Zalo']) {
                $is_checked = isset($data_check['Zalo']) ? $data_check['Zalo']->is_checked : 0;
                $json_doc_zalo = __generateJsonTopSourceZalo($start_date, $end_date, $brand_id, [$object_primary], true, $is_checked);
                $data_query_zalo = _searchZalo($json_doc_zalo);

                $data_query_zalo = $data_query_zalo->hits->hits;

                $array_return = array();
                foreach ($data_query_zalo as $key => $v) {
                    $array_return[$v->_source->page_name][] = $v;
                }

                foreach ($array_return as $key => $item) {
                    $list_zalo_top_source[$key]['page_name'] = $item[0]->_source->page_name;
                    $list_zalo_top_source[$key]['count'] = count($item);
                    $list_zalo_top_source[$key]['page_id'] = $item[0]->_source->page_id;
                }

                usort($list_zalo_top_source, function ($item1, $item2) {
                    return $item1['count'] < $item2['count'];
                });
            }
            $list_list_zalo_top_source[] = $list_zalo_top_source;

            // Ecommerce
            $list_ecommerce_top_source = array();
            if ($active_channels['Ecommerce']) {
                $is_checked = isset($data_check['Ecommerce']) ? $data_check['Ecommerce']->is_checked : 0;
                $json_doc_ecommerce = __generateJsonTopSourceEcommerce($start_date, $end_date, $brand_id, [$object_primary], true, $is_checked);
                $data_query_ecommerce = _searchEcommerce($json_doc_ecommerce);

                $data_query_ecommerce = $data_query_ecommerce->hits->hits;

                $array_return = array();
                foreach ($data_query_ecommerce as $key => $v) {
                    $array_return[$v->_source->page_name][] = $v;
                }

                foreach ($array_return as $key => $item) {
                    $list_ecommerce_top_source[$key]['page_name'] = $item[0]->_source->page_name;
                    $list_ecommerce_top_source[$key]['count'] = count($item);
                    $list_ecommerce_top_source[$key]['page_id'] = $item[0]->_source->page_id;
                }

                usort($list_ecommerce_top_source, function ($item1, $item2) {
                    return $item1['count'] < $item2['count'];
                });
            }
            $list_list_ecommerce_top_source[] = $list_ecommerce_top_source;

            // Threads
            $list_threads_top_source = array();
            if ($active_channels['Threads']) {
                $is_checked = isset($data_check['Threads']) ? $data_check['Threads']->is_checked : 0;
                $json_doc_threads = __generateJsonTopSourceThreads($start_date, $end_date, $brand_id, [$object_primary], true, $is_checked);
                $data_query_threads = _searchThreads($json_doc_threads);

                $data_query_threads = $data_query_threads->hits->hits;

                $array_return = array();
                foreach ($data_query_threads as $key => $v) {
                    $array_return[$v->_source->page_name][] = $v;
                }

                foreach ($array_return as $key => $item) {
                    $list_threads_top_source[$key]['page_name'] = $item[0]->_source->page_name;
                    $list_threads_top_source[$key]['count'] = count($item);
                    $list_threads_top_source[$key]['page_id'] = $item[0]->_source->page_id;
                }

                usort($list_threads_top_source, function ($item1, $item2) {
                    return $item1['count'] < $item2['count'];
                });
            }
            $list_list_threads_top_source[] = $list_threads_top_source;

            // Reviewapp
            $list_reviewapp_top_source = array();
            $list_list_reviewapp_top_source[] = $list_reviewapp_top_source;

            // Paper
            $list_paper_top_source = array();
            if ($active_channels['Paper']) {
                $is_checked = isset($data_check['Paper']) ? $data_check['Paper']->is_checked : 0;
                $is_read = isset($data_check['Paper']) ? $data_check['Paper']->is_read : 0;
                $json_doc = __generateJsonTopSourcePaper($start_date, $end_date, $brand_id, [$object_primary], $is_checked, $is_read, $is_subbrand);
                $data_query = _searchPaper($json_doc);
                $source_generate_data = $data_query->aggregations->group_by_state->buckets;

                foreach ($source_generate_data as $row) {
                    $domain_name = $row->top_tag_hits->hits->hits[0]->_source->paper_page_name;
                    if (!isset($list_paper_top_source[$domain_name])) {
                        $list_paper_top_source[$domain_name] = (object)array(
                            'paper_page_name' => $domain_name,
                            'doc_count' => $row->doc_count
                        );
                    } else {
                        $list_paper_top_source[$domain_name] = (object)array(
                            'paper_page_name' => $domain_name,
                            'doc_count' => $row->doc_count + $list_paper_top_source[$domain_name]->doc_count
                        );
                    }
                }
                usort($list_paper_top_source, function ($first, $second) {
                    return $first->doc_count < $second->doc_count;
                });
            }
            $list_list_paper_top_source[] = $list_paper_top_source;

            // Tv
            $list_tv_top_source = array();
            if ($active_channels['Tv']) {
                $is_checked = isset($data_check['Tv']) ? $data_check['Tv']->is_checked : 0;
                $is_read = isset($data_check['Tv']) ? $data_check['Tv']->is_read : 0;
                $json_doc = __generateJsonTopSourceTv($start_date, $end_date, $brand_id, [$object_primary], null, $is_checked, $is_read, $is_subbrand);
                $data_query = _searchTv($json_doc);
                $source_generate_data = $data_query->aggregations->group_by_state->buckets;

                foreach ($source_generate_data as $row) {
                    $domain_name = $row->top_tag_hits->hits->hits[0]->_source->web_page_name;
                    if (!isset($list_tv_top_source[$domain_name])) {
                        $list_tv_top_source[$domain_name] = (object)array(
                            'tv_page_name' => $domain_name,
                            'doc_count' => $row->doc_count
                        );
                    } else {
                        $list_tv_top_source[$domain_name] = (object)array(
                            'tv_page_name' => $domain_name,
                            'doc_count' => $row->doc_count + $list_tv_top_source[$domain_name]->doc_count
                        );
                    }
                }
                usort($list_tv_top_source, function ($first, $second) {
                    return $first->doc_count < $second->doc_count;
                });
            }
            $list_list_tv_top_source[] = $list_tv_top_source;
        }

        // echo "<pre>";
        // print_r($list_list_paper_top_source);
        // exit;
        // if ($setting_id == 117) {
        //     echo "<pre>";
        //     print_r($list_list_paper_top_source);
        //     exit;
        // }


        // TOP REACTION
        $list_data_facebook_top_reaction = [];
        $list_data_youtube_top_reaction = [];
        $list_data_ifollow_top_reaction = [];
        $list_data_tiktok_top_reaction = [];
        $list_data_paper_top_reaction = [];
        $list_data_twitter_top_reaction = [];
        $list_data_threads_top_reaction = [];
        $list_data_reviewapp_top_reaction = [];

        foreach ($list_object as $key => $sub_brand_object) {
            $object_primary = $object_id[$key][0];
            $data_facebook_top_reaction = array();
            if ($active_channels['Facebook']) {
                $data_facebook_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Facebook']->hits->hits, 0, $limit_top_reaction['Facebook']);
            }
            $list_data_facebook_top_reaction[] = $data_facebook_top_reaction;

            $data_youtube_top_reaction = array();
            if ($active_channels['Youtube']) {
                $data_youtube_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Youtube']->hits->hits, 0, $limit_top_reaction['Youtube']);
            }
            $list_data_youtube_top_reaction[] = $data_youtube_top_reaction;

            $data_ifollow_top_reaction = array();
            if ($active_channels['Ifollow']) {
                $data_ifollow_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Ifollow']->hits->hits, 0, $limit_top_reaction['Ifollow']);
            }
            $list_data_ifollow_top_reaction[] = $data_ifollow_top_reaction;

            $data_tiktok_top_reaction = [];
            if ($active_channels['Tiktok']) {
                $data_tiktok_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Tiktok']->hits->hits, 0, $limit_top_reaction['Tiktok']);
            }
            $list_data_tiktok_top_reaction[] = $data_tiktok_top_reaction;

            $data_twitter_top_reaction = [];
            if ($active_channels['Twitter']) {
                $data_twitter_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Twitter']->hits->hits, 0, $limit_top_reaction['Twitter']);
            }
            $list_data_twitter_top_reaction[] = $data_twitter_top_reaction;

            $data_instagram_top_reaction = [];
            if ($active_channels['Instagram']) {
                $data_instagram_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Instagram']->hits->hits, 0, $limit_top_reaction['Instagram']);
            }
            $list_data_instagram_top_reaction[] = $data_instagram_top_reaction;

            $data_reviewmap_top_reaction = [];
            if ($active_channels['Reviewmap']) {
                $data_reviewmap_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Reviewmap']->hits->hits, 0, $limit_top_reaction['Reviewmap']);
            }
            $list_data_reviewmap_top_reaction[] = $data_reviewmap_top_reaction;

            $data_zalo_top_reaction = [];
            if ($active_channels['Zalo']) {
                $data_zalo_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Zalo']->hits->hits, 0, $limit_top_reaction['Zalo']);
            }
            $list_data_zalo_top_reaction[] = $data_zalo_top_reaction;

            $data_ecommerce_top_reaction = [];
            if ($active_channels['Ecommerce']) {
                $data_ecommerce_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Ecommerce']->hits->hits, 0, $limit_top_reaction['Ecommerce']);
            }
            $list_data_ecommerce_top_reaction[] = $data_ecommerce_top_reaction;


            $data_reviewapp_top_reaction = [];
            if ($active_channels['Reviewapp']) {
                $data_reviewapp_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Reviewapp']->hits->hits, 0, $limit_top_reaction['Reviewapp']);
            }
            $list_data_reviewapp_top_reaction[] = $data_reviewapp_top_reaction;

            $data_threads_top_reaction = [];
            if ($active_channels['Threads']) {
                $data_threads_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Threads']->hits->hits, 0, $limit_top_reaction['Threads']);
            }
            $list_data_threads_top_reaction[] = $data_threads_top_reaction;

            $data_paper_top_reaction = [];
            if ($active_channels['Paper']) {
                $data_paper_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Paper']->hits->hits, 0, $limit_top_reaction['Paper']);
            }
            $list_data_paper_top_reaction[] = $data_paper_top_reaction;

            $data_tv_top_reaction = [];
            if ($active_channels['Tv']) {
                $data_tv_top_reaction = array_slice($list_primary_data_query[$key]['channel']['Tv']->hits->hits, 0, $limit_top_reaction['Tv']);
            }
            $list_data_tv_top_reaction[] = $data_tv_top_reaction;
        }
        // echo "<pre>";print_r($list_data_facebook_top_reaction); exit;

        // Share Of Voice
        $list_chartPieConfig = [];
        foreach ($list_object as $sub_index => $sub_brand_object) {
            $share_of_voice = [];
            foreach ($list_sentiment[$sub_index] as $key => $item) {
                if (in_array($item['Negative']['object_name'], array('Eco Retreat'))) continue;
                $share_of_voice[$item['Negative']['object_name']] = [
                    'total_sentiment' => $item['Negative']['quantity'] + $item['Neutral']['quantity'] + $item['Positive']['quantity']
                ];
            }

            $key_share_of_voice = array_keys($share_of_voice);
            $label_share_of_voice = '';
            foreach ($key_share_of_voice as $key => $item) {
                $label_share_of_voice .= "'$item',";
            }

            $value_share_of_voice = array_values($share_of_voice);
            $data_share_of_voice = '';
            // pre($value_share_of_voice);die;
            $total_data_share_of_voice = 0;
            $quantityOfValueShareOfVoice = count($value_share_of_voice);

            foreach ($value_share_of_voice as $item) {
                $total_data_share_of_voice += $item['total_sentiment'];
            }

            $arrayQuantity = [];
            for ($index = 0; $index < $quantityOfValueShareOfVoice - 1; $index++) {
                $arrayQuantity[] = $total_data_share_of_voice ? round(($value_share_of_voice[$index]['total_sentiment'] / $total_data_share_of_voice) * 100, 1) : 0;
                $data_share_of_voice .= ($total_data_share_of_voice ? round(($value_share_of_voice[$index]['total_sentiment'] / $total_data_share_of_voice) * 100, 1) : 0) . ',';
            }
            $data_share_of_voice .= 100 - array_sum($arrayQuantity) . ',';

            // foreach ($value_share_of_voice as $key => $item) {
            //     $data_share_of_voice .= ($total_data_share_of_voice ? round(($item['total_sentiment'] / $total_data_share_of_voice) * 100, 1) : 0) . ',';
            // }

            $color = '';
            foreach ($data_color as $value) {
                $color .= "'" . $value . "',";
            }
            $chartPieConfig = "{
                type: 'pie',
                data: {
                    datasets: [{
                        data: [" . $data_share_of_voice . "],
                        backgroundColor: [
                            " . $color . "
                        ]
                    }],
                    labels: [" . $label_share_of_voice . "]
                },
                options: {
                    borderWidth: 2,
                    fontColor: '#FFFFFF',
                    fontStyle: 'bold',
                    responsive: true,
                    legend: {
                        position: 'left',
                        display: true,
                        labels: {
                        fontFamily: 'Cambria',
                        fontStyle: 'bold'
                        }
                    },
                    plugins: {
                        datalabels: {
                            color: '#FFFFFF',
                            align: 'end',
                            font: {
                            size: 12,
                            weight: 'bold',
                            family: 'Cambria'
                            },
                            formatter: function(value) {
                            return value +'%'
                            }
                        }
                    },
                    title: {
                        display: true,
                        align: 'right',
                        text: 'Share of Voice',
                        x: 500,
                        fontSize: 16,
                        fontColor: '#000000',
                        fontFamily: 'Cambria'
                    }
                }
            }";
            $list_chartPieConfig[] = $chartPieConfig;
        }

        // subbrand local compare
        $main_share_of_voice = [];
        foreach ($list_primary_array_sentiment as $sub_index => $item) {
            if (in_array($item['Negative']['object_name'], array('Eco Retreat'))) continue;
            $main_share_of_voice[$item['Negative']['object_name']] = [
                'total_sentiment' => $item['Negative']['quantity'] + $item['Neutral']['quantity'] + $item['Positive']['quantity']
            ];
        }
        // print_r($main_share_of_voice);die;
        $main_key_share_of_voice = array_keys($main_share_of_voice);
        $main_label_share_of_voice = '';
        foreach ($main_key_share_of_voice as $key => $item) {
            $main_label_share_of_voice .= "'$item',";
        }

        $main_value_share_of_voice = array_values($main_share_of_voice);
        $main_data_share_of_voice = '';
        // pre($value_share_of_voice);
        $main_total_data_share_of_voice = 0;

        foreach ($main_value_share_of_voice as $item) {
            $main_total_data_share_of_voice += $item['total_sentiment'];
        }

        // up 0% -> 1%
        $data_val = [];
        foreach ($main_value_share_of_voice as $key => $item) {
            $val = $main_total_data_share_of_voice ? round(($item['total_sentiment'] / $main_total_data_share_of_voice) * 100) : 0;
            if ($val == 0 && $item['total_sentiment'] != 0) {
                $val = 1;
            }
            array_push($data_val, $val);
        }
        $up_val = array_sum($data_val) - 100;
        $max_val = max($data_val);
        $minus_up_val = false;

        foreach ($data_val as $value) {
            if (!$minus_up_val && $value == $max_val) {
                $value -= $up_val;
                $minus_up_val = true;
            }
            $main_data_share_of_voice .=  $value . ',';
        }

        // foreach ($main_value_share_of_voice as $key => $item) {
        //     $main_data_share_of_voice .= ($main_total_data_share_of_voice ? round(($item['total_sentiment'] / $main_total_data_share_of_voice) * 100) : 0) . ',';
        // }

        $color = '';
        foreach ($data_color as $value) {
            $color .= "'" . $value . "',";
        }

        $main_chartPieConfig = "{
            type: 'pie',
            data: {
                datasets: [{
                    data: [" . $main_data_share_of_voice . "],
                    backgroundColor: [
                        " . $color . "
                    ]
                }],
                labels: [" . $main_label_share_of_voice . "]
            },
            options: {
                borderWidth: 2,
                fontColor: '#FFFFFF',
                fontStyle: 'bold',
                responsive: true,
                legend: {
                    position: 'left',
                    display: true,
                    labels: {
                    fontFamily: 'Cambria',
                    fontStyle: 'bold'
                    }
                },
                plugins: {
                    datalabels: {
                        color: '#FFFFFF',
                        align: 'end',
                        font: {
                        size: 12,
                        weight: 'bold',
                        family: 'Cambria'
                        },
                        formatter: function(value) {
                        return (value ? value +'%' : '')
                        }
                    }
                },
                title: {
                    display: true,
                    align: 'right',
                    text: 'Share of Voice',
                    x: 500,
                    fontSize: 16,
                    fontColor: '#000000',
                    fontFamily: 'Cambria'
                }
            }
        }";

        // echo "<pre>";print_r($list_chartPieConfig); exit;
        // LƯỢNG TIN TRÊN CÁC NỀN TẢNG
        $list_chartBarConfig = [];
        foreach ($list_object as $sub_index => $sub_brand_object) {
            $data = '';
            foreach ($list_channel[$sub_index] as $key => $item) {
                $data_channel = '';
                foreach ($active_channels as $c => $value) {
                    if ($value) {
                        $data_channel .= $item[$c] . ",";
                    }
                }
                if (in_array($item['object_name'], array('Eco Retreat'))) continue;
                $data .= "{
                    label: '" . $item['object_name'] . "',
                    data: [" . $data_channel . "],
                    backgroundColor: [
                        '" . $data_color[$key] . "',
                        '" . $data_color[$key] . "',
                        '" . $data_color[$key] . "',
                        '" . $data_color[$key] . "',
                        '" . $data_color[$key] . "',
                        '" . $data_color[$key] . "',
                        '" . $data_color[$key] . "',
                    ],
                },";
            }
            $label_bar = '';
            foreach ($active_channels as $key => $value) {
                if ($value) {
                    $label_bar .= "'" . $channel_text[$key] . "', ";
                }
            }
            $chartBarConfig = "{
                type: 'bar',
                data: {
                    labels: [" . $label_bar . "],
                    datasets: [" . $data . "]
                },
                options: {
                    responsive: true,
                    legend: {
                        display: true,
                        position: 'bottom'
                    },
                    plugins: {
                        labels: {
                            fontSize: 10,
                        },
                        datalabels: {
                            anchor: 'end',
                            align: 'top',
                            color: '#333333',
                            font: {
                            size: 8,
                            },
                        }
                    },
                    title: {
                        display: true,
                        text: 'Lượng Tin Trên Các Nền Tảng',
                        padding: 16,
                        fontSize: 14,
                        fontColor: '#000000'
                    }
                }
            }";
            $list_chartBarConfig[] = $chartBarConfig;
        }
        // echo "<pre>";print_r($list_chartBarConfig); exit;

        // main
        $main_data_bar = '';
        // foreach ($list_primary_quantity_all_channel as $key => $item) {
        //     $data_channel = '';
        //     if (in_array($item['object_name'], array('Eco Retreat'))) continue;
        //     foreach ($active_channels as $c => $value) {
        //         if ($value) {
        //             $data_channel .= $item[$c] . ",";
        //         }
        //     }
        //     $main_data_bar .= "{
        //         label: '" . $item['object_name'] . "',
        //         data: [" . $data_channel . "],
        //         backgroundColor: [
        //             '" . $data_color[$key] . "',
        //             '" . $data_color[$key] . "',
        //             '" . $data_color[$key] . "',
        //             '" . $data_color[$key] . "',
        //             '" . $data_color[$key] . "',
        //             '" . $data_color[$key] . "',
        //             '" . $data_color[$key] . "',
        //         ],
        //     },";
        // }
        $key_color = 0;
        foreach ($list_channel_and_sentiment_sub_primary as $key => $item) {
            $data_channel = '';
            // if (in_array($item['object_name'], array('Eco Retreat'))) continue;
            foreach ($active_channels as $c => $value) {
                if ($value) {
                    $data_channel .= $item[$c] . ",";
                }
            }
            $main_data_bar .= "{
                label: '" . $item['object_name'] . "',
                data: [" . $data_channel . "],
                backgroundColor: [
                    '" . $data_color[$key_color] . "',
                    '" . $data_color[$key_color] . "',
                    '" . $data_color[$key_color] . "',
                    '" . $data_color[$key_color] . "',
                    '" . $data_color[$key_color] . "',
                    '" . $data_color[$key_color] . "',
                    '" . $data_color[$key_color] . "',
                ],
            },";
            $key_color++;
        }

        $main_label_bar = '';
        foreach ($active_channels as $key => $value) {
            if ($value) {
                $main_label_bar .= "'" . $key . "', ";
            }
        }
        $main_chartBarConfig = "{
            type: 'bar',
            data: {
                labels: [" . $main_label_bar . "],
                datasets: [" . $main_data_bar . "]
            },
            defaults: {
                global: {
                    defaultFont: 'Cambria'
                }
            },
            options: {
                responsive: true,
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                    fontFamily: 'Cambria',
                    fontStyle: 'bold'
                    }
                },
                scales: {
                    xAxes: [{
                        ticks: {
                            fontFamily: 'Cambria'
                        }
                    }],
                    yAxes: [{
                        ticks: {
                            fontFamily: 'Cambria'
                        }
                    }]
                },
                plugins: {
                    labels: {
                        fontSize: 10,
                    },
                    datalabels: {
                        anchor: 'end',
                        align: 'top',
                        color: '#333333',
                        font: {
                        size: 10,
                        family: 'Cambria'
                        },
                    }
                },
                title: {
                    display: true,
                    text: 'Lượng Tin Trên Các Nền Tảng',
                    padding: 16,
                    fontSize: 16,
                    fontColor: '#000000',
                    fontFamily: 'Cambria'
                }
            }
        }";
        // print_r($main_chartBarConfig);die;

        // COUNT MENTIONS PER HOUR
        $list_chartLineConfig = [];
        if ($setting['type'] == 'daily') {
            foreach ($list_object as $sub_index => $sub_brand_object) {
                $data_hourly = [];
                foreach ($sub_brand_object as $key => $item) {
                    $hourly_mention = [];
                    for ($hi = 0; $hi < 24; $hi++) {
                        $hourly_mention[$hi] = 0;
                    }
                    $interval = DateInterval::createFromDateString('1 hour');
                    $period = new DatePeriod(new DateTime($start_date), $interval, new DateTime($end_date));
                    foreach ($period as $dt) {
                        $i = (int)$dt->format("H");
                        $start_time = $dt->format("Y-m-d H:00:00");
                        $end_time = $dt->format("Y-m-d H:59:59");
                        $data_query = $get_data_query($active_channels, $brand_id, $item->id, $start_time, $end_time, $data_check);
                        $channel = $get_channel($data_query);
                        $mention_count = $channel['Total'];
                        $hourly_mention[$i] += $mention_count;
                    }
                    $data_hourly[$item->name] = $hourly_mention;
                }

                $data = '';
                $k = 0;
                foreach ($data_hourly as $key => $item) {
                    $data .= "{
                        label: '" . $key . "',
                        lineTension: '0.5',
                        data: [" . implode(',', $item) . "],
                        backgroundColor: '" . $data_color[$k] . "',
                        borderColor: '" . $data_color[$k] . "',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    },";

                    $k++;
                }

                $chartLineConfig = "{
                    type: 'line',
                    data: {
                        labels: [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
                        datasets: [" . $data . "]
                    },
                    options: {
                        responsive: true,
                        legend: {
                            display: true,
                            position: 'bottom'
                        },
                        scales: {
                            xAxes: [{
                                display: true
                            }],
                            yAxes: [{
                                display: true
                            }]
                        },
                        title: {
                            display: true,
                            text: 'Xu Hướng Tin Bài Theo Giờ',
                            fontSize: 16,
                            fontColor: '#000000'
                        }
                    }
                }";
                $list_chartLineConfig[] = $chartLineConfig;
            }

            //main
            $main_data_hourly = [];
            foreach ($list_object as $item) {
                if (in_array($item[0]->name, array('Eco Retreat'))) continue;
                $hourly_mention = [];
                for ($hi = 0; $hi < 24; $hi++) {
                    $hourly_mention[$hi] = 0;
                }
                $interval = DateInterval::createFromDateString('1 hour');
                $period = new DatePeriod(new DateTime($start_date), $interval, new DateTime($end_date));
                foreach ($period as $dt) {
                    $i = (int)$dt->format("H");
                    $start_time = $dt->format("Y-m-d H:00:00");
                    $end_time = $dt->format("Y-m-d H:59:59");
                    $data_query = $get_data_query($active_channels, $brand_id, $item[0]->id, $start_time, $end_time, $data_check);
                    $channel = $get_channel($data_query);
                    $mention_count = $channel['Total'];
                    $hourly_mention[$i] += $mention_count;
                }

                $main_data_hourly[$item[0]->name] = $hourly_mention;
            }

            $data_hourly = '';
            $k = 0;
            foreach ($main_data_hourly as $key => $item) {
                $data_hourly .= "{
                    label: '" . $key . "',
                    lineTension: '0.5',
                    data: [" . implode(',', $item) . "],
                    backgroundColor: '" . $data_color[$k] . "',
                    borderColor: '" . $data_color[$k] . "',
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: false
                },";

                $k++;
            }

            $main_chartLineConfig = "{
                type: 'line',
                data: {
                    labels: [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],
                    datasets: [" . $data_hourly . "]
                },
                options: {
                    responsive: true,
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                        fontFamily: 'Cambria',
                        fontStyle: 'bold'
                        }
                    },
                    scales: {
                        xAxes: [{
                            display: true,
                            ticks: {
                                fontFamily: 'Cambria'
                            }
                        }],
                        yAxes: [{
                            display: true,
                            ticks: {
                                fontFamily: 'Cambria'
                            }
                        }]
                    },
                    title: {
                        display: true,
                        text: 'Xu Hướng Tin Bài Theo Giờ',
                        fontSize: 16,
                        fontColor: '#000000',
                        fontFamily: 'Cambria'
                    }
                }
            }";
        }
        // echo "<pre>";print_r($list_chartLineConfig); exit;

        // SENTIMENT
        $list_chartStackedBarConfig = [];
        foreach ($list_object as $sub_index => $sub_brand_object) {
            $sentiment_list_labels = '';
            foreach ($sub_brand_object as $obj) {
                $sentiment_list_labels .= '"' . $obj->name . '",';
            }
            $sentiment_list_data = '';
            $sentiment_bg_color = ["#00b0f0", "#c00000", "#d9d9d9"];
            foreach (array_keys($list_sentiment[$sub_index][0]) as $index => $type) {
                $data_type = '';
                foreach ($list_sentiment[$sub_index] as $sentiment) {
                    $data_type .= $sentiment[$type]["percentage"] . ",";
                }
                $sentiment_list_data .= "{
                    label: '" . $type . "',
                    backgroundColor: '" . $sentiment_bg_color[$index] . "',
                    data: [" . $data_type . "]
                },";
            }

            $chartStackedBarConfig = "{
                type: 'horizontalBar',
                data: {
                    labels: [" . $sentiment_list_labels . "],
                    datasets: [" . $sentiment_list_data . "]
                },
                options: {
                    responsive: true,
                    legend: {
                        display: true,
                        position: 'bottom'
                    },
                    scales: {
                        xAxes: [{
                            stacked: true,
                            ticks: {
                                min: 0,
                                max: 100,
                                callback: function(value){return value+ '%'}
                            }
                        }],
                        yAxes: [{
                            stacked: true
                        }]
                    },
                    title: {
                        display: true,
                        text: 'Tỷ lệ cảm xúc',
                        fontSize: 16,
                        fontColor: '#000000'
                    },
                    plugins: {
                        datalabels: {
                            color: '#333333',
                            font: {
                            size: 10,
                            },
                            formatter: function(value, context) {
                            return value +'%'
                            }
                        }
                    },
                }
            }";

            $list_chartStackedBarConfig[] = $chartStackedBarConfig;
        }
        // echo "<pre>";print_r($list_chartStackedBarConfig); exit;

        // main
        $sentiment_list_labels = '';
        foreach ($list_object as $item) {
            if (in_array($item[0]->name, array('Eco Retreat'))) continue;
            $sentiment_list_labels .= '"' . $item[0]->name . '",';
        }
        $sentiment_list_data = '';
        $sentiment_bg_color = ["#00b0f0", "#c00000", "#d9d9d9"];
        // pre($list_sentiment);die;
        foreach (array_keys($list_sentiment[0][0]) as $index => $type) {
            $data_type = '';
            foreach ($list_sentiment as $sentiment) {
                if (in_array($sentiment[0][$type]['object_name'], array('Eco Retreat'))) continue;
                $data_type .= $sentiment[0][$type]["percentage"] . ",";
            }
            $sentiment_list_data .= "{
                label: '" . $type . "',
                backgroundColor: '" . $sentiment_bg_color[$index] . "',
                data: [" . $data_type . "]
            },";
        }
        $main_chartStackedBarConfig = "{
            type: 'horizontalBar',
            data: {
                labels: [" . $sentiment_list_labels . "],
                datasets: [" . $sentiment_list_data . "]
            },
            options: {
                responsive: true,
                legend: {
                    display: true,
                    position: 'bottom'
                },
                scales: {
                    xAxes: [{
                        stacked: true,
                        ticks: {
                            min: 0,
                            max: 100,
                            callback: function(value){return value+ '%'}
                        }
                    }],
                    yAxes: [{
                        stacked: true
                    }]
                },
                title: {
                    display: true,
                    text: 'Tỷ lệ cảm xúc',
                    fontSize: 16,
                    fontColor: '#000000'
                },
                plugins: {
                    datalabels: {
                        color: '#333333',
                        font: {
                        size: 10,
                        },
                        formatter: function(value, context) {
                        return value +'%'
                        }
                    }
                },
            }
        }";
        // print_r($main_chartStackedBarConfig); exit;

        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getDefaultStyle()->getFont()
            ->setName('Arial')
            ->setSize(14)
            ->setBold(true);

        ob_end_clean();

        // custom style with different font than the global one
        $styleArrays = array(

            'font' => array(
                //  'bold' => true,
                'color' => array('rgb' => '#492ca0'),
            ),
        );

        // apply custom style to single cell
        $objPHPExcel->getActiveSheet()->getStyle('A3')->applyFromArray($styleArrays);
        $objPHPExcel = PHPExcel_IOFactory::load(PUBLIC_PATH . "report_template/Form_2.xlsx");

        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getActiveSheet()->setTitle('Báo cáo ' . $report_type_text_text);

        // $objPHPExcel->getActiveSheet()->getColumnDimension("A")->setWidth(15);

        $styles = array(
            'borders' => array(
                'allborders' => array(
                    'style' => PHPExcel_Style_Border::BORDER_THIN
                )
            ),
            'fill' => array(
                'type' => PHPExcel_Style_Fill::FILL_SOLID,
                'color' => array('rgb' => 'FFFFFF')
            ),
            'font' => array(
                'bold' => true,
                //'color' => array('rgb' => '3696ad'),
                //'size'  => 10,
                //'name'  => 'Verdana'
            ),
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER,
            )
        );
        $styleArrays = array(

            'font' => array(
                'bold' => true,
            ),
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER,
            )
        );
        $styleSmallText = array(
            'font' => array(
                'size' => 9,
            )
        );
        $styleRedColor = array(
            'font' => array(
                'color' => array('rgb' => 'FF0000'),
            )
        );
        $styleBlueColor = array(
            'font' => array(
                'color' => array('rgb' => '0070C0'),
            )
        );
        $styleLeftAlignment = array(
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT
            )
        );
        $styleRightAlignment = array(
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_RIGHT
            )
        );
        $styleBoldText = array(
            'font' => array(
                'bold' => true,
            )
        );
        $styleAllBorders = array(
            'borders' => array(
                'allborders' => array(
                    'style' => PHPExcel_Style_Border::BORDER_THIN
                )
            )
        );
        $styleItalicText = array(
            'font' => array(
                'italic' => true,
            )
        );
        $styleArray = array(
            'borders' => array(
                'allborders' => array(
                    'style' => PHPExcel_Style_Border::BORDER_THIN
                )
            ),
            'font' => array(
                //                'bold' => true,
                'underline' => 'none',
                'color' => array('rgb' => '492ca0'),

            ),
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER,
            )
        );
        $styleText = array(
            'borders' => array(
                'allborders' => array(
                    'style' => PHPExcel_Style_Border::BORDER_THIN
                )
            ),
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER,
            )
        );
        $styleWrapText = array(
            'alignment' => array(
                'wrap' => true
            )
        );
        $styleCenterAlignment = array(
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER,
            )
        );
        $styleLinkText = array(
            'font'  => array(
                'color' => array('rgb' => '0000FF'),
                'size'  => 11
            )
        );

        $url = 'https://example.com';

        $arrContextOptions = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );


        //bảng lượng tin
        $objPHPExcel->getActiveSheet()->setCellValue('F5', strtoupper($report_type_text) . ' REPORT');
        $objPHPExcel->getActiveSheet()->setCellValue('F6', 'Ngày: ' . $date_report);
        $objPHPExcel->getActiveSheet()->setCellValue('C9', $brand_name);
        $objPHPExcel->getActiveSheet()->setCellValue('C10', 'Ngày: ' . date('d/m/Y'));
        $objPHPExcel->getActiveSheet()->setCellValue('C11', 'Báo cáo ' . $report_type_text_text);


        $i = 15;

        // TỔNG QUAN VỀ BRAND
        $col = 1;
        $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $i, 'I. THÔNG TIN CỤ THỂ VỀ ' . strtoupper($brand_name));
        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $i)->applyFromArray($styleBlueColor)->applyFromArray($styleBoldText);
        $i += 2;
        $rowCountFirstMention = $i;
        foreach ($list_channel_and_sentiment_sub_primary as $index => $detailChannel) {
            if ($index == 'Các dự án') {
                $i = $rowCountFirstMention;
                $col = 3;
            } else {
                $col = 2;
            }
            foreach ($detailChannel as $channelName => $quantity) {
                if ($channelName == 'object_name') {
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'Lượng tin');
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $i, $quantity);
                } elseif ($channelName == 'Total') {
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'Tổng');
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $i, $quantity);
                } elseif ($channelName == 'array_sentiment') {
                    continue;
                } else {
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $channelName);
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $i, $quantity);
                }
                $i++;
            }
        }

        // sentiment
        $objPHPExcel->getActiveSheet()->setCellValue('G' . $rowCountFirstMention, 'CHỈ SỐ CẢM XÚC');
        foreach ($list_channel_and_sentiment_sub_primary as $index => $detailChannel) {
            $rowCountSentiment = $rowCountFirstMention;
            if ($index == 'Các dự án') {
                $col = 'I';
            } else {
                $col = 'H';
            }
            $objPHPExcel->getActiveSheet()->setCellValue($col . $rowCountSentiment, $index);
            foreach ($detailChannel['array_sentiment'] as $sentimentName => $value) {
                $rowCountSentiment++;
                $objPHPExcel->getActiveSheet()->setCellValue('G' . $rowCountSentiment, $sentimentName);
                $objPHPExcel->getActiveSheet()->setCellValue($col . $rowCountSentiment, $value['percentage']);
            }
            $quantityPositive = $detailChannel['array_sentiment']['Positive']['quantity'];
            $quantityNegative = $detailChannel['array_sentiment']['Negative']['quantity'];
            $calculateSentimentScore = ($quantityPositive - $quantityNegative) / ($quantityPositive + $quantityNegative);
            $sentimentScore[$index] = round($calculateSentimentScore);
        }
        $rowCountSentiment += 2;
        $objPHPExcel->getActiveSheet()->setCellValue('G' . $rowCountSentiment, 'CHỈ SỐ CẢM XÚC');
        $objPHPExcel->getActiveSheet()->setCellValue('H' . $rowCountSentiment, $sentimentScore['Thương hiệu BIM và lãnh đạo']);
        $objPHPExcel->getActiveSheet()->setCellValue('I' . $rowCountSentiment, $sentimentScore['Các dự án']);
        // die;

        $i += 2;

        // BIỂU ĐỒ
        // share of voices
        $sub_index = 0;
        if ($setting_id == 169) {
            $qc = new QuickChart(array(
                'width' => 800,
                'height' => 400
            ));
        } else {
            $qc = new QuickChart(array(
                'width' => 500,
                'height' => 300
            ));
        }
        // print_r($main_chartPieConfig);die;
        $qc->setApiKey($apiKeyQuickChart);
        $qc->setConfig($main_chartPieConfig);
        $statusChartPieConfig = get_headers($qc->getShortUrl());
        if ($statusChartPieConfig && !strpos($statusChartPieConfig[0], '200')) {
            $messageChartPieConfig = "\nStatus Chart Pie In Config "
                . $setting['id']
                . '-' . $setting['type']
                . ': ' . $statusChartPieConfig[0];
            telegramAlert($token, $chatID, $messageChartPieConfig);
        }
        $pathToPieImg = PUBLIC_PATH . 'tmp/' . $setting['id'] . '_' . $setting['type'] . '_main_pie_chart.png';
        $qc->toFile($pathToPieImg);


        if ($setting_id == 169) {
            $qc = new QuickChart(array(
                'width' => 800,
                'height' => 400
            ));
        } else {
            $qc = new QuickChart(array(
                'width' => 600,
                'height' => 300
            ));
        }
        $qc->setApiKey($apiKeyQuickChart);
        $qc->setConfig($main_chartBarConfig);
        $statusChartBarConfig = get_headers($qc->getShortUrl());
        if ($statusChartBarConfig && !strpos($statusChartBarConfig[0], '200')) {
            $messageChartBarConfig = "\nStatus Chart Bar In Config "
                . $setting['id']
                . '-' . $setting['type']
                . ': ' . $statusChartBarConfig[0];
            telegramAlert($token, $chatID, $messageChartBarConfig);
        }
        $pathToBarImg = PUBLIC_PATH . 'tmp/' . $setting['id'] . '_' . $setting['type'] . '_main_bar_chart.png';
        $qc->toFile($pathToBarImg);

        if ($setting['type'] == 'daily') {
            if ($setting_id == 169) {
                $qc = new QuickChart(array(
                    'width' => 800,
                    'height' => 400
                ));
            } else {
                $qc = new QuickChart(array(
                    'width' => 600,
                    'height' => 300
                ));
            }
            $qc->setApiKey($apiKeyQuickChart);
            $qc->setConfig($main_chartLineConfig);
            $statusChartLineConfig = get_headers($qc->getShortUrl());
            if ($statusChartLineConfig && !strpos($statusChartLineConfig[0], '200')) {
                $messageChartLineConfig = "\nStatus Chart Line In Config "
                    . $setting['id']
                    . '-' . $setting['type']
                    . ': ' . $statusChartLineConfig[0];
                telegramAlert($token, $chatID, $messageChartLineConfig);
            }
            $pathToLineImg = PUBLIC_PATH . 'tmp/' . $setting['id'] . '_' . $setting['type'] . '_main_line_chart.png';
            $qc->toFile($pathToLineImg);
        }

        if ($setting_id == 169) {
            $qc = new QuickChart(array(
                'width' => 800,
                'height' => 400
            ));
        } else {
            $qc = new QuickChart(array(
                'width' => 600,
                'height' => 300
            ));
        }
        $qc->setApiKey($apiKeyQuickChart);
        $qc->setConfig($main_chartStackedBarConfig);
        $statusChartStackedBarConfig = get_headers($qc->getShortUrl());
        if ($statusChartStackedBarConfig && !strpos($statusChartStackedBarConfig[0], '200')) {
            $messageChartStackedBarConfig = "\nStatus Chart Stacked Bar In Config "
                . $setting['id']
                . '-' . $setting['type']
                . ': ' . $statusChartStackedBarConfig[0];
            telegramAlert($token, $chatID, $messageChartStackedBarConfig);
        }
        $pathToStackedBarImg = PUBLIC_PATH . 'tmp/' . $setting['id'] . '_' . $setting['type'] . '_main_stacked_bar_chart.png';
        $qc->toFile($pathToStackedBarImg);

        $objDrawing = new PHPExcel_Worksheet_Drawing();

        if (!in_array($setting_id, array(143, 144, 146))) {
            if ($setting_id == 169) {
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToPieImg);
                $objDrawing->setCoordinates('C' . $i);
                $objDrawing->setName('Share of Voice');
                $objDrawing->setDescription('Share of Voice');
                //setOffsetX works properly
                $objDrawing->setOffsetX(50);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(800);
                $objDrawing->setHeight(400);

                $i += 23;

                $objDrawing = new PHPExcel_Worksheet_Drawing();
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToBarImg);
                $objDrawing->setCoordinates('C' . $i);
                $objDrawing->setName('Lượng tin trên các nền tảng');
                //setOffsetX works properly
                $objDrawing->setOffsetX(50);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(800);
                $objDrawing->setHeight(400);
                $i += 23;
            } else {
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToPieImg);
                $objDrawing->setCoordinates('A' . $i);
                $objDrawing->setName('Share of Voice');
                $objDrawing->setDescription('Share of Voice');
                //setOffsetX works properly
                $objDrawing->setOffsetX(50);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(500);
                $objDrawing->setHeight(300);

                $objDrawing = new PHPExcel_Worksheet_Drawing();
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToBarImg);
                $objDrawing->setCoordinates('G' . $i);
                $objDrawing->setName('Lượng tin trên các nền tảng');
                //setOffsetX works properly
                $objDrawing->setOffsetX(50);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(600);
                $objDrawing->setHeight(300);
                $i += 23;
            }
        }

        if ($setting['type'] == 'daily') {
            if ($setting_id == 169) {
                $objDrawing = new PHPExcel_Worksheet_Drawing();
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToLineImg);
                $objDrawing->setCoordinates('C' . $i);
                $objDrawing->setName('Xu hướng tin bài theo giờ');
                //setOffsetX works properly
                $objDrawing->setOffsetX(25);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(800);
                $objDrawing->setHeight(400);
                $i += 23;
            } else {
                $objDrawing = new PHPExcel_Worksheet_Drawing();
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToLineImg);
                $objDrawing->setCoordinates('A' . $i);
                $objDrawing->setName('Xu hướng tin bài theo giờ');
                //setOffsetX works properly
                $objDrawing->setOffsetX(25);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(600);
                $objDrawing->setHeight(300);
            }
        }

        if ($setting_id == 169) {
            $objDrawing = new PHPExcel_Worksheet_Drawing();
            $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
            $objDrawing->setPath($pathToStackedBarImg);
            $objDrawing->setCoordinates('C' . $i);
            $objDrawing->setName('Tỷ lệ cảm xúc');
            //setOffsetX works properly
            $objDrawing->setOffsetX(50);
            $objDrawing->setOffsetY(5);
            //set width, height
            $objDrawing->setWidth(800);
            $objDrawing->setHeight(400);
            $i += 5;
        } else {
            $objDrawing = new PHPExcel_Worksheet_Drawing();
            $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
            $objDrawing->setPath($pathToStackedBarImg);
            if ($setting['type'] == 'daily') {
                $objDrawing->setCoordinates('G' . $i);
            } else {
                $objDrawing->setCoordinates('D' . $i);
            }
            $objDrawing->setName('Tỷ lệ cảm xúc');
            //setOffsetX works properly
            $objDrawing->setOffsetX(50);
            $objDrawing->setOffsetY(5);
            //set width, height
            $objDrawing->setWidth(600);
            $objDrawing->setHeight(300);
        }

        $i += 18;

        if ($setting_id == 143 || $setting_id == 144  || $setting_id == 146 || $setting_id == 169) {
            // label
            $j = max(1, $i); // Đảm bảo $j >= 1

            if (count($list_object) > 3) {
                $col = max(1, round((10 - count($list_object)) / 2)); // Đảm bảo cột >= 1
            } else {
                $col = 1;
            }
        } else {
            // label
            $j = $i;
            if (count($list_object) > 3) {
                $col = round((10 - count($list_object)) / 2);
            } else {
                $col = 1;
            }
        }

        // print_r('<pre>');
        // print_r($list_primary_quantity_all_channel);
        // foreach ($list_primary_quantity_all_channel[0] as $key => $value) {
        //     if ($i == $j) {
        //         $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, "LƯỢNG TIN");
        //         $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);
        //     } else {
        //         $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $key == "Total" ? "Tổng" : $key);
        //         $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);
        //     }
        //     $j++;
        // }
        $rowCountBeginQuantityMention = $j;
        $total_primary_quantity_all_channel = [];
        foreach ($list_object as $sub_index => $sub_brand_object) {
            if ($sub_brand_object[0]->name == 'Eco Retreat') continue;
            $col = 1;
            $j++;
            $primary_quantity_all_channel = $list_primary_quantity_all_channel[$sub_index];
            // print_r($primary_quantity_all_channel);die;
            foreach ($primary_quantity_all_channel as $key => $row) {
                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row);
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($j))->applyFromArray($j == $i ? $styles : $styleText)->applyFromArray($styleRightAlignment);
                if ($key != 'object_name') {
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $rowCountBeginQuantityMention, $key);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $rowCountBeginQuantityMention)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);
                }
                if (!$sub_index) {
                    $total_primary_quantity_all_channel[$key] = $row;
                } else {
                    $total_primary_quantity_all_channel[$key] += $row;
                }
                $col++;
            }
        }

        // subbrand
        // $col++;
        // $total_primary_quantity_all_channel = [];
        // foreach ($list_object as $sub_index => $sub_brand_object) {
        //     $j = $i;
        //     $primary_quantity_all_channel = $list_primary_quantity_all_channel[$sub_index];
        //     foreach ($primary_quantity_all_channel as $key => $row) {
        //         $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row);
        //         $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($j))->applyFromArray($j == $i ? $styles : $styleText)->applyFromArray($styleRightAlignment);
        //         $j += 1;
        //         if (!$sub_index) {
        //             $total_primary_quantity_all_channel[$key] = $row;
        //         } else {
        //             $total_primary_quantity_all_channel[$key] += $row;
        //         }
        //     }
        //     $col++;
        // }

        // total
        // $j = $i;
        // print_r($total_primary_quantity_all_channel);
        // foreach ($total_primary_quantity_all_channel as $key => $row) {
        //     // $rate = $total_primary_quantity_all_channel['Total'] ? round(100 * $row/$total_primary_quantity_all_channel['Total'],1) : 0;
        //     $rate = $total_primary_quantity_all_channel['Total'] ? number_format(round(100 * $row / $total_primary_quantity_all_channel['Total'], 1), 1) : 0;
        //     if ($key == "object_name") {
        //         $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $brand_name);
        //         $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders);
        //         $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, "Tỷ lệ");
        //         $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders);
        //     } else {
        //         $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row);
        //         $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleAllBorders)->applyFromArray($styleRightAlignment);
        //         $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, $rate . "%");
        //         $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders)->applyFromArray($styleRightAlignment)->applyFromArray($styleBlueColor);
        //     }
        //     $j++;
        // }
        $col = 1;
        $j++;
        // print_r($total_primary_quantity_all_channel);die;;
        foreach ($total_primary_quantity_all_channel as $key => $row) {
            // $rate = $total_primary_quantity_all_channel['Total'] ? round(100 * $row/$total_primary_quantity_all_channel['Total'],1) : 0;
            $rate = $total_primary_quantity_all_channel['Total'] ? number_format(round(100 * $row / $total_primary_quantity_all_channel['Total'], 1), 1) : 0;
            if ($key == "object_name") {
                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $brand_name);
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders);
                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($j + 1), "Tỷ lệ");
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($j + 1))->applyFromArray($styleArrays)->applyFromArray($styleAllBorders);
            } else {
                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row);
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleAllBorders)->applyFromArray($styleRightAlignment);
                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($j + 1), $rate . "%");
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($j + 1))->applyFromArray($styleArrays)->applyFromArray($styleAllBorders)->applyFromArray($styleRightAlignment)->applyFromArray($styleBlueColor);
            }
            // $j++;
            $col++;
        }
        $j += 4;

        //bảng sentiment
        if ($setting_id == 143 || $setting_id == 144  || $setting_id == 146 || $setting_id == 169) {
            if (count($list_object) > 3) {
                $col = max(0, round((12 - count($list_object)) / 2)) + 1; //
                $j++;
            } else {
                $col = max(4 + count($list_object), 8) + 1; //
                $j = $i;
            }
        } else {
            if (count($list_object) > 3) {
                $col = round((12 - count($list_object)) / 2);
                $j++;
            } else {
                $col = max(4 + count($list_object), 8);
                $j = $i;
            }
        }
        // label
        $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, "SENTIMENT");
        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders);
        $col_j = $col + 1;
        foreach ($list_primary_array_sentiment[0] as $key => $row) {
            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $key);
            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleText);
            $col_j++;
        }
        $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, "CHỈ SỐ CẢM XÚC");
        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders);

        $col_j = $col;
        $total_primary_array_sentiment = [];
        $total_primary_array_sentiment['Rate'] = 0;
        $j++;

        if ($setting_id == 94 && $setting['type'] == 'weekly') {
            $arrayNeedToConvertJson = [];
            for ($indexOfArray = 0; $indexOfArray < count($list_primary_quantity_all_channel); $indexOfArray++) {
                $arrayNeedToConvertJson[$list_primary_quantity_all_channel[$indexOfArray]['object_name']] = [
                    'channel' => [],
                    'sentiment' => [
                        'positive' => 0,
                        'neutral' => 0,
                        'negative' => 0,
                        'sentiment_rate' => 0,
                    ],
                ];
                foreach ($active_channels as $channelName => $active) {
                    if (!$active) continue;
                    $sentiment_rate = round(($list_primary_array_sentiment[$indexOfArray]['Positive']['percentage'] - $list_primary_array_sentiment[$indexOfArray]['Negative']['percentage']) / max($list_primary_array_sentiment[$indexOfArray]['Positive']['percentage'] + $list_primary_array_sentiment[$indexOfArray]['Negative']['percentage'], 1), 1);
                    $arrayNeedToConvertJson[$list_primary_quantity_all_channel[$indexOfArray]['object_name']]['channel'][$channelName] =
                        $list_primary_quantity_all_channel[$indexOfArray][$channelName];

                    $arrayNeedToConvertJson[$list_primary_quantity_all_channel[$indexOfArray]['object_name']]['sentiment']['positive'] =
                        $list_primary_array_sentiment[$indexOfArray]['Positive']['quantity'];

                    $arrayNeedToConvertJson[$list_primary_quantity_all_channel[$indexOfArray]['object_name']]['sentiment']['neutral'] =
                        $list_primary_array_sentiment[$indexOfArray]['Neutral']['quantity'];

                    $arrayNeedToConvertJson[$list_primary_quantity_all_channel[$indexOfArray]['object_name']]['sentiment']['negative'] =
                        $list_primary_array_sentiment[$indexOfArray]['Negative']['quantity'];

                    $arrayNeedToConvertJson[$list_primary_quantity_all_channel[$indexOfArray]['object_name']]['sentiment']['sentiment_rate'] =
                        $sentiment_rate;
                }
            }
            $arrayNeedToConvertJson[$brand_name] = [
                'channel' => [],
            ];
            foreach ($total_primary_quantity_all_channel as $key => $value) {
                if ($key == 'object_name' || $key == 'Total') continue;
                $arrayNeedToConvertJson[$brand_name]['channel'][$key] = $value;
            }
            // print_r($list_primary_array_sentiment);
            $jsonMentionAndSentiment = json_encode($arrayNeedToConvertJson, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

            // Write in the file
            // $fp = fopen($pathFileJson, 'w');
            // fwrite($fp, $jsonMentionAndSentiment);
            // fclose($fp);
            // print_r($jsonMentionAndSentiment);
        }

        foreach ($list_object as $sub_index => $sub_brand_object) {
            if ($sub_brand_object[0]->name == 'Eco Retreat') continue;
            $col_j = $col;
            $primary_array_sentiment = $list_primary_array_sentiment[$sub_index];
            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $sub_brand_object[0]->name);
            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders);
            $col_j++;
            foreach ($primary_array_sentiment as $key => $row) {
                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row['quantity']);
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                if (!$sub_index) {
                    $total_primary_array_sentiment[$key] = $row['quantity'];
                } else {
                    $total_primary_array_sentiment[$key] += $row['quantity'];
                }
                $col_j++;
            }

            $sentiment_rate = round(($primary_array_sentiment['Positive']['percentage'] - $primary_array_sentiment['Negative']['percentage']) / max($primary_array_sentiment['Positive']['percentage'] + $primary_array_sentiment['Negative']['percentage'], 1), 1);
            $total_primary_array_sentiment['Rate'] += $sentiment_rate;
            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $sentiment_rate);
            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleAllBorders)->applyFromArray($styleRightAlignment);
            $j++;
        }
        $col_j = $col;
        // total
        $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $brand_name);
        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders);
        $col_j++;
        foreach ($total_primary_array_sentiment as $key => $row) {
            if ($key == "Rate") continue;
            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row);
            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders)->applyFromArray($styleRightAlignment);
            $col_j++;
        }
        $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, round($total_primary_array_sentiment['Rate']), 1);
        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArrays)->applyFromArray($styleAllBorders)->applyFromArray($styleRightAlignment);

        //bảng nguồn tin tiêu cực lưu ý
        if (count($list_object) > 3) {
            $i = $j + 2;
        } else {
            $i += max(count($list_object) + 1, count($total_primary_quantity_all_channel)) + 1;
        }


        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, "NGUỒN TIN TIÊU CỰC ĐÁNG LƯU Ý");
        $objPHPExcel->getActiveSheet()->getStyle('B' . ($i))->applyFromArray($styleArrays)->applyFromArray($styleRedColor);

        $i += 2;

        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'Đối tượng');
        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Nền tảng');
        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, 'Top nguồn');
        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, 'Nội dung');
        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styles);
        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles);
        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Time');
        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, 'Sentiment');
        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, 'Like');
        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, 'Share/View');
        $objPHPExcel->getActiveSheet()->getStyle('K' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, 'Comment');
        $objPHPExcel->getActiveSheet()->getStyle('L' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, 'Tổng tương tác');
        $objPHPExcel->getActiveSheet()->getStyle('M' . $i)->applyFromArray($styles);

        $i += 1;
        foreach ($list_object as $sub_index => $sub_brand_object) {
            $primary_data_social_negative = $list_primary_data_social_negative[$sub_index];
            $primary_data_ifollow_negative = $list_primary_data_ifollow_negative[$sub_index];
            $primary_data_paper_negative = $list_primary_data_paper_negative[$sub_index];
            $primary_data_youtube_negative = $list_primary_data_youtube_negative[$sub_index];
            $primary_data_tiktok_negative = $list_primary_data_tiktok_negative[$sub_index];
            $primary_data_threads_negative = $list_primary_data_threads_negative[$sub_index];
            $primary_data_twitter_negative = $list_primary_data_twitter_negative[$sub_index];
            $primary_data_instagram_negative = $list_primary_data_instagram_negative[$sub_index];
            $primary_data_reviewmap_negative = $list_primary_data_reviewmap_negative[$sub_index];
            $primary_data_zalo_negative = $list_primary_data_zalo_negative[$sub_index];
            $primary_data_ecommerce_negative = $list_primary_data_ecommerce_negative[$sub_index];
            $len = -1 + count($primary_data_social_negative) + count($primary_data_ifollow_negative) + count($primary_data_youtube_negative) + count($primary_data_tiktok_negative) + count($primary_data_twitter_negative) + count($primary_data_instagram_negative) + count($primary_data_reviewmap_negative) + count($primary_data_zalo_negative) + count($primary_data_ecommerce_negative);
            if ($len < 0) continue;

            $objPHPExcel->getActiveSheet()->mergeCells("B" . $i . ":B" . ($i + $len));
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $sub_brand_object[0]->name);
            $objPHPExcel->getActiveSheet()->getStyle('B' . $i . ':B' . ($i + $len))->applyFromArray($styles);

            // facebook
            if (count($primary_data_social_negative)) {
                foreach ($primary_data_social_negative as $key => $row) {
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . ($i), "Facebook");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $url = str_replace('https://', '', 'https://facebook.com/' . $row->_source->fb_id);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->page_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(3, $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->message);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->_source->total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row->_source->total_share);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, $row->_source->total_like + $row->_source->total_share + $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                    $j += 1;
                }
            }

            //news
            if (count($primary_data_ifollow_negative)) {
                $j += count($primary_data_ifollow_negative);
                foreach ($primary_data_ifollow_negative as $key => $row) {
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "News");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $url = str_replace('https://', '', 'https://' . $row->_source->web_url);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->web_page_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(3, $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->web_title);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->web_content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->web_state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            // paper
            if (count($primary_data_paper_negative)) {
                $j += count($primary_data_paper_negative);
                foreach ($primary_data_paper_negative as $key => $row) {
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, "Paper");
                    $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $url = 'http://data.monitaz.vn/detail/index/' . $row->_id . '?_channel=2';
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $row->_source->paper_page_name);
                    // $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->getAlignment()->setWrapText(true);
                    $objPHPExcel->getActiveSheet()->getStyle('C' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($url);

                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->paper_title);
                    $objPHPExcel->getActiveSheet()->mergeCells("D" . $i . ":F" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    // $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->getAlignment()->setWrapText(true);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    //            $objPHPExcel->getActiveSheet()->getStyle('C'.$i)->getFont()->setUnderline(true);
                    $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(3, $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, extractTimeFromDateString($row->_source->paper_content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $state_text[$row->_source->paper_state]);
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //youtube
            if (count($primary_data_youtube_negative)) {
                $j += count($primary_data_youtube_negative);
                foreach ($primary_data_youtube_negative as $key => $row) {
                    $row = (array)$row->_source;
                    // print_r('<pre>');
                    // print_r($row);
                    // die();
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "Youtube");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $url = str_replace('https://', '', 'https://youtube.com/' . $row['page_id']);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row['page_name']);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(3, $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row['message']);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row['content_created']));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row['state']]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row['total_like']);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row['total_share']);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //tiktok
            if (count($primary_data_tiktok_negative)) {
                $j += count($primary_data_youtube_negative);
                foreach ($primary_data_tiktok_negative as $key => $row) {
                    $row = (array)$row->_source;
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "Tiktok");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $url = str_replace('https://', '', 'https://tiktok.com/@' . $row['page_id'] . '/video/' . $row['fb_id']);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row['page_name']);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row['message']);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row['content_created']));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row['state']]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row['total_like']);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row['total_share']);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //twitter
            if (count($primary_data_twitter_negative)) {
                $j += count($primary_data_tiktok_negative);
                foreach ($primary_data_twitter_negative as $key => $row) {
                    $row = (array)$row->_source;
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "Twitter");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $url = str_replace('https://', '', 'https://twitter.com/' . $row['page_id'] . '/status/' . $row['fb_id']);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row['page_name']);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row['message']);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row['content_created']));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row['state']]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row['total_like']);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row['total_share']);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //reviewmap
            if (count($primary_data_reviewmap_negative)) {
                $j += count($primary_data_reviewmap_negative);
                foreach ($primary_data_reviewmap_negative as $key => $row) {
                    $row = (array)$row->_source;
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "Review Map");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    // $url = str_replace('https://', '', 'https://https://play.google.com/store/apps/details?id=' . $row['page_id'] . '&hl=vi&gl=US&reviewId=' . $row['fb_id']);
                    $url = 'https://www.google.com/maps/contrib/' . $row['content_from_id'] . '/place/' . $row['page_id'];
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row['page_name']);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row['message']);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row['content_created']));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row['state']]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row['total_like']);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row['total_share'] . '/5 sao');
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //zalo
            if (count($primary_data_zalo_negative)) {
                $j += count($primary_data_zalo_negative);
                foreach ($primary_data_zalo_negative as $key => $row) {
                    $row = (array)$row->_source;
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "Zalo");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $link_user = $domain_zalo_url . "/detail?content_from_id=" . $row['content_from_id'] . "&fb_id=" . $row['fb_id'];
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row['page_name']);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $link_user);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row['message']);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl(curlLinkZalo($link_user));

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row['content_created']));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row['state']]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row['total_like']);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row['total_share']);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //ecommerce
            if (count($primary_data_ecommerce_negative)) {
                $j += count($primary_data_ecommerce_negative);
                foreach ($primary_data_ecommerce_negative as $key => $row) {
                    $row = (array)$row->_source;
                    if ($row['type'] == 0) {
                        $star = $row['rate_count'];
                        $type_mention = "Post";

                        if ($row['type_ecomerce'] == 1) {
                            $url = 'https://shopee.vn/' . $row['content_from_name'] . '-i.' . $row['content_from_id'] . '.' . $row['fb_id'];
                            $url_page_name = 'https://shopee.vn/' . $row['content_from_name'];
                        } else {
                            $url = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row['fb_id'];
                            $url_page_name = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row['fb_id'];
                        }
                    } else {
                        $star = $row['star'];
                        $type_mention = "Review";
                        if ($row['type_ecomerce'] == 1) {
                            $url = 'https://shopee.vn/' . $row['page_name'] . '-i.' . $row['page_id'] . '.' . $row['fb_parent_id'];
                            $url_page_name = 'https://shopee.vn/' . $row['page_name'];
                        } else {
                            $url = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row['fb_parent_id'];
                            $url_page_name = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row['fb_parent_id'];
                        }
                    }

                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "Ecommerce");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $url = str_replace('https://', '', 'https://twitter.com/' . $row['page_id'] . '/status/' . $row['fb_id']);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row['page_name']);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl($url_page_name);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row['message']);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl($url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row['content_created']));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row['state']]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row['total_like']);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $star . '/5 sao');
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    // $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row['total_share']);
                    // $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //Threads
            if (count($primary_data_threads_negative)) {
                $j += count($primary_data_threads_negative);
                foreach ($primary_data_threads_negative as $key => $row) {
                    $row = (array)$row->_source;

                    $url = "https://www.threads.net/@" . $row['content_from_id'] . "/post/" . $row['fb_id'];
                    $url_page_name = "https://www.threads.net/@" . $row['content_from_id'];

                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "Threads");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row['page_name']);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url_page_name);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row['message']);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row['content_created']));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row['state']]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row['total_like']);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row['total_share']);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //instagram
            if (count($primary_data_instagram_negative)) {
                $j += count($primary_data_instagram_negative);
                foreach ($primary_data_instagram_negative as $key => $row) {
                    $row = (array)$row->_source;
                    $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, "Instagram");
                    $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);
                    $url = str_replace('https://', '', 'https://instagram.com/' . $row['page_id'] . '/p/' . $row['fb_id']);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row['page_name']);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row['message']);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styles);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row['content_created']));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row['state']]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row['total_like']);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row['total_share']);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }
        }

        //top nguồn tin
        $i += 1;
        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'TOP NGUỒN TIN VỀ ' . strtoupper($brand_name));
        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleBoldText);
        $i += 2;
        $rowCountBeginTopSource = $i;
        // label
        $col = 1;
        $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($i), 'Đối tượng');
        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($i))->applyFromArray($styles)->getAlignment();
        // $col = 2;
        // foreach ($list_object as $sub_index => $sub_brand_object) {
        //     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($i), $sub_brand_object[0]->name);
        //     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($i))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
        //
        //     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($i), 'Số lượng');
        //     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($i))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
        //     $col += 2;
        // }
        foreach ($list_object as $sub_index => $sub_brand_object) {
            $rowCountBeginTopSourceObject = $i;
            $col = 2;
            foreach ($active_channels as $channel_name => $is_active) {
                // $col++;
                $j = $rowCountBeginTopSourceObject + 1;
                // top nguồn tin về social
                if ($channel_name == 'Facebook' && $is_active) {
                    $max_len = 0;
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource), $channel_name);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource), 'Số lượng');
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

                    $list_social_top_source = $list_list_social_top_source[$sub_index];
                    $len = count($list_social_top_source);
                    if ($len) {
                        foreach ($list_social_top_source as $key => $row) {
                            $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $sub_brand_object[0]->name);
                            $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                            $url = str_replace('https://', '', 'https://facebook.com/' . (isset($row->key) ? $row->key : ''));
                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row->page_name);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col, $j)->getHyperlink()->setUrl('https://' . $url);

                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, $row->doc_count);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                            $j++;
                        }
                        if ($len > $max_len) $max_len = $len;
                        $i += max($max_len, 1);
                    }
                    $col += 2;
                }
                // top nguồn tin về youtube
                if ($channel_name == 'Youtube' && $is_active) {
                    $max_len = 0;
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource), $channel_name);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource), 'Số lượng');
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

                    $list_youtube_top_source = $list_list_youtube_top_source[$sub_index];
                    $len = count($list_youtube_top_source);
                    if ($len) {
                        foreach ($list_youtube_top_source as $key => $row) {
                            $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $sub_brand_object[0]->name);
                            $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                            $url = str_replace('https://', '', 'https://youtube.com/' . $row['page_id']);
                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row['page_name']);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col, $j)->getHyperlink()->setUrl('https://' . $url);

                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, $row['count']);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                            $j++;
                        }
                        if ($len > $max_len) $max_len = $len;
                        $i += max($max_len, 1);
                    }
                    $col += 2;
                }
                // top nguồn tin về news
                if ($channel_name == 'Ifollow' && $is_active) {
                    $max_len = 0;
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource), $channel_name);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource), 'Số lượng');
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
                    $list_ifollow_top_source = $list_list_ifollow_top_source[$sub_index];
                    $len = count($list_ifollow_top_source);
                    if ($len) {
                        foreach ($list_ifollow_top_source as $key => $row) {
                            $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $sub_brand_object[0]->name);
                            $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                            $url = str_replace('https://', '', 'https://' . $row->web_page_name);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row->web_page_name);
                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col, $j)->getHyperlink()->setUrl('https://' . $url);

                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, $row->doc_count);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                            $j++;
                        }
                        if ($len > $max_len) $max_len = $len;
                        $i += max($max_len, 1);
                    }
                    $col += 2;
                }
                // top nguồn tin paper
                if ($channel_name == 'Paper' && $is_active) {
                    $max_len = 0;
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource), $channel_name);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource), 'Số lượng');
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

                    $list_paper_top_source = $list_list_paper_top_source[$sub_index];
                    $len = count($list_paper_top_source);
                    if ($len) {
                        foreach ($list_paper_top_source as $key => $row) {
                            $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $sub_brand_object[0]->name);
                            $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                            $url = str_replace('https://', '', 'https://' . $row->paper_page_name);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row->paper_page_name);
                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col, $j)->getHyperlink()->setUrl('https://' . $url);

                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, $row->doc_count);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                            $j++;
                        }
                        if ($len > $max_len) $max_len = $len;
                        $i += max($max_len, 1);
                    }
                    $col += 2;
                }
                // top nguồn tin về tiktok
                if ($channel_name == 'Tiktok' && $is_active) {
                    $max_len = 0;
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource), $channel_name);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource), 'Số lượng');
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . ($rowCountBeginTopSource))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

                    $list_tiktok_top_source = $list_list_tiktok_top_source[$sub_index];
                    $len = count($list_tiktok_top_source);
                    if ($len) {
                        foreach ($list_tiktok_top_source as $key => $row) {
                            $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $sub_brand_object[0]->name);
                            $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                            $url = str_replace('https://', '', 'https://tiktok.com/@' . $row['page_id']);
                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row['page_name']);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col, $j)->getHyperlink()->setUrl('https://' . $url);

                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, $row['count']);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                            $j++;
                        }
                        if ($len > $max_len) $max_len = $len;
                        $i += max($max_len, 1);
                    }
                    $col += 2;
                }
                // top nguồn tin về twitter
                if ($channel_name == 'Twitter' && $is_active) {
                    $max_len = 0;
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $channel_name);
                    $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles);
                    $list_twitter_top_source = $list_list_twitter_top_source[$sub_index];
                    $len = count($list_twitter_top_source);
                    if ($len) {
                        foreach ($list_twitter_top_source as $key => $row) {
                            $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $sub_brand_object[0]->name);
                            $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                            $url = str_replace('https://', '', 'https://twitter.com/' . $row['page_id'] . '/status/' . $row['fb_id']);
                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row['page_name']);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col, $j)->getHyperlink()->setUrl('https://' . $url);

                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, $row['count']);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                            $j++;
                        }
                        if ($len > $max_len) $max_len = $len;
                        $i += max($max_len, 1);
                    }
                    $col += 2;
                }
                // top nguồn tin về instagram
                if ($channel_name == 'Instagram' && $is_active) {
                    $max_len = 0;
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $channel_name);
                    $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles);
                    $list_instagram_top_source = $list_list_instagram_top_source[$sub_index];
                    $len = count($list_instagram_top_source);
                    if ($len) {
                        foreach ($list_instagram_top_source as $key => $row) {
                            $objPHPExcel->getActiveSheet()->setCellValue('B' . ($j), $sub_brand_object[0]->name);
                            $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->applyFromArray($styles)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

                            $url = str_replace('https://', '', 'https://instagram.com/' . $row['page_id']);
                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col) . $j, $row['page_name']);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col, $j)->getHyperlink()->setUrl('https://' . $url);

                            $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j, $row['count']);
                            $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                            $j++;
                        }
                        if ($len > $max_len) $max_len = $len;
                        $i += max($max_len, 1);
                    }
                    $col += 2;
                }
            }
        }

        // $i += 1;
        // $col = 2;
        //
        // foreach ($active_channels as $channel_name => $is_active) {
        //     $col_j = $col;
        //     // top nguồn tin về social
        //     if ($channel_name == 'Facebook' && $is_active) {
        //         $objPHPExcel->getActiveSheet()->setCellValue('B' . ($i), $channel_name);
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i))->applyFromArray($styles);
        //         $max_len = 0;
        //         foreach ($list_object as $sub_index => $sub_brand_object) {
        //             $j = $i;
        //             $list_social_top_source = $list_list_social_top_source[$sub_index];
        //             $len = count($list_social_top_source);
        //             if ($len) {
        //                 foreach ($list_social_top_source as $key => $row) {
        //                     $url = str_replace('https://', '', 'https://facebook.com/' . (isset($row->key) ? $row->key : ''));
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row->page_name);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
        //                     $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col_j, $j)->getHyperlink()->setUrl('https://' . $url);
        //
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j, $row->doc_count);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
        //                     $j += 1;
        //                 }
        //                 if ($len > $max_len) $max_len = $len;
        //             }
        //             $col_j += 2;
        //         }
        //         if ($max_len) {
        //             $objPHPExcel->getActiveSheet()->mergeCells('B' . ($i) . ':B' . ($i - 1 + $max_len));
        //             $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':B' . ($i - 1 + $max_len))->applyFromArray($styles);
        //         } else {
        //             $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . $i);
        //         }
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . max($i - 1 + $max_len, $i))->applyFromArray($styleAllBorders);
        //         $i += max($max_len, 1);
        //     }
        //     // top nguồn tin về youtube
        //     if ($channel_name == 'Youtube' && $is_active) {
        //         $objPHPExcel->getActiveSheet()->setCellValue('B' . ($i), $channel_name);
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i))->applyFromArray($styles);
        //         $max_len = 0;
        //         foreach ($list_object as $sub_index => $sub_brand_object) {
        //             $j = $i;
        //             $list_youtube_top_source = $list_list_youtube_top_source[$sub_index];
        //             $len = count($list_youtube_top_source);
        //             if ($len) {
        //                 foreach ($list_youtube_top_source as $key => $row) {
        //                     $url = str_replace('https://', '', 'https://youtube.com/' . $row['page_id']);
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row['page_name']);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
        //                     $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col_j, $j)->getHyperlink()->setUrl('https://' . $url);
        //
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j, $row['count']);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
        //                     $j += 1;
        //                 }
        //                 if ($len > $max_len) $max_len = $len;
        //             }
        //             $col_j += 2;
        //         }
        //         if ($max_len) {
        //             $objPHPExcel->getActiveSheet()->mergeCells('B' . ($i) . ':B' . ($i - 1 + $max_len));
        //             $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':B' . ($i - 1 + $max_len))->applyFromArray($styles);
        //         } else {
        //             $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . $i);
        //         }
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . max($i - 1 + $max_len, $i))->applyFromArray($styleAllBorders);
        //         $i += max($max_len, 1);
        //     }
        //     // top nguồn tin về news
        //     if ($channel_name == 'Ifollow' && $is_active) {
        //         $objPHPExcel->getActiveSheet()->setCellValue('B' . ($i), $channel_name);
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i))->applyFromArray($styles);
        //         $max_len = 0;
        //         foreach ($list_object as $sub_index => $sub_brand_object) {
        //             $j = $i;
        //             $list_ifollow_top_source = $list_list_ifollow_top_source[$sub_index];
        //             $len = count($list_ifollow_top_source);
        //             if ($len) {
        //                 foreach ($list_ifollow_top_source as $key => $row) {
        //                     $url = str_replace('https://', '', 'https://' . $row->web_page_name);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row->web_page_name);
        //                     $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col_j, $j)->getHyperlink()->setUrl('https://' . $url);
        //
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j, $row->doc_count);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
        //                     $j += 1;
        //                 }
        //                 if ($len > $max_len) $max_len = $len;
        //             }
        //             $col_j += 2;
        //         }
        //         if ($max_len) {
        //             $objPHPExcel->getActiveSheet()->mergeCells('B' . ($i) . ':B' . ($i - 1 + $max_len));
        //             $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':B' . ($i - 1 + $max_len))->applyFromArray($styles);
        //         } else {
        //             $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . $i);
        //         }
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . max($i - 1 + $max_len, $i))->applyFromArray($styleAllBorders);
        //         $i += max($max_len, 1);
        //     }
        //     // top nguồn tương tác paper
        //     if ($channel_name == 'Paper' && $is_active) {
        //         $objPHPExcel->getActiveSheet()->setCellValue('B' . ($i), $channel_name);
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i))->applyFromArray($styles);
        //         $max_len = 0;
        //         foreach ($list_object as $sub_index => $sub_brand_object) {
        //             $j = $i;
        //             $list_paper_top_source = $list_list_paper_top_source[$sub_index];
        //             $len = count($list_paper_top_source);
        //             if ($len) {
        //                 foreach ($list_paper_top_source as $key => $row) {
        //                     $url = str_replace('https://', '', 'https://' . $row->paper_page_name);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row->paper_page_name);
        //                     $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col_j, $j)->getHyperlink()->setUrl('https://' . $url);
        //
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j, $row->doc_count);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
        //                     $j += 1;
        //                 }
        //                 if ($len > $max_len) $max_len = $len;
        //             }
        //             $col_j += 2;
        //         }
        //         if ($max_len) {
        //             $objPHPExcel->getActiveSheet()->mergeCells('B' . ($i) . ':B' . ($i - 1 + $max_len));
        //             $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':B' . ($i - 1 + $max_len))->applyFromArray($styles);
        //         } else {
        //             $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . $i);
        //         }
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . max($i - 1 + $max_len, $i))->applyFromArray($styleAllBorders);
        //         $i += max($max_len, 1);
        //     }
        //     // top nguồn tin về tiktok
        //     if ($channel_name == 'Tiktok' && $is_active) {
        //         $objPHPExcel->getActiveSheet()->setCellValue('B' . ($i), $channel_name);
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i))->applyFromArray($styles);
        //         $max_len = 0;
        //         foreach ($list_object as $sub_index => $sub_brand_object) {
        //             $j = $i;
        //             $list_tiktok_top_source = $list_list_tiktok_top_source[$sub_index];
        //             $len = count($list_tiktok_top_source);
        //             if ($len) {
        //                 foreach ($list_tiktok_top_source as $key => $row) {
        //                     $url = str_replace('https://', '', 'https://tiktok.com/@' . $row['page_id']);
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row['page_name']);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
        //                     $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col_j, $j)->getHyperlink()->setUrl('https://' . $url);
        //
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j, $row['count']);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
        //                     $j += 1;
        //                 }
        //                 if ($len > $max_len) $max_len = $len;
        //             }
        //             $col_j += 2;
        //         }
        //         if ($max_len) {
        //             $objPHPExcel->getActiveSheet()->mergeCells('B' . ($i) . ':B' . ($i - 1 + $max_len));
        //             $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':B' . ($i - 1 + $max_len))->applyFromArray($styles);
        //         } else {
        //             $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . $i);
        //         }
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . max($i - 1 + $max_len, $i))->applyFromArray($styleAllBorders);
        //         $i += max($max_len, 1);
        //     }
        //     // top nguồn tin về twitter
        //     if ($channel_name == 'Twitter' && $is_active) {
        //         $objPHPExcel->getActiveSheet()->setCellValue('B' . ($i), $channel_name);
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i))->applyFromArray($styles);
        //         $max_len = 0;
        //         foreach ($list_object as $sub_index => $sub_brand_object) {
        //             $j = $i;
        //             $list_twitter_top_source = $list_list_twitter_top_source[$sub_index];
        //             $len = count($list_twitter_top_source);
        //             if ($len) {
        //                 foreach ($list_twitter_top_source as $key => $row) {
        //                     $url = str_replace('https://', '', 'https://twitter.com/' . $row['page_id'] . '/status/' . $row['fb_id']);
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row['page_name']);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
        //                     $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col_j, $j)->getHyperlink()->setUrl('https://' . $url);
        //
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j, $row['count']);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
        //                     $j += 1;
        //                 }
        //                 if ($len > $max_len) $max_len = $len;
        //             }
        //             $col_j += 2;
        //         }
        //         if ($max_len) {
        //             $objPHPExcel->getActiveSheet()->mergeCells('B' . ($i) . ':B' . ($i - 1 + $max_len));
        //             $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':B' . ($i - 1 + $max_len))->applyFromArray($styles);
        //         } else {
        //             $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . $i);
        //         }
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . max($i - 1 + $max_len, $i))->applyFromArray($styleAllBorders);
        //         $i += max($max_len, 1);
        //     }
        //     // top nguồn tin về instagram
        //     if ($channel_name == 'Instagram' && $is_active) {
        //         $objPHPExcel->getActiveSheet()->setCellValue('B' . ($i), $channel_name);
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i))->applyFromArray($styles);
        //         $max_len = 0;
        //         foreach ($list_object as $sub_index => $sub_brand_object) {
        //             $j = $i;
        //             $list_instagram_top_source = $list_list_instagram_top_source[$sub_index];
        //             $len = count($list_instagram_top_source);
        //             if ($len) {
        //                 foreach ($list_instagram_top_source as $key => $row) {
        //                     $url = str_replace('https://', '', 'https://instagram.com/' . $row['page_id']);
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j, $row['page_name']);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j) . $j)->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
        //                     $objPHPExcel->getActiveSheet()->getCellByColumnAndRow($col_j, $j)->getHyperlink()->setUrl('https://' . $url);
        //
        //                     $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j, $row['count']);
        //                     $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col_j + 1) . $j)->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
        //                     $j += 1;
        //                 }
        //                 if ($len > $max_len) $max_len = $len;
        //             }
        //             $col_j += 2;
        //         }
        //         if ($max_len) {
        //             $objPHPExcel->getActiveSheet()->mergeCells('B' . ($i) . ':B' . ($i - 1 + $max_len));
        //             $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':B' . ($i - 1 + $max_len))->applyFromArray($styles);
        //         } else {
        //             $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . $i);
        //         }
        //         $objPHPExcel->getActiveSheet()->getStyle('B' . ($i) . ':' . PHPExcel_Cell::stringFromColumnIndex($col_j - 1) . max($i - 1 + $max_len, $i))->applyFromArray($styleAllBorders);
        //         $i += max($max_len, 1);
        //     }
        // }

        $i += 5;
        //bảng top nguồn tương tác
        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'TOP NGUỒN TƯƠNG TÁC VỀ ' . strtoupper($brand_name));
        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleArrays);

        $i += 2;
        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'Đối tượng');
        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Nền tảng');
        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, 'Top nguồn');
        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, 'Nội dung');
        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styles);
        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles);
        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Time');
        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, 'Sentiment');
        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, 'Like');
        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, 'Share/View');
        $objPHPExcel->getActiveSheet()->getStyle('K' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, 'Comment');
        $objPHPExcel->getActiveSheet()->getStyle('L' . $i)->applyFromArray($styles);

        $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, 'Tổng tương tác');
        $objPHPExcel->getActiveSheet()->getStyle('M' . $i)->applyFromArray($styles);

        $i += 1;
        foreach ($list_object as $sub_index => $sub_brand_object) {
            $j = $i;
            $data_facebook_top_reaction = $list_data_facebook_top_reaction[$sub_index];
            $data_youtube_top_reaction = $list_data_youtube_top_reaction[$sub_index];
            $data_ifollow_top_reaction = $list_data_ifollow_top_reaction[$sub_index];
            $data_paper_top_reaction = $list_data_paper_top_reaction[$sub_index];
            $data_tiktok_top_reaction = $list_data_tiktok_top_reaction[$sub_index];
            $data_twitter_top_reaction = $list_data_twitter_top_reaction[$sub_index];
            $data_reviewapp_top_reaction = $list_data_reviewapp_top_reaction[$sub_index];
            $data_instagram_top_reaction = $list_data_instagram_top_reaction[$sub_index];

            if (
                count($data_facebook_top_reaction) <= 0 &&
                count($data_youtube_top_reaction) <= 0 &&
                count($data_ifollow_top_reaction) <= 0 &&
                count($data_paper_top_reaction) <= 0 &&
                count($data_tiktok_top_reaction) <= 0 &&
                count($data_twitter_top_reaction) <= 0 &&
                count($data_reviewapp_top_reaction) <= 0 &&
                count($data_instagram_top_reaction) <= 0
            ) {
                continue;
            }

            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $sub_brand_object[0]->name);
            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styles);

            //bảng top nguồn tương tác về đối tượng trên facebook
            $fb_len = count($data_facebook_top_reaction);
            if ($fb_len) {
                // echo "fb starts at: ", $i, "-", $i-1+$fb_len, "\r\n";
                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Facebook');
                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':C' . ($i - 1 + $fb_len));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i - 1 + $fb_len))->applyFromArray($styleAllBorders);

                foreach ($data_facebook_top_reaction as $key => $row) {

                    $url = str_replace('https://', '', 'https://facebook.com/' . $row->_source->fb_id);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . ($i), $row->_source->content_from_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . ($i))->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->mergeCells("E" . ($i) . ":G" . ($i));
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . ($i), $row->_source->message);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . ($i))->getHyperlink()->setUrl('https://' . $url);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styleArray);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . ($i), extractTimeFromDateString($row->_source->content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . ($i), $state_text[$row->_source->state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . ($i), $row->_source->total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . ($i), $row->_source->total_share);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . ($i), $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . ($i), $row->_source->total_like + $row->_source->total_share + $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //bảng top nguồn tương tác về đối tượng trên youtube
            $yt_len = count($data_youtube_top_reaction);
            if ($yt_len) {
                // echo "yt starts at: ", $i, "-", $i-1+$yt_len, "\r\n";
                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Youtube');
                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':C' . ($i - 1 + $yt_len));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i - 1 + $yt_len))->applyFromArray($styleAllBorders);

                foreach ($data_youtube_top_reaction as $key => $row) {
                    $url = str_replace('https://', '', 'https://youtube.com/' . $row->_source->page_id);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->content_from_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->message);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styleArray);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->_source->total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row->_source->total_share);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, $row->_source->total_like + $row->_source->total_share + $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //bảng top nguồn tương tác về đối tượng trên news
            // print_r($data_ifollow_top_reaction); exit;
            $ifl_len = count($data_ifollow_top_reaction);
            if ($ifl_len) {
                // echo "news starts at: ", $i, "-", $i-1+$ifl_len, "\r\n";
                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'News');
                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':C' . ($i - 1 + $ifl_len));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i - 1 + $ifl_len))->applyFromArray($styleAllBorders);

                foreach ($data_ifollow_top_reaction as $key => $row) {
                    $url = str_replace('https://', '', 'https://' . $row->_source->web_page_name);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->web_page_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);
                    $url = str_replace('https://', '', 'https://' . $row->_source->web_url);
                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->web_title);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styleArray);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->web_content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->web_state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->_source->web_total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row->_source->web_total_share);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, $row->_source->web_child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, $row->_source->web_total_like + $row->_source->web_total_share + $row->_source->web_child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //bảng top nguồn tương tác về đối tượng trên papers
            if (count($data_paper_top_reaction)) {
                // echo "news starts at: ", $i, "-", $i-1+$ifl_len, "\r\n";
                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Báo giấy');
                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':C' . ($i - 1 + count($data_paper_top_reaction)));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i - 1 + count($data_paper_top_reaction)))->applyFromArray($styleAllBorders);

                foreach ($data_paper_top_reaction as $key => $row) {
                    // $urlMentionPaper = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=2';
                    $url = 'http://data.monitaz.vn/detail/index/' . $row->_id . '?_channel=2';
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->paper_page_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl($url);

                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->paper_title);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    // $url = str_replace('https://', '', 'https://' . $row->_source->paper_url);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl($url);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styleArray);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->paper_content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->paper_state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->_source->paper_total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row->_source->paper_total_share);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, 0);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, $row->_source->paper_total_like + $row->_source->paper_total_share + 0);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //bảng top nguồn tương tác về đối tượng trên tiktok
            $ttk_len = count($data_tiktok_top_reaction);
            if ($ttk_len) {
                // echo "ttk starts at: ", $i, "-", $i-1+$ttk_len, "\r\n";
                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Tiktok');
                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':C' . ($i - 1 + $ttk_len));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i - 1 + $ttk_len))->applyFromArray($styleAllBorders);

                foreach ($data_tiktok_top_reaction as $key => $row) {
                    $url = str_replace('https://', '', 'https://tiktok.com/@' . $row->_source->page_id . '/video/' . $row->_source->fb_id);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->content_from_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":G" . $i);
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->message);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styleArray);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->_source->total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row->_source->total_share);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, $row->_source->total_like + $row->_source->total_share + $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //bảng top nguồn tương tác về đối tượng trên twitter
            $ttr_len = count($data_twitter_top_reaction);
            if ($ttr_len) {
                // echo "ttr starts at: ", $i, "-", $i-1+$ttr_len, "\r\n";
                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Twitter');
                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':C' . ($i - 1 + $ttr_len));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i - 1 + $ttr_len))->applyFromArray($styleAllBorders);

                foreach ($data_twitter_top_reaction as $key => $row) {
                    $url = str_replace('https://', '', 'https://twitter.com/' . $row->_source->page_id . '/status/' . $row->_source->fb_id);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->content_from_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":F" . $i);
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->message);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styleArray);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->_source->total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row->_source->total_share);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, $row->_source->total_like + $row->_source->total_share + $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            //bảng top nguồn tương tác về đối tượng trên Instagram
            $ins_len = count($data_instagram_top_reaction);
            if ($ins_len) {
                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Instagram');
                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':C' . ($i - 1 + $ttr_len));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i - 1 + $ttr_len))->applyFromArray($styleAllBorders);

                foreach ($data_instagram_top_reaction as $key => $row) {
                    $url = str_replace('https://', '', 'https://instagram.com/' . $row->_source->page_id . '/p/' . $row->_source->fb_id);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->content_from_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":F" . $i);
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->message);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styleArray);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->_source->total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row->_source->total_share);
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, $row->_source->total_like + $row->_source->total_share + $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            // bảng top nguồn tương tác về đối tượng trên Reviewapp
            // echo "\r\ndata_reviewapp_top_reaction\r\n";
            // print_r($data_reviewapp_top_reaction);
            $rva_len = count($data_reviewapp_top_reaction);
            if ($rva_len) {
                // echo "rva starts at: ", $i, "-", $i-1+$rva_len, "\r\n";
                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Review App');
                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':C' . ($i - 1 + $rva_len));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i - 1 + $rva_len))->applyFromArray($styleAllBorders);

                foreach ($data_reviewapp_top_reaction as $key => $row) {
                    $url = str_replace('https://', '', 'https://https://play.google.com/store/apps/details?id=' . $row->_source->page_id . '&hl=vi&gl=US&reviewId=' . $row->_source->fb_id);
                    $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->_source->page_name);
                    $objPHPExcel->getActiveSheet()->getStyle('D' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('D' . $i)->getHyperlink()->setUrl('https://' . $url);

                    $objPHPExcel->getActiveSheet()->mergeCells("E" . $i . ":F" . $i);
                    $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, $row->_source->message);
                    $objPHPExcel->getActiveSheet()->getStyle('E' . ($i))->applyFromArray($styleArray)->applyFromArray($styleBlueColor);
                    $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://' . $url);
                    $objPHPExcel->getActiveSheet()->getStyle('F' . ($i))->applyFromArray($styleArray);
                    $objPHPExcel->getActiveSheet()->getStyle('G' . ($i))->applyFromArray($styleArray);

                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, extractTimeFromDateString($row->_source->content_created));
                    $objPHPExcel->getActiveSheet()->getStyle('H' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $state_text[$row->_source->state]);
                    $objPHPExcel->getActiveSheet()->getStyle('I' . ($i))->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->_source->total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('J' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $row->_source->total_share . "/5 sao");
                    $objPHPExcel->getActiveSheet()->getStyle('K' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('L' . $i, $row->_source->child_count);
                    $objPHPExcel->getActiveSheet()->getStyle('L' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);

                    $objPHPExcel->getActiveSheet()->setCellValue('M' . $i, $row->_source->total_like);
                    $objPHPExcel->getActiveSheet()->getStyle('M' . ($i))->applyFromArray($styleText)->applyFromArray($styleRightAlignment);
                    $i += 1;
                }
            }

            // echo $j, " ", $i;
            if ($j < $i) {
                $objPHPExcel->getActiveSheet()->mergeCells('B' . ($j) . ':B' . ($i - 1));
                $objPHPExcel->getActiveSheet()->getStyle('B' . ($j) . ':B' . ($i - 1))->applyFromArray($styles);
                $objPHPExcel->getActiveSheet()->getStyle('B' . ($j))->getAlignment()->setWrapText(true);
            } else {
                $objPHPExcel->getActiveSheet()->mergeCells('C' . ($i) . ':M' . ($i));
                $objPHPExcel->getActiveSheet()->getStyle('C' . ($i) . ':M' . ($i))->applyFromArray($styleAllBorders);
                $i++;
            }
        }

        // Tổng quan với đối thủ
        if ($has_sub_enemy) {
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'II. TỔNG QUAN ' . strtoupper($brand_name) . ' VÀ ĐỐI THỦ');
            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleArrays)->applyFromArray($styleBlueColor);
            $i += 2;
            $count_sub = 0;
            foreach ($list_object as $sub_index => $sub_brand_object) {
                if (count($sub_brand_object) == 1) continue;
                $count_sub++;
                $primary_quantity_all_channel = $list_primary_quantity_all_channel[$sub_index];
                $primary_array_sentiment = $list_primary_array_sentiment[$sub_index];
                if ($sub_brand_object[0]->id == 41787) $nameTitle = 'Khu vực HẠ LONG';
                if ($sub_brand_object[0]->id == 41788) $nameTitle = 'Khu vực PHÚ QUỐC';
                if ($sub_brand_object[0]->id == 41792) $nameTitle = 'Khu vực VĨNH PHÚC';
                if ($sub_brand_object[0]->id == 41801) $nameTitle = 'Khu vực KHÁC (LONG AN)';
                $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $count_sub . '. ' . $nameTitle);
                // $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $count_sub . '. ' . $sub_brand_object[0]->name);
                $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleArrays)->applyFromArray($styleBlueColor);
                $i += 2;
                $k = $i;
                // label
                if (count($sub_brand_object) > 4) {
                    // $col = round((13 - count($sub_brand_object)) / 2);
                    $col = 3;
                } else {
                    $col = 2;
                }
                foreach ($primary_quantity_all_channel as $channel_name => $row) {
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k, $k == $i ? "LƯỢNG TIN" : ($channel_name == "Total" ? "Tổng" : $channel_name));
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k)->applyFromArray($k == $i ? $styles : $styleText)->applyFromArray($styleLeftAlignment);

                    foreach ($sub_brand_object as $obj_index => $value) {
                        $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k, $list_channel[$sub_index][$obj_index][$channel_name]);
                        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k)->applyFromArray($obj_index ? $styleText : $styles);
                        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k)->getAlignment()->setHorizontal($k == $i ? PHPExcel_Style_Alignment::HORIZONTAL_CENTER : PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
                    }

                    $k += 1;
                }

                if (count($sub_brand_object) > 4) {
                    // $col = round((13 - count($sub_brand_object)) / 2);
                    $col = 3;
                    $i += count($primary_quantity_all_channel) + 1;
                } else {
                    $col = max(4 + count($sub_brand_object), 8);
                    $k = $i;
                }

                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k, "SENTIMENT");
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k)->applyFromArray($styles)->applyFromArray($styleLeftAlignment);

                foreach ($sub_brand_object as $obj_index => $obj) {
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k, $obj->name);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k)->applyFromArray($obj_index ? $styleText : $styles);
                }

                $k += 1;
                foreach ($primary_array_sentiment as $key => $row) {
                    // echo $key, "\r\n" ,$k,"\r\n";
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k, $key);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k)->applyFromArray($styleText)->applyFromArray($styleLeftAlignment);

                    foreach ($sub_brand_object as $obj_index => $obj) {
                        $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k, " " . $list_sentiment[$sub_index][$obj_index][$key]['percentage'] . "%");
                        $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k)->applyFromArray($obj_index ? $styleText : $styles)->applyFromArray($styleRightAlignment);
                    }

                    $k += 1;
                }

                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k, "CHỈ SỐ CẢM XÚC");
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k)->applyFromArray($styles)->applyFromArray($styleLeftAlignment);

                foreach ($sub_brand_object as $obj_index => $obj) {
                    $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k, round(($list_sentiment[$sub_index][$obj_index]['Positive']['percentage'] - $list_sentiment[$sub_index][$obj_index]['Negative']['percentage']) / max($list_sentiment[$sub_index][$obj_index]['Positive']['percentage'] + $list_sentiment[$sub_index][$obj_index]['Negative']['percentage'], 1)), 1);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k)->applyFromArray($obj_index ? $styleText : $styles);
                    $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col + $obj_index) . $k)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
                }

                $k += 1;
                $objPHPExcel->getActiveSheet()->setCellValue(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k, "Chỉ số cảm xúc = (Tích cực - Tiêu cực)/(Tích cực + Tiêu cực)");
                $objPHPExcel->getActiveSheet()->getStyle(PHPExcel_Cell::stringFromColumnIndex($col - 1) . $k)->applyFromArray($styleItalicText)->applyFromArray($styleSmallText)->applyFromArray($styleLeftAlignment);

                if (count($sub_brand_object) > 4) {
                    $i = $k + 2;
                } else $i += max(count($primary_quantity_all_channel), count($primary_array_sentiment)) + 3;


                // BIỂU ĐỒ
                // share of voices

                $qc = new QuickChart(array(
                    'width' => 500,
                    'height' => 300
                ));
                $qc->setApiKey($apiKeyQuickChart);
                $qc->setConfig($list_chartPieConfig[$sub_index]);
                $statusChartPieConfig = get_headers($qc->getShortUrl());
                if ($statusChartPieConfig && !strpos($statusChartPieConfig[0], '200')) {
                    $messageChartPieConfig = "\nStatus Chart Pie By Index Sub Brand " . $sub_index . " In Config "
                        . $setting['id']
                        . '-' . $setting['type']
                        . ': ' . $statusChartPieConfig[0];
                    telegramAlert($token, $chatID, $messageChartPieConfig);
                }
                $pathToPieImg = PUBLIC_PATH . 'tmp/' . $sub_brand_object[0]->id . '_' . $sub_index . '_pie_chart.png';
                $qc->toFile($pathToPieImg);

                $qc = new QuickChart(array(
                    'width' => 600,
                    'height' => 300
                ));
                $qc->setApiKey($apiKeyQuickChart);
                $qc->setConfig($list_chartBarConfig[$sub_index]);
                $statusChartBarConfig = get_headers($qc->getShortUrl());
                if ($statusChartBarConfig && !strpos($statusChartBarConfig[0], '200')) {
                    $messageChartBarConfig = "\nStatus Chart Bar By Index Sub Brand " . $sub_index . " In Config "
                        . $setting['id']
                        . '-' . $setting['type']
                        . ': ' . $statusChartBarConfig[0];
                    telegramAlert($token, $chatID, $messageChartBarConfig);
                }
                $pathToBarImg = PUBLIC_PATH . 'tmp/' . $sub_brand_object[0]->id . '_' . $sub_index . '_bar_chart.png';
                $qc->toFile($pathToBarImg);

                if ($setting['type'] == 'daily') {
                    $qc = new QuickChart(array(
                        'width' => 600,
                        'height' => 300
                    ));
                    $qc->setApiKey($apiKeyQuickChart);
                    $qc->setConfig($list_chartLineConfig[$sub_index]);
                    $statusChartLineConfig = get_headers($qc->getShortUrl());
                    if ($statusChartLineConfig && !strpos($statusChartLineConfig[0], '200')) {
                        $messageChartLineConfig = "\nStatus Chart Line By Index Sub Brand " . $sub_index . " In Config "
                            . $setting['id']
                            . '-' . $setting['type']
                            . ': ' . $statusChartLineConfig[0];
                        telegramAlert($token, $chatID, $messageChartLineConfig);
                    }
                    $pathToLineImg = PUBLIC_PATH . 'tmp/' . $sub_brand_object[0]->id . '_' . $sub_index . '_line_chart.png';
                    $qc->toFile($pathToLineImg);
                }

                $qc = new QuickChart(array(
                    'width' => 600,
                    'height' => 300
                ));
                $qc->setApiKey($apiKeyQuickChart);
                $qc->setConfig($list_chartStackedBarConfig[$sub_index]);
                $statusChartStackedBarConfig = get_headers($qc->getShortUrl());
                if ($statusChartStackedBarConfig && !strpos($statusChartStackedBarConfig[0], '200')) {
                    $messageChartStackedBarConfig = "\nStatus Chart Stacked Bar By Index Sub Brand " . $sub_index . " In Config "
                        . $setting['id']
                        . '-' . $setting['type']
                        . ': ' . $statusChartStackedBarConfig[0];
                    telegramAlert($token, $chatID, $messageChartStackedBarConfig);
                }
                $pathToStackedBarImg = PUBLIC_PATH . 'tmp/' . $sub_brand_object[0]->id . '_' . $sub_index . '_stacked_bar_chart.png';
                $qc->toFile($pathToStackedBarImg);

                $objDrawing = new PHPExcel_Worksheet_Drawing();

                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToPieImg);
                $objDrawing->setCoordinates('A' . $i);
                $objDrawing->setName('Share of Voice');
                $objDrawing->setDescription('Share of Voice');
                //setOffsetX works properly
                $objDrawing->setOffsetX(50);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(500);
                $objDrawing->setHeight(300);

                $objDrawing = new PHPExcel_Worksheet_Drawing();
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToBarImg);
                $objDrawing->setCoordinates('G' . $i);
                $objDrawing->setName('Lượng tin trên các nền tảng');
                //setOffsetX works properly
                $objDrawing->setOffsetX(50);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(600);
                $objDrawing->setHeight(300);


                $i += 18;
                $objDrawing = new PHPExcel_Worksheet_Drawing();
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToLineImg);
                $objDrawing->setCoordinates('A' . $i);
                $objDrawing->setName('Xu hướng tin bài theo giờ');
                //setOffsetX works properly
                $objDrawing->setOffsetX(25);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(600);
                $objDrawing->setHeight(300);

                $objDrawing = new PHPExcel_Worksheet_Drawing();
                $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());
                $objDrawing->setPath($pathToStackedBarImg);
                $objDrawing->setCoordinates('G' . $i);
                $objDrawing->setName('Tỷ lệ cảm xúc');
                //setOffsetX works properly
                $objDrawing->setOffsetX(50);
                $objDrawing->setOffsetY(5);
                //set width, height
                $objDrawing->setWidth(600);
                $objDrawing->setHeight(300);

                $i += 18;
            }
        }

        // data
        if ($has_sheet_detail) {
            $objPHPExcel->createSheet(1);
            $objPHPExcel->setActiveSheetIndex(1);
            $objPHPExcel->getActiveSheet()->setTitle('Data');
            $objPHPExcel->getActiveSheet()->getColumnDimension("A")->setWidth(20);
            $objPHPExcel->getActiveSheet()->getColumnDimension("B")->setWidth(10);
            $objPHPExcel->getActiveSheet()->getColumnDimension("C")->setWidth(50);
            $objPHPExcel->getActiveSheet()->getColumnDimension("D")->setWidth(20);
            $objPHPExcel->getActiveSheet()->getColumnDimension("E")->setWidth(20);
            $objPHPExcel->getActiveSheet()->getColumnDimension("F")->setWidth(15);
            $objPHPExcel->getActiveSheet()->getColumnDimension("G")->setWidth(15);
            $objPHPExcel->getActiveSheet()->getColumnDimension("H")->setWidth(15);
            $objPHPExcel->getActiveSheet()->getColumnDimension("I")->setWidth(20);
            $objPHPExcel->getActiveSheet()->getColumnDimension("J")->setWidth(20);
            $objPHPExcel->getActiveSheet()->getColumnDimension("K")->setWidth(20);

            $i = 1;
            // echo "<pre>";
            if ($setting_id == 120) {
                foreach ($list_object as $sub_index => $sub_objs) {
                    foreach ($sub_objs as $index => $item) {
                        $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $item->name);
                        $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleBoldText)->applyFromArray($styleLeftAlignment)->applyFromArray($styleBlueColor);
                        $i += 2;

                        foreach ($active_channels as $channel => $is_active) {
                            if (!$is_active)
                                continue;
                            if ($channel == "Facebook") {
                                $is_checked = isset($data_check['Facebook']) ? $data_check['Facebook']->is_checked : 0;
                                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                                $json_doc = __generateJsonSocial($brand_id, [$item->id], $start_date, $end_date, '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchSocial($json_doc);
                            } else if ($channel == "Ifollow") {
                                $is_checked = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_checked : 0;
                                $is_read = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_read : 0;
                                $json_doc = __generateJsonIfollow($brand_id, [$item->id], $start_date, $end_date, '', '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchIfollow($json_doc);
                            } else if ($channel == "Instagram") {
                                $is_checked = isset($data_check['Instagram']) ? $data_check['Instagram']->is_checked : 0;
                                $json_doc = __generateJsonInstagram($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked);
                                $data_query = _searchInstagram($json_doc);
                            } else if ($channel == "Youtube") {
                                $is_checked = isset($data_check['Youtube']) ? $data_check['Youtube']->is_checked : 0;
                                $is_read = isset($data_check['Youtube']) ? $data_check['Youtube']->is_read : 0;
                                $json_doc = __generateJsonYoutube($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchYoutube($json_doc);
                            } else if ($channel == "Tiktok") {
                                $is_checked = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_checked : 0;
                                $is_read = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_read : 0;
                                $json_doc = __generateJsonTiktok($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTiktok($json_doc);
                            } else if ($channel == "Twitter") {
                                $is_checked = isset($data_check['Twitter']) ? $data_check['Twitter']->is_checked : 0;
                                $is_read = isset($data_check['Twitter']) ? $data_check['Twitter']->is_read : 0;
                                $json_doc = __generateJsonTwitter($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTwitter($json_doc);
                            } else if ($channel == "Reviewapp") {
                                $is_checked = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_checked : 0;
                                $is_read = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_read : 0;
                                $json_doc = __generateJsonReviewapp($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchReviewapp($json_doc);
                            } else if ($channel == "Threads") {
                                $is_checked = isset($data_check['Threads']) ? $data_check['Threads']->is_checked : 0;
                                $is_read = isset($data_check['Threads']) ? $data_check['Threads']->is_read : 0;
                                $json_doc = __generateJsonThreads($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchThreads($json_doc);
                            } else if ($channel == "Paper") {
                                $is_checked = isset($data_check['Paper']) ? $data_check['Paper']->is_checked : 0;
                                $json_doc = __generateJsonPaper($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, null, $is_subbrand);
                                $data_query = _searchPaper($json_doc);
                            }

                            $data = $data_query->hits->hits;
                            // echo $channel . ": " . count($data) . "\n";
                            if (count($data)) {
                                $data = sortByState($data, $channel);
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, "Nền tảng");
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                // iFollow
                                $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'STT');
                                $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Tiêu đề / Nội dung');
                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, 'Nguồn');
                                $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, 'Ðánh giá (Tích cực, Tiêu cực, trung tính)');
                                $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                if ($channel == "Facebook" || $channel == "Youtube" || $channel == "Tiktok" || $channel == "Twitter" || $channel == "Instagram") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Likes');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Shares');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Comments');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, 'Phân Loại Tin');
                                    $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                if ($channel == "Reviewapp") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Store');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Star');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                $i++;
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $channel == "Ifollow" ? "News" : $channel);
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $j = 0;

                                foreach ($data as $key => $row) {
                                    $j++;
                                    $_id = $row->_id;
                                    $row = $row->_source;
                                    if ($channel == "Facebook") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl('https://facebook.com/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, ($row->type == 0) ? 'Post' : 'Comment');
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Ifollow" || $channel == "Forum") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->web_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($row->web_url);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->web_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->web_state == 2) ? 'Tiêu cực' : (($row->web_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                    } else if ($channel == "Paper") {
                                        $urlMentionPaper = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=2';
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->paper_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($urlMentionPaper);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->paper_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->paper_state == 2) ? 'Tiêu cực' : (($row->paper_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                    } else if ($channel == "Tv") {
                                        $urlMentionTv = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=3';
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->tv_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($urlMentionTv);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->tv_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->tv_state == 2) ? 'Tiêu cực' : (($row->tv_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                    } else if ($channel == "Youtube") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://youtube.com/watch?v=' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, ($row->type == 0) ? 'Post' : 'Comment');
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Tiktok") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://tiktok.com/@' . $row->page_id . '/video/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, ($row->type == 0) ? 'Post' : 'Comment');
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Instagram") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://instagram.com/' . $row->page_id . '/p/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Twitter") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://twitter.com/' . $row->page_id . '/status/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Reviewapp") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $store = strpos($_id, $row->fb_id) !== false ? 'App Store' : 'Google Play';
                                        if ($store == 'Google Play') {
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://play.google.com/store/apps/details?id=' . $row->page_id . '&hl=vi&gl=US&reviewId=' . $row->fb_id);
                                        } else {
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);
                                        }

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $store);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    }
                                    $i++;
                                }
                                // echo "\n merge " . "A" . ($i - $j) . ":A" . ($i-1) ."\n";
                                $objPHPExcel->getActiveSheet()->mergeCells("A" . ($i - $j) . ":A" . ($i - 1));
                                $objPHPExcel->getActiveSheet()->getStyle("A" . ($i - $j) . ":A" . ($i - 1))->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $i += 2;
                            }
                        }
                        $i += 3;
                    }
                }
            } elseif ($setting_id == 143 || $setting_id == 144  || $setting_id == 146) {
                foreach ($list_object as $sub_index => $sub_objs) {
                    foreach ($sub_objs as $index => $item) {
                        $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $item->name);
                        $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleBoldText)->applyFromArray($styleLeftAlignment)->applyFromArray($styleBlueColor);
                        $i += 2;

                        foreach ($active_channels as $channel => $is_active) {
                            if (!$is_active)
                                continue;
                            if ($channel == "Facebook") {
                                $is_checked = isset($data_check['Facebook']) ? $data_check['Facebook']->is_checked : 0;
                                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                                $json_doc = __generateJsonSocial($brand_id, [$item->id], $start_date, $end_date, '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchSocial($json_doc);
                            } else if ($channel == "Ifollow") {
                                $is_checked = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_checked : 0;
                                $is_read = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_read : 0;
                                $json_doc = __generateJsonIfollow($brand_id, [$item->id], $start_date, $end_date, '', '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchIfollow($json_doc);
                            } else if ($channel == "Instagram") {
                                $is_checked = isset($data_check['Instagram']) ? $data_check['Instagram']->is_checked : 0;
                                $json_doc = __generateJsonInstagram($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked);
                                $data_query = _searchInstagram($json_doc);
                            } else if ($channel == "Youtube") {
                                $is_checked = isset($data_check['Youtube']) ? $data_check['Youtube']->is_checked : 0;
                                $is_read = isset($data_check['Youtube']) ? $data_check['Youtube']->is_read : 0;
                                $json_doc = __generateJsonYoutube($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchYoutube($json_doc);
                            } else if ($channel == "Tiktok") {
                                $is_checked = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_checked : 0;
                                $is_read = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_read : 0;
                                $json_doc = __generateJsonTiktok($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTiktok($json_doc);
                            } else if ($channel == "Twitter") {
                                $is_checked = isset($data_check['Twitter']) ? $data_check['Twitter']->is_checked : 0;
                                $is_read = isset($data_check['Twitter']) ? $data_check['Twitter']->is_read : 0;
                                $json_doc = __generateJsonTwitter($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTwitter($json_doc);
                            } else if ($channel == "Reviewapp") {
                                $is_checked = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_checked : 0;
                                $is_read = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_read : 0;
                                $json_doc = __generateJsonReviewapp($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchReviewapp($json_doc);
                            } else if ($channel == "Paper") {
                                $is_checked = isset($data_check['Paper']) ? $data_check['Paper']->is_checked : 0;
                                $json_doc = __generateJsonPaper($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, null, $is_subbrand);
                                $data_query = _searchPaper($json_doc);
                            } else if ($channel == "Tv") {
                                $is_checked = isset($data_check['Tv']) ? $data_check['Tv']->is_checked : 0;
                                $json_doc = __generateJsonTv($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, null, $is_subbrand);
                                $data_query = _searchTv($json_doc);
                            }

                            $data = $data_query->hits->hits;
                            // echo $channel . ": " . count($data) . "\n";
                            if (count($data)) {
                                $data = sortByState($data, $channel);
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, "Nền tảng");
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                // iFollow
                                $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'STT');
                                $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Tiêu đề / Nội dung');
                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, 'Nguồn');
                                $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, 'Ðánh giá (Tích cực, Tiêu cực, trung tính)');
                                $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                if ($channel == "Facebook" || $channel == "Youtube" || $channel == "Tiktok" || $channel == "Twitter" || $channel == "Instagram") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Likes');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Shares');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Comments');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, 'Khối');
                                    $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, 'Công Ty');
                                    $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, 'Viết Tắt');
                                    $objPHPExcel->getActiveSheet()->getStyle('K' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                if ($channel == "Ifollow" || $channel == "Tv" || $channel == "Paper") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Khối');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Công Ty');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Viết Tắt');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                if ($channel == "Reviewapp") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Store');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Star');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                $i++;
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $channel == "Ifollow" ? "News" : $channel);
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $j = 0;

                                foreach ($data as $key => $row) {
                                    $j++;
                                    $_id = $row->_id;
                                    $row = $row->_source;
                                    if ($channel == 'Ifollow' || $channel == 'Paper' || $channel == 'Tv') {
                                        if ($channel == 'Ifollow') {
                                            $subbrand = $um->getSubBrandById($row->web_sub_brand_service_id);
                                            if ($subbrand && isset($subbrand)) {
                                                $name_subbrand = $subbrand->name;
                                            } else {
                                                $name_subbrand = '';
                                            }
                                        } elseif ($channel == 'Paper') {
                                            $subbrand = $um->getSubBrandById($row->paper_sub_brand_service_id);
                                            if ($subbrand && isset($subbrand)) {
                                                $name_subbrand = $subbrand->name;
                                            } else {
                                                $name_subbrand = '';
                                            }
                                        } elseif ($channel == 'Tv') {
                                            $subbrand = $um->getSubBrandById($row->tv_sub_brand_service_id);
                                            if ($subbrand && isset($subbrand)) {
                                                $name_subbrand = $subbrand->name;
                                            } else {
                                                $name_subbrand = '';
                                            }
                                        }
                                    } else {
                                        $subbrand = $um->getSubBrandById($row->sub_brand_service_id);
                                        if ($subbrand && isset($subbrand)) {
                                            $name_subbrand = $subbrand->name;
                                        } else {
                                            $name_subbrand = '';
                                        }
                                    }

                                    $name_sub_kh = '';
                                    $name_sub_ct = '';
                                    $name_sub_vt = '';

                                    if ($name_subbrand) {
                                        $arr_name = explode("-", $name_subbrand);
                                        $name_sub_kh = $arr_name[0];
                                        $name_sub_ct = $arr_name[1];
                                        $name_sub_vt = $arr_name[2];
                                    }

                                    if ($channel == "Facebook") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl('https://facebook.com/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $name_sub_kh);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $name_sub_ct);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $name_sub_vt);
                                        $objPHPExcel->getActiveSheet()->getStyle('K' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Ifollow" || $channel == "Forum") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->web_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($row->web_url);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->web_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->web_state == 2) ? 'Tiêu cực' : (($row->web_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $name_sub_kh);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $name_sub_ct);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $name_sub_vt);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Paper") {
                                        $urlMentionPaper = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=2';
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->paper_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($urlMentionPaper);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->paper_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->paper_state == 2) ? 'Tiêu cực' : (($row->paper_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $name_sub_kh);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $name_sub_ct);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $name_sub_vt);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Tv") {
                                        $urlMentionTv = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=3';
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->tv_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($urlMentionTv);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->tv_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->tv_state == 2) ? 'Tiêu cực' : (($row->tv_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $name_sub_kh);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $name_sub_ct);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $name_sub_vt);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Youtube") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://youtube.com/watch?v=' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $name_sub_kh);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $name_sub_ct);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $name_sub_vt);
                                        $objPHPExcel->getActiveSheet()->getStyle('K' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Tiktok") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://tiktok.com/@' . $row->page_id . '/video/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $name_sub_kh);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $name_sub_ct);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $name_sub_vt);
                                        $objPHPExcel->getActiveSheet()->getStyle('K' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Instagram") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://instagram.com/' . $row->page_id . '/p/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $name_sub_kh);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $name_sub_ct);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $name_sub_vt);
                                        $objPHPExcel->getActiveSheet()->getStyle('K' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Twitter") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://twitter.com/' . $row->page_id . '/status/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $name_sub_kh);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $name_sub_ct);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('K' . $i, $name_sub_vt);
                                        $objPHPExcel->getActiveSheet()->getStyle('K' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Reviewapp") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $store = strpos($_id, $row->fb_id) !== false ? 'App Store' : 'Google Play';
                                        if ($store == 'Google Play') {
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://play.google.com/store/apps/details?id=' . $row->page_id . '&hl=vi&gl=US&reviewId=' . $row->fb_id);
                                        } else {
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);
                                        }

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $store);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    }
                                    $i++;
                                }
                                // echo "\n merge " . "A" . ($i - $j) . ":A" . ($i-1) ."\n";
                                $objPHPExcel->getActiveSheet()->mergeCells("A" . ($i - $j) . ":A" . ($i - 1));
                                $objPHPExcel->getActiveSheet()->getStyle("A" . ($i - $j) . ":A" . ($i - 1))->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $i += 2;
                            }
                        }
                        $i += 3;
                    }
                }
            } elseif (in_array($setting_id, array(94))) { // custom tập đoàn thành công
                $objPHPExcel->getActiveSheet()->getColumnDimension("A")->setWidth(20);
                $objPHPExcel->getActiveSheet()->getColumnDimension("B")->setWidth(10);
                $objPHPExcel->getActiveSheet()->getColumnDimension("C")->setWidth(10);
                $objPHPExcel->getActiveSheet()->getColumnDimension("D")->setWidth(20);
                $objPHPExcel->getActiveSheet()->getColumnDimension("E")->setWidth(50);
                $objPHPExcel->getActiveSheet()->getColumnDimension("F")->setWidth(20);
                $objPHPExcel->getActiveSheet()->getColumnDimension("G")->setWidth(15);
                $objPHPExcel->getActiveSheet()->getColumnDimension("H")->setWidth(15);
                $objPHPExcel->getActiveSheet()->getColumnDimension("I")->setWidth(20);
                $objPHPExcel->getActiveSheet()->getColumnDimension("J")->setWidth(20);
                $objPHPExcel->getActiveSheet()->getColumnDimension("K")->setWidth(20);

                foreach ($list_object as $sub_index => $sub_objs) {
                    foreach ($sub_objs as $index => $item) {
                        foreach ($active_channels as $channel => $is_active) {
                            if (!$is_active)
                                continue;
                            if ($channel == "Facebook") {
                                $is_checked = isset($data_check['Facebook']) ? $data_check['Facebook']->is_checked : 0;
                                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                                $json_doc = __generateJsonSocial($brand_id, [$item->id], $start_date, $end_date, '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchSocial($json_doc);
                            } else if ($channel == "Ifollow") {
                                $is_checked = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_checked : 0;
                                $is_read = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_read : 0;
                                $json_doc = __generateJsonIfollow($brand_id, [$item->id], $start_date, $end_date, '', '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchIfollow($json_doc);
                            } else if ($channel == "Instagram") {
                                $is_checked = isset($data_check['Instagram']) ? $data_check['Instagram']->is_checked : 0;
                                $json_doc = __generateJsonInstagram($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked);
                                $data_query = _searchInstagram($json_doc);
                            } else if ($channel == "Youtube") {
                                $is_checked = isset($data_check['Youtube']) ? $data_check['Youtube']->is_checked : 0;
                                $is_read = isset($data_check['Youtube']) ? $data_check['Youtube']->is_read : 0;
                                $json_doc = __generateJsonYoutube($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchYoutube($json_doc);
                            } else if ($channel == "Tiktok") {
                                $is_checked = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_checked : 0;
                                $is_read = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_read : 0;
                                $json_doc = __generateJsonTiktok($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTiktok($json_doc);
                            } else if ($channel == "Twitter") {
                                $is_checked = isset($data_check['Twitter']) ? $data_check['Twitter']->is_checked : 0;
                                $is_read = isset($data_check['Twitter']) ? $data_check['Twitter']->is_read : 0;
                                $json_doc = __generateJsonTwitter($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTwitter($json_doc);
                            } else if ($channel == "Reviewapp") {
                                $is_checked = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_checked : 0;
                                $is_read = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_read : 0;
                                $json_doc = __generateJsonReviewapp($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchReviewapp($json_doc);
                            } else if ($channel == "Paper") {
                                $is_checked = isset($data_check['Paper']) ? $data_check['Paper']->is_checked : 0;
                                $json_doc = __generateJsonPaper($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, null, $is_subbrand);
                                $data_query = _searchPaper($json_doc);
                            }

                            $data = $data_query->hits->hits;
                            // echo $channel . ": " . count($data) . "\n";
                            if (count($data)) {
                                $data = sortByState($data, $channel);
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, "Đối tượng");
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, "Nền tảng");
                                $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                // iFollow
                                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'STT');
                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, 'Ngày đăng');
                                $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, 'Tiêu đề / Nội dung');
                                $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Nguồn');
                                $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Ðánh giá (Tích cực, Tiêu cực, trung tính)');
                                $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                if ($channel == "Facebook" || $channel == "Youtube" || $channel == "Tiktok" || $channel == "Twitter" || $channel == "Instagram") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Likes');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, 'Shares');
                                    $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, 'Comments');
                                    $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                if ($channel == "Reviewapp") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Store');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, 'Star');
                                    $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                $i++;
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $item->name);
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleBoldText)->applyFromArray($styleCenterAlignment)->applyFromArray($styleBlueColor);

                                $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $channel == "Ifollow" ? "News" : $channel);
                                $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $j = 0;

                                foreach ($data as $key => $row) {
                                    $j++;
                                    $_id = $row->_id;
                                    $row = $row->_source;
                                    if ($channel == "Facebook") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $i)->getHyperlink()->setUrl('https://facebook.com/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Ifollow" || $channel == "Forum") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->web_content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, remove3and4bytesCharFromUtf8Str($row->web_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $i)->getHyperlink()->setUrl($row->web_url);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->web_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->web_state == 2) ? 'Tiêu cực' : (($row->web_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                    } else if ($channel == "Paper") {
                                        $urlMentionPaper = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=2';
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->paper_content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, remove3and4bytesCharFromUtf8Str($row->paper_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $i)->getHyperlink()->setUrl($urlMentionPaper);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->paper_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->paper_state == 2) ? 'Tiêu cực' : (($row->paper_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                    } else if ($channel == "Tv") {
                                        $urlMentionTv = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=3';
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->tv_content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, remove3and4bytesCharFromUtf8Str($row->tv_title));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(4, $i)->getHyperlink()->setUrl($urlMentionTv);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->tv_page_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->tv_state == 2) ? 'Tiêu cực' : (($row->tv_state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                    } else if ($channel == "Youtube") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://youtube.com/watch?v=' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Tiktok") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://tiktok.com/@' . $row->page_id . '/video/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Instagram") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://instagram.com/' . $row->page_id . '/p/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Twitter") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://twitter.com/' . $row->page_id . '/status/' . $row->fb_id);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->total_like);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('J' . $i, $row->child_count);
                                        $objPHPExcel->getActiveSheet()->getStyle('J' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    } else if ($channel == "Reviewapp") {
                                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $j);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, date('Y-m-d', strtotime($row->content_created)));
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->getCell('E' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                        $store = strpos($_id, $row->fb_id) !== false ? 'App Store' : 'Google Play';
                                        if ($store == 'Google Play') {
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('E' . $i)->getHyperlink()->setUrl('https://play.google.com/store/apps/details?id=' . $row->page_id . '&hl=vi&gl=US&reviewId=' . $row->fb_id);
                                        } else {
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);
                                        }

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->content_from_name);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $store);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('I' . $i, $row->total_share);
                                        $objPHPExcel->getActiveSheet()->getStyle('I' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                    }
                                    $i++;
                                }
                                // echo "\n merge " . "A" . ($i - $j) . ":A" . ($i-1) ."\n";
                                $objPHPExcel->getActiveSheet()->mergeCells("A" . ($i - $j) . ":A" . ($i - 1));
                                $objPHPExcel->getActiveSheet()->getStyle("A" . ($i - $j) . ":A" . ($i - 1))->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->mergeCells("B" . ($i - $j) . ":B" . ($i - 1));
                                $objPHPExcel->getActiveSheet()->getStyle("B" . ($i - $j) . ":B" . ($i - 1))->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $i += 2;
                            }
                        }
                        $i += 3;
                    }
                }
            } elseif ($setting_id == 169) {
                $indexSheet = 0;
                foreach ($list_object as $sub_index => $sub_objs) {
                    $indexSheet++;
                    if ($indexSheet != 1) {
                        $objPHPExcel->createSheet($indexSheet);
                        $objPHPExcel->setActiveSheetIndex($indexSheet);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("A")->setWidth(20);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("B")->setWidth(10);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("C")->setWidth(50);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("D")->setWidth(20);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("E")->setWidth(20);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("F")->setWidth(15);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("G")->setWidth(15);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("H")->setWidth(15);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("I")->setWidth(20);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("J")->setWidth(20);
                        $objPHPExcel->getActiveSheet()->getColumnDimension("K")->setWidth(20);

                        $i = 1;
                    }
                    foreach ($sub_objs as $index => $item) {
                        if ($i == 1) $objPHPExcel->getActiveSheet()->setTitle(substr($item->name, 0, 31));
                        $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $item->name);
                        $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleBoldText)->applyFromArray($styleLeftAlignment)->applyFromArray($styleBlueColor);
                        $i += 2;

                        foreach ($active_channels as $channel => $is_active) {
                            if (!$is_active)
                                continue;
                            if ($channel == "Facebook") {
                                $is_checked = isset($data_check['Facebook']) ? $data_check['Facebook']->is_checked : 0;
                                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                                $json_doc = __generateJsonSocial($brand_id, [$item->id], $start_date, $end_date, '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchSocial($json_doc);
                            } else if ($channel == "Ifollow") {
                                $is_checked = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_checked : 0;
                                $is_read = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_read : 0;
                                $json_doc = __generateJsonIfollow($brand_id, [$item->id], $start_date, $end_date, '', '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchIfollow($json_doc);
                            } else if ($channel == "Instagram") {
                                $is_checked = isset($data_check['Instagram']) ? $data_check['Instagram']->is_checked : 0;
                                $json_doc = __generateJsonInstagram($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked);
                                $data_query = _searchInstagram($json_doc);
                            } else if ($channel == "Youtube") {
                                $is_checked = isset($data_check['Youtube']) ? $data_check['Youtube']->is_checked : 0;
                                $is_read = isset($data_check['Youtube']) ? $data_check['Youtube']->is_read : 0;
                                $json_doc = __generateJsonYoutube($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchYoutube($json_doc);
                            } else if ($channel == "Tiktok") {
                                $is_checked = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_checked : 0;
                                $is_read = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_read : 0;
                                $json_doc = __generateJsonTiktok($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTiktok($json_doc);
                            } else if ($channel == "Twitter") {
                                $is_checked = isset($data_check['Twitter']) ? $data_check['Twitter']->is_checked : 0;
                                $is_read = isset($data_check['Twitter']) ? $data_check['Twitter']->is_read : 0;
                                $json_doc = __generateJsonTwitter($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTwitter($json_doc);
                            } else if ($channel == "Reviewapp") {
                                $is_checked = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_checked : 0;
                                $is_read = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_read : 0;
                                $json_doc = __generateJsonReviewapp($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchReviewapp($json_doc);
                            } else if ($channel == "Reviewmap") {
                                $is_checked = isset($data_check['Reviewmap']) ? $data_check['Reviewmap']->is_checked : 0;
                                $is_read = isset($data_check['Reviewmap']) ? $data_check['Reviewmap']->is_read : 0;
                                $json_doc = __generateJsonReviewmap($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchReviewmap($json_doc);
                            } else if ($channel == "Zalo") {
                                $is_checked = isset($data_check['Zalo']) ? $data_check['Zalo']->is_checked : 0;
                                $is_read = isset($data_check['Zalo']) ? $data_check['Zalo']->is_read : 0;
                                $json_doc = __generateJsonZalo($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchZalo($json_doc);
                            } else if ($channel == "Ecommerce") {
                                $is_checked = isset($data_check['Ecommerce']) ? $data_check['Ecommerce']->is_checked : 0;
                                $is_read = isset($data_check['Ecommerce']) ? $data_check['Ecommerce']->is_read : 0;
                                $json_doc = __generateJsonEcommerce($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchEcommerce($json_doc);
                            } else if ($channel == "Paper") {
                                $is_checked = isset($data_check['Paper']) ? $data_check['Paper']->is_checked : 0;
                                $json_doc = __generateJsonPaper($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, null, $is_subbrand);
                                $data_query = _searchPaper($json_doc);
                            } else if ($channel == "Tv") {
                                $is_checked = isset($data_check['Tv']) ? $data_check['Tv']->is_checked : 0;
                                $json_doc = __generateJsonTv($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, null, $is_subbrand);
                                $data_query = _searchTv($json_doc);
                            }

                            $data = $data_query->hits->hits;
                            // echo $channel . ": " . count($data) . "\n";
                            if (count($data)) {
                                $data = sortByState($data, $channel);
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, "Nền tảng");
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                // iFollow
                                $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'STT');
                                $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Tiêu đề / Nội dung');
                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, 'Nguồn');
                                $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, 'Ðánh giá (Tích cực, Tiêu cực, trung tính)');
                                $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                if ($channel == "Facebook" || $channel == "Youtube" || $channel == "Tiktok" || $channel == "Twitter" || $channel == "Instagram" || $channel == "Threads") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Likes');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Shares');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Comments');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                if ($channel == "Reviewapp" || $channel == "Reviewmap") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Store');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Star');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                if ($channel == "Ecommerce") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Star');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Phân Loại Tin');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Loại Nền Tảng');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                $i++;
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $channel == "Ifollow" ? "News" : $channel);
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $j = 0;
                                if ($channel == "Ecommerce") {
                                    $ecomerceShopee = [];
                                    $otherEcomerce = [];

                                    foreach ($data as $key => $row) {
                                        $j++;
                                        $_id = $row->_id;
                                        $row = $row->_source;
                                        if ($row->type == 0) {
                                            $star = $row->rate_count;
                                            $type_mention = "Post";

                                            if ($row->type_ecomerce == 1) {
                                                $url = 'https://shopee.vn/' . $row->content_from_name . '-i.' . $row->content_from_id . '.' . $row->fb_id;
                                                $url_page_name = 'https://shopee.vn/' . $row->content_from_name;
                                            } else {
                                                $url = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row->fb_id;
                                                $url_page_name = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row->fb_id;
                                            }
                                        } else {
                                            $star = $row->star;
                                            $type_mention = "Review";
                                            if ($row->type_ecomerce == 1) {
                                                $url = 'https://shopee.vn/' . $row->page_name . '-i.' . $row->page_id . '.' . $row->fb_parent_id;
                                                $url_page_name = 'https://shopee.vn/' . $row->page_name;
                                            } else {
                                                $url = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row->fb_parent_id;
                                                $url_page_name = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row->fb_parent_id;
                                            }
                                        }

                                        // Phân loại dữ liệu theo type_ecomerce
                                        if ($row->type_ecomerce == 1) {
                                            $ecommerce_name = "Ecommerce Shopee";
                                            $ecomerceShopee[] = [
                                                'url' => $url,
                                                'message' => remove3and4bytesCharFromUtf8Str($row->message),
                                                'content_from_name' => $row->content_from_name,
                                                'state' => $row->state,
                                                'star' => $star,
                                                'type_mention' => $type_mention,
                                                'ecommerce_name' => $ecommerce_name
                                            ];
                                        } else {
                                            $ecommerce_name = "Ecommerce Tiktok Shop";
                                            $otherEcomerce[] = [
                                                'url' => $url,
                                                'message' => remove3and4bytesCharFromUtf8Str($row->message),
                                                'content_from_name' => $row->content_from_name,
                                                'state' => $row->state,
                                                'star' => $star,
                                                'type_mention' => $type_mention,
                                                'ecommerce_name' => $ecommerce_name
                                            ];
                                        }
                                    }

                                    $a = 0;

                                    foreach ($ecomerceShopee as $s => $dataRow) {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $a + 1);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit($dataRow['message'], PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl($dataRow['url']);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $dataRow['content_from_name']);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($dataRow['state'] == 2) ? 'Tiêu cực' : (($dataRow['state'] == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $dataRow['star']);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $dataRow['type_mention']);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $dataRow['ecommerce_name']);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $i++;
                                        $a++;
                                    }

                                    // Sau đó là các dòng còn lại (Ecommerce Tiktok Shop)
                                    foreach ($otherEcomerce as $s => $dataRow) {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $a + 1);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit($dataRow['message'], PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl($dataRow['url']);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $dataRow['content_from_name']);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($dataRow['state'] == 2) ? 'Tiêu cực' : (($dataRow['state'] == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $dataRow['star']);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $dataRow['type_mention']);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $dataRow['ecommerce_name']);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $i++;
                                        $a++;
                                    }
                                } else {
                                    foreach ($data as $key => $row) {
                                        $j++;
                                        $_id = $row->_id;
                                        $row = $row->_source;
                                        if ($channel == "Facebook") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl('https://facebook.com/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Ifollow" || $channel == "Forum") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->web_title = htmlspecialchars($row->web_title, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, cleanExcelText($row->web_title));
                                            // $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->web_title));
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($row->web_url);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->web_page_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->web_state == 2) ? 'Tiêu cực' : (($row->web_state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                        } else if ($channel == "Paper") {
                                            $urlMentionPaper = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=2';
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->paper_title = htmlspecialchars($row->paper_title, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, cleanExcelText($row->paper_title));
                                            // $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->paper_title));
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($urlMentionPaper);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->paper_page_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->paper_state == 2) ? 'Tiêu cực' : (($row->paper_state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                        } else if ($channel == "Tv") {
                                            $urlMentionTv = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=3';
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->tv_title = htmlspecialchars($row->tv_title, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, cleanExcelText($row->tv_title));
                                            // $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->tv_title));
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($urlMentionTv);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->tv_page_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->tv_state == 2) ? 'Tiêu cực' : (($row->tv_state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                        } else if ($channel == "Youtube") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://youtube.com/watch?v=' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Tiktok") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://tiktok.com/@' . $row->page_id . '/video/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Instagram") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://instagram.com/' . $row->page_id . '/p/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Twitter") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://twitter.com/' . $row->page_id . '/status/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Zalo") {
                                            // $url = str_replace('https://', '', 'https://rd.zapps.vn/detail/' . $row->page_id . '?id=' . $row->fb_id . '&src=profile');
                                            $link_user = $domain_zalo_url . "/detail?content_from_id=" . $row->content_from_id . "&fb_id=" . $row->fb_id;
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('http://' . $link_user);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                        } else if ($channel == "Reviewapp") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $store = strpos($_id, $row->fb_id) !== false ? 'App Store' : 'Google Play';
                                            if ($store == 'Google Play') {
                                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                                $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://play.google.com/store/apps/details?id=' . $row->page_id . '&hl=vi&gl=US&reviewId=' . $row->fb_id);
                                            } else {
                                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);
                                            }

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $store);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Reviewmap") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://www.google.com/maps/contrib/' . $row->content_from_id . '/place/' . $row->page_id);
                                            // $urlMention = 'https://www.google.com/maps/contrib/' . $row->content_from_id . '/place/' . $row->page_id;
                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share . "/5 sao");
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Threads") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $row->message = htmlspecialchars($row->message, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(cleanExcelText($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            // $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://www.threads.net/@' . $row->page_id . '/post/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, isset($row->total_like) ? $row->total_like : 0);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, isset($row->total_share) ? $row->total_share : 0);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, isset($row->child_count) ? $row->child_count : 0);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        }
                                        $i++;
                                    }
                                }
                                // echo "\n merge " . "A" . ($i - $j) . ":A" . ($i-1) ."\n";
                                $objPHPExcel->getActiveSheet()->mergeCells("A" . ($i - $j) . ":A" . ($i - 1));
                                $objPHPExcel->getActiveSheet()->getStyle("A" . ($i - $j) . ":A" . ($i - 1))->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $i += 2;
                            }
                        }
                        $i += 3;
                    }
                }
            } else {
                foreach ($list_object as $sub_index => $sub_objs) {
                    foreach ($sub_objs as $index => $item) {
                        $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $item->name);
                        $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleBoldText)->applyFromArray($styleLeftAlignment)->applyFromArray($styleBlueColor);
                        $i += 2;

                        foreach ($active_channels as $channel => $is_active) {
                            if (!$is_active)
                                continue;
                            if ($channel == "Facebook") {
                                $is_checked = isset($data_check['Facebook']) ? $data_check['Facebook']->is_checked : 0;
                                $is_read = isset($data_check['Facebook']) ? $data_check['Facebook']->is_read : 0;
                                $json_doc = __generateJsonSocial($brand_id, [$item->id], $start_date, $end_date, '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchSocial($json_doc);
                            } else if ($channel == "Ifollow") {
                                $is_checked = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_checked : 0;
                                $is_read = isset($data_check['Ifollow']) ? $data_check['Ifollow']->is_read : 0;
                                $json_doc = __generateJsonIfollow($brand_id, [$item->id], $start_date, $end_date, '', '', null, '', 1000, $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchIfollow($json_doc);
                            } else if ($channel == "Instagram") {
                                $is_checked = isset($data_check['Instagram']) ? $data_check['Instagram']->is_checked : 0;
                                $json_doc = __generateJsonInstagram($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked);
                                $data_query = _searchInstagram($json_doc);
                            } else if ($channel == "Youtube") {
                                $is_checked = isset($data_check['Youtube']) ? $data_check['Youtube']->is_checked : 0;
                                $is_read = isset($data_check['Youtube']) ? $data_check['Youtube']->is_read : 0;
                                $json_doc = __generateJsonYoutube($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchYoutube($json_doc);
                            } else if ($channel == "Tiktok") {
                                $is_checked = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_checked : 0;
                                $is_read = isset($data_check['Tiktok']) ? $data_check['Tiktok']->is_read : 0;
                                $json_doc = __generateJsonTiktok($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTiktok($json_doc);
                            } else if ($channel == "Twitter") {
                                $is_checked = isset($data_check['Twitter']) ? $data_check['Twitter']->is_checked : 0;
                                $is_read = isset($data_check['Twitter']) ? $data_check['Twitter']->is_read : 0;
                                $json_doc = __generateJsonTwitter($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchTwitter($json_doc);
                            } else if ($channel == "Reviewapp") {
                                $is_checked = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_checked : 0;
                                $is_read = isset($data_check['Reviewapp']) ? $data_check['Reviewapp']->is_read : 0;
                                $json_doc = __generateJsonReviewapp($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchReviewapp($json_doc);
                            } else if ($channel == "Reviewmap") {
                                $is_checked = isset($data_check['Reviewmap']) ? $data_check['Reviewmap']->is_checked : 0;
                                $is_read = isset($data_check['Reviewmap']) ? $data_check['Reviewmap']->is_read : 0;
                                $json_doc = __generateJsonReviewmap($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchReviewmap($json_doc);
                            } else if ($channel == "Zalo") {
                                $is_checked = isset($data_check['Zalo']) ? $data_check['Zalo']->is_checked : 0;
                                $is_read = isset($data_check['Zalo']) ? $data_check['Zalo']->is_read : 0;
                                $json_doc = __generateJsonZalo($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchZalo($json_doc);
                            } else if ($channel == "Ecommerce") {
                                $is_checked = isset($data_check['Ecommerce']) ? $data_check['Ecommerce']->is_checked : 0;
                                $is_read = isset($data_check['Ecommerce']) ? $data_check['Ecommerce']->is_read : 0;
                                $json_doc = __generateJsonEcommerce($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, $is_read, $is_subbrand);
                                $data_query = _searchEcommerce($json_doc);
                            } else if ($channel == "Paper") {
                                $is_checked = isset($data_check['Paper']) ? $data_check['Paper']->is_checked : 0;
                                $json_doc = __generateJsonPaper($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, null, $is_subbrand);
                                $data_query = _searchPaper($json_doc);
                            } else if ($channel == "Tv") {
                                $is_checked = isset($data_check['Tv']) ? $data_check['Tv']->is_checked : 0;
                                $json_doc = __generateJsonTv($brand_id, [$item->id], $start_date, $end_date, '', 1000, '', $is_checked, null, $is_subbrand);
                                $data_query = _searchTv($json_doc);
                            }

                            $data = $data_query->hits->hits;
                            // echo $channel . ": " . count($data) . "\n";
                            if (count($data)) {
                                $data = sortByState($data, $channel);
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, "Nền tảng");
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                // iFollow
                                $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, 'STT');
                                $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, 'Tiêu đề / Nội dung');
                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, 'Nguồn');
                                $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, 'Ðánh giá (Tích cực, Tiêu cực, trung tính)');
                                $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                if ($channel == "Facebook" || $channel == "Youtube" || $channel == "Tiktok" || $channel == "Twitter" || $channel == "Instagram" || $channel == "Threads") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Likes');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Shares');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);

                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Comments');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                if ($channel == "Reviewapp" || $channel == "Reviewmap") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Store');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Star');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                if ($channel == "Ecommerce") {
                                    $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, 'Star');
                                    $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, 'Phân Loại Tin');
                                    $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                    $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, 'Loại Nền Tảng');
                                    $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styles)->applyFromArray($styleCenterAlignment);
                                }

                                $i++;
                                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $channel == "Ifollow" ? "News" : $channel);
                                $objPHPExcel->getActiveSheet()->getStyle('A' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $j = 0;
                                if ($channel == "Ecommerce") {
                                    $ecomerceShopee = [];
                                    $otherEcomerce = [];

                                    foreach ($data as $key => $row) {
                                        $j++;
                                        $_id = $row->_id;
                                        $row = $row->_source;
                                        if ($row->type == 0) {
                                            $star = $row->rate_count;
                                            $type_mention = "Post";

                                            if ($row->type_ecomerce == 1) {
                                                $url = 'https://shopee.vn/' . $row->content_from_name . '-i.' . $row->content_from_id . '.' . $row->fb_id;
                                                $url_page_name = 'https://shopee.vn/' . $row->content_from_name;
                                            } else {
                                                $url = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row->fb_id;
                                                $url_page_name = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row->fb_id;
                                            }
                                        } else {
                                            $star = $row->star;
                                            $type_mention = "Review";
                                            if ($row->type_ecomerce == 1) {
                                                $url = 'https://shopee.vn/' . $row->page_name . '-i.' . $row->page_id . '.' . $row->fb_parent_id;
                                                $url_page_name = 'https://shopee.vn/' . $row->page_name;
                                            } else {
                                                $url = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row->fb_parent_id;
                                                $url_page_name = 'http://ecommerce.monitaz.com/detail/show/?product_id=' . $row->fb_parent_id;
                                            }
                                        }

                                        // Phân loại dữ liệu theo type_ecomerce
                                        if ($row->type_ecomerce == 1) {
                                            $ecommerce_name = "Ecommerce Shopee";
                                            $ecomerceShopee[] = [
                                                'url' => $url,
                                                'message' => remove3and4bytesCharFromUtf8Str($row->message),
                                                'content_from_name' => $row->content_from_name,
                                                'state' => $row->state,
                                                'star' => $star,
                                                'type_mention' => $type_mention,
                                                'ecommerce_name' => $ecommerce_name
                                            ];
                                        } else {
                                            $ecommerce_name = "Ecommerce Tiktok Shop";
                                            $otherEcomerce[] = [
                                                'url' => $url,
                                                'message' => remove3and4bytesCharFromUtf8Str($row->message),
                                                'content_from_name' => $row->content_from_name,
                                                'state' => $row->state,
                                                'star' => $star,
                                                'type_mention' => $type_mention,
                                                'ecommerce_name' => $ecommerce_name
                                            ];
                                        }
                                    }

                                    $a = 0;

                                    foreach ($ecomerceShopee as $s => $dataRow) {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $a + 1);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit($dataRow['message'], PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl($dataRow['url']);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $dataRow['content_from_name']);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($dataRow['state'] == 2) ? 'Tiêu cực' : (($dataRow['state'] == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $dataRow['star']);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $dataRow['type_mention']);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $dataRow['ecommerce_name']);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $i++;
                                        $a++;
                                    }

                                    // Sau đó là các dòng còn lại (Ecommerce Tiktok Shop)
                                    foreach ($otherEcomerce as $s => $dataRow) {
                                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $a + 1);
                                        $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit($dataRow['message'], PHPExcel_Cell_DataType::TYPE_STRING);
                                        $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                        $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl($dataRow['url']);

                                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $dataRow['content_from_name']);
                                        $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($dataRow['state'] == 2) ? 'Tiêu cực' : (($dataRow['state'] == 1) ? 'Tích cực' : 'Trung tính'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                        $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $dataRow['star']);
                                        $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $dataRow['type_mention']);
                                        $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $dataRow['ecommerce_name']);
                                        $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                        $i++;
                                        $a++;
                                    }
                                } else {
                                    foreach ($data as $key => $row) {
                                        $j++;
                                        $_id = $row->_id;
                                        $row = $row->_source;
                                        if ($channel == "Facebook") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl('https://facebook.com/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Ifollow" || $channel == "Forum") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->web_title));
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($row->web_url);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->web_page_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->web_state == 2) ? 'Tiêu cực' : (($row->web_state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                        } else if ($channel == "Paper") {
                                            $urlMentionPaper = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=2';
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->paper_title));
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($urlMentionPaper);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->paper_page_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->paper_state == 2) ? 'Tiêu cực' : (($row->paper_state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                        } else if ($channel == "Tv") {
                                            $urlMentionTv = 'http://data.monitaz.vn/detail/index/' . $_id . '?_channel=3';
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->setCellValue('C' . $i, remove3and4bytesCharFromUtf8Str($row->tv_title));
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCellByColumnAndRow(2, $i)->getHyperlink()->setUrl($urlMentionTv);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->tv_page_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->tv_state == 2) ? 'Tiêu cực' : (($row->tv_state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                        } else if ($channel == "Youtube") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://youtube.com/watch?v=' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Tiktok") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://tiktok.com/@' . $row->page_id . '/video/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Instagram") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://instagram.com/' . $row->page_id . '/p/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Twitter") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://twitter.com/' . $row->page_id . '/status/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, $row->child_count);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Zalo") {
                                            // $url = str_replace('https://', '', 'https://rd.zapps.vn/detail/' . $row->page_id . '?id=' . $row->fb_id . '&src=profile');
                                            $link_user = $domain_zalo_url . "/detail?content_from_id=" . $row->content_from_id . "&fb_id=" . $row->fb_id;
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('http://' . $link_user);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);
                                        } else if ($channel == "Reviewapp") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $store = strpos($_id, $row->fb_id) !== false ? 'App Store' : 'Google Play';
                                            if ($store == 'Google Play') {
                                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                                $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://play.google.com/store/apps/details?id=' . $row->page_id . '&hl=vi&gl=US&reviewId=' . $row->fb_id);
                                            } else {
                                                $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);
                                            }

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $store);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Reviewmap") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://www.google.com/maps/contrib/' . $row->content_from_id . '/place/' . $row->page_id);
                                            // $urlMention = 'https://www.google.com/maps/contrib/' . $row->content_from_id . '/place/' . $row->page_id;
                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, $row->total_like);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, $row->total_share . "/5 sao");
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        } else if ($channel == "Threads") {
                                            $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $j);
                                            $objPHPExcel->getActiveSheet()->getStyle('B' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->setValueExplicit(remove3and4bytesCharFromUtf8Str($row->message), PHPExcel_Cell_DataType::TYPE_STRING);
                                            $objPHPExcel->getActiveSheet()->getStyle('C' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment)->applyFromArray($styleLinkText);
                                            $objPHPExcel->getActiveSheet()->getCell('C' . $i)->getHyperlink()->setUrl('https://www.threads.net/@' . $row->page_id . '/post/' . $row->fb_id);

                                            $objPHPExcel->getActiveSheet()->setCellValue('D' . $i, $row->content_from_name);
                                            $objPHPExcel->getActiveSheet()->getStyle('D' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleLeftAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('E' . $i, ($row->state == 2) ? 'Tiêu cực' : (($row->state == 1) ? 'Tích cực' : 'Trung tính'));
                                            $objPHPExcel->getActiveSheet()->getStyle('E' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment)->applyFromArray($styleWrapText);

                                            $objPHPExcel->getActiveSheet()->setCellValue('F' . $i, isset($row->total_like) ? $row->total_like : 0);
                                            $objPHPExcel->getActiveSheet()->getStyle('F' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('G' . $i, isset($row->total_share) ? $row->total_share : 0);
                                            $objPHPExcel->getActiveSheet()->getStyle('G' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);

                                            $objPHPExcel->getActiveSheet()->setCellValue('H' . $i, isset($row->child_count) ? $row->child_count : 0);
                                            $objPHPExcel->getActiveSheet()->getStyle('H' . $i)->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                        }
                                        $i++;
                                    }
                                }
                                // echo "\n merge " . "A" . ($i - $j) . ":A" . ($i-1) ."\n";
                                $objPHPExcel->getActiveSheet()->mergeCells("A" . ($i - $j) . ":A" . ($i - 1));
                                $objPHPExcel->getActiveSheet()->getStyle("A" . ($i - $j) . ":A" . ($i - 1))->applyFromArray($styleAllBorders)->applyFromArray($styleCenterAlignment);
                                $i += 2;
                            }
                        }
                        $i += 3;
                    }
                }
            }
        }

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $objPHPExcel->setActiveSheetIndex(0);

        // Save Excel 2007 file
        $writer = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');

        if (file_exists($pathToOutputFile) && $saved_folder != 'tmp_reports') {
            $message = "Config ID " . $setting_id . '-' . $setting['type'] . ' đã tồn tại báo cáo'
                . "\nFile Báo cáo: " . $pathToOutputFile
                . "\nKhông gửi email nữa !!!";
            telegramAlert($token, $chatID, $message);
            return [$outputFile, false];
        }

        $writer->save($pathToOutputFile);

        $end_time = date("Y-m-d H:i:s");
        $timeEnd = time() - $timeStart;
        // print_r($pathToOutputFile);
        // echo 'File bao cao xong trong: ' . $timeEnd . ' seconds';
        // echo '<br>';
        // echo "\n";
        $um->logReportTimeRun($setting_id, date("Y-m-d"), $setting['type'], $outputFile, $begin_time, $end_time, $timeEnd, $end_time);


        // test open command
        // $list_mail = ['<EMAIL>'];

        if (!$has_send_mail || !count($list_mail)) {
            if ($setting_id == 94 && $setting['type'] == 'weekly') {
                return [$outputFile . ', ' . $outputFileJson, false];
            } else {
                return [$outputFile, false];
            }
        }
        $email = $list_mail;
        $titlemail = $report_type_text . " Report - Monitaz Social Listening";
        if ($setting_id == 100) $titlemail = $report_type_text . " Report Skoda - Monitaz Social Listening";
        $CCs = [
            ["mail" => "<EMAIL>", "name" => "Tiến Đạt"],
            ["mail" => "<EMAIL>", "name" => "Xuân Tuấn"],
        ];

        $attachments = [$pathToOutputFile];
        if ($setting_id == 94 && $setting['type'] == 'weekly') $attachments = [$pathToOutputFile, $pathFileJson];
        $message = getMailMessage($report_type_text_text, $brand_name, $date_report);
        $response = smtpmailer("<EMAIL>", $email, 'Monitaz', $CCs, $titlemail, $message, $attachments);
        // <NAME_EMAIL> error open it
        // $response = smtpmailerUseMailAlert("<EMAIL>", $email, 'Monitaz', $CCs, $titlemail, $message, $attachments);
        if (!$response) {
            $messageSendMail = "Config ID " . $setting_id . ' - ' . $brand_name . ' - ' . $setting['type']
                . "\nLỗi Gửi Email Rồi !!!";
            telegramAlert($token, $chatID, $messageSendMail);
        }

        if ($setting_id == 94 && $setting['type'] == 'weekly') {
            return [$outputFile . ', ' . $outputFileJson, $response];
        } else {
            return [$outputFile, $response];
        }
    }
}
