<?php

namespace App\Service;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Symfony\Component\DomCrawler\Crawler;

class ApiReactionService
{


    public function tiktok($uid)
    {
        try {
            $http = \Http::get("https://api-tiktok-v1.monitaz.asia/tuanpx/video-detail/", [
                'video_id' => $uid
            ]);
            if (!$http->successful()) {
                return [];
            }
            $data = $http->json();
            if ($data['status'] != 'success') {
                return [];
            }
            return $data['data'];
        } catch (\Exception $exception) {
            TelegramService::sendMessage("Lõi call api Tiktok: " . $exception->getMessage());
            return [];
        }
    }

    public function youtube(array $ids)
    {
        try {
            if (empty($ids)) {
                return [];
            }
            $keys = explode("\n", get_config('ytb_api_key')) ?? [];
            if (empty($keys)) {
                return [];
            }
            $http = \Http::get("https://www.googleapis.com/youtube/v3/videos", [
                'part' => 'statistics',
                'id' => implode(',', $ids),
                'key' => $keys[array_rand($keys)]
            ]);
            if (!$http->successful()) {
                return [];
            }
            $data = $http->json();
            return $data['items'];
        } catch (\Exception $exception) {
            TelegramService::sendMessage("Lõi call api Youtube: " . $exception->getMessage());

            return [];
        }
    }

    public function instagram($id)
    {
        try {
            $http = \Http::get("https://www.instagram.com/p/{$id}/embed/captioned/?cr=2&v=14&wp=1080");
            if (!$http->successful()) {
                return [];
            }
            $data = $http->body();
            $html = stripcslashes($data);

            preg_match('/"edge_media_to_comment"\s*:\s*({.*?})\s*,\s*"(?:[\w_]+)"\s*:/s', $html, $matchsComment);
            $commentCount = 0;
            if (!empty(@$matchsComment[1])) {
                $dataComment = json_decode($matchsComment[1], true);
                $commentCount = $dataComment['count'] ?? 0;
            }
            if (empty($commentCount)) {
                $crawler = new Crawler($data);
                $node = $crawler->filter('.CaptionComments');
                if ($node->count()) {
                    $text = $node->text();
                    $commentCount = (int)preg_replace('/[^0-9]/', '', $text);
                }
            }
            preg_match('/"edge_liked_by"\s*:\s*({.*?})\s*,\s*"(?:[\w_]+)"\s*:/s', $html, $matchsLike);
            $likeCount = 0;
            if (!empty(@$matchsLike[1])) {
                $dataComment = json_decode($matchsLike[1], true);
                $likeCount = $dataComment['count'] ?? 0;
            }
            if (empty($likeCount)) {
                $crawler = new Crawler($data);
                $node = $crawler->filter('.SocialProof');
                if ($node->count()) {
                    $text = $node->text();
                    $likeCount = (int)preg_replace('/[^0-9]/', '', $text);
                }
            }
            return [
                'comment' => $commentCount,
                'like' => $likeCount
            ];
        } catch (\Exception $exception) {
            TelegramService::sendMessage("Lõi call api instagram: " . $exception->getMessage());
            return [];
        }
    }

    public function threads(array $ids)
    {
        try {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://www.threads.com/graphql/query',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS =>
                    'av=17841468668033477' .
                    '&__user=0' .
                    '&__a=1' .
                    '&__req=l' .
                    '&__hs=' . urlencode('20215.HYP:barcelona_web_pkg.2.1...0') .
                    '&dpr=1' .
                    '&__ccg=GOOD' .
                    '&__rev=1022566272' .
                    '&__s=' . urlencode('3pmecl:7h5odx:rcjehw') .
                    '&__hsi=7501528732238017372' .
                    '&__dyn=' . rawurlencode('7xeUmwlEnwn8K2Wmh0no6u5U4e0yoW3q32360CEbo1nEhw2nVE4W0qa0FE2awgo9oO0n24oaEd82lwv89k2C1Fwc60D85m1mzXwae4UaEW0Loco5G0zK5o4q0HU1IEGdwtU2ewbS1LwTwKG0hq1Iwqo9EpwUwiQ1mwLwHxW17y9UjgbVE-0UE5O1tK1Uw') .
                    '&__csr=' . rawurlencode('gH2sLFiR9HYIDsyOWsj8BhnLOi9bnltrqo-bBBK6Xgpz89osEyBp4m00msu3S3O0q-0u05wlxd3AaDweTwnU550tNNkbN0mwqU4re0LFNoowlk3R1C5Q0kVaAjyU8E0h_wko1yU23DVo3CK4U5i0rS17we2Ejk8cb0rqxd1rgB160Pk0-o11K0Fm0qi2e14xq1whUxkskmu8x2UG3xh0DglhD8h0Izywj824gK05ComEYWwIzyw08Kqup0Zg') .
                    '&__comet_req=29' .
                    '&fb_dtsg=' . rawurlencode('NAcP7UnvPSrIPCgJHruem0gKiXsTi0mKtLuH-sUnREOnysfeD-fmo7A:17864642926059691:1746504490') .
                    '&jazoest=26439' .
                    '&lsd=UAP141lmIaBtX4uwApPcPU' .
                    '&__spin_r=1022566272' .
                    '&__spin_b=trunk' .
                    '&__spin_t=1746585763' .
                    '&__crn=' . rawurlencode('comet.threads.BarcelonaPostColumnRoute') .
                    '&fb_api_caller_class=RelayModern' .
                    '&fb_api_req_friendly_name=useBarcelonaBatchedDynamicPostCountsSubscriptionQuery' .
                    '&variables=' . rawurlencode(json_encode(['post_ids' => [...$ids]])) .
                    '&server_timestamps=true' .
                    '&doc_id=9584569441631294',
                CURLOPT_HTTPHEADER => array(
                    'accept: */*',
                    'accept-language: vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
                    'content-type: application/x-www-form-urlencoded',
                    'dnt: 1',
                    'origin: https://www.threads.com',
                    'priority: u=1, i',
                    'referer: https://www.threads.com/@mary_dasville/post/DJADNJSzWOU',
                    'sec-ch-prefers-color-scheme: light',
                    'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'sec-ch-ua-full-version-list: "Chromium";v="136.0.7103.48", "Google Chrome";v="136.0.7103.48", "Not.A/Brand";v="********"',
                    'sec-ch-ua-mobile: ?0',
                    'sec-ch-ua-model: ""',
                    'sec-ch-ua-platform: "Windows"',
                    'sec-ch-ua-platform-version: "10.0.0"',
                    'sec-fetch-dest: empty',
                    'sec-fetch-mode: cors',
                    'sec-fetch-site: same-origin',
                    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'x-asbd-id: 359341',
                    'x-bloks-version-id: cf39c6377e026a1760665d37cfc1b31a93ae150e5d202da0aa6d36af9f0749fd',
                    'x-csrftoken: nv8HVVzZsVKYtnaHNx7xDQvQtJ1XbGfn',
                    'x-fb-friendly-name: useBarcelonaBatchedDynamicPostCountsSubscriptionQuery',
                    'x-fb-lsd: UAP141lmIaBtX4uwApPcPU',
                    'x-ig-app-id: 238260118697367',
                    'x-root-field-name: xdt_text_app_posts_batch',
                    'Cookie: ig_did=AE26DBE5-6128-4AAB-8E2D-36D356953490; ps_l=1; ps_n=1; mid=aBlrRwALAAEtrmk9wz0cwO8tBDdS; csrftoken=nv8HVVzZsVKYtnaHNx7xDQvQtJ1XbGfn; ds_user_id=68747816934; sessionid=68747816934%3APeMf9Z5kw0f8y9%3A16%3AAYcjXP9a9cXy7syOMto_RwI71-eumqaRnMMzGDtJhw; csrftoken=nv8HVVzZsVKYtnaHNx7xDQvQtJ1XbGfn; ds_user_id=68747816934; ig_did=3C99B40D-BD4D-4BC1-B115-706A48AF8DA5; mid=aBrJbwALAAGQNquj9XDC0MnicCS5; rur="EAG\\05468747816934\\0541778901774:01f719fcb4dbebbfde21012cd8735cd982b843039685cf6d458635ecae14b48da90ee057"; csrftoken=nv8HVVzZsVKYtnaHNx7xDQvQtJ1XbGfn; ds_user_id=68747816934; rur="EAG\\05468747816934\\0541778902409:01f71e3d0d7bbe185df9ba3b3b50b7d0e648782bdfa7565138a23098c5369fdf94812751"'
                ),
            ));

            $response = curl_exec($curl);
            $data = json_decode($response, true);
            if (empty($data)) {
                return [];
            }
            if ($data['status'] != 'ok') {
                return [];
            }
            return $data['data']['data']['posts'];
        } catch (\Exception $exception) {
            return [];
        }

    }


    public function facebookReactionIds(array $ids)
    {
        try {
            $token = $this->getTokenFacebook();
//            $token = "EAAAAUaZA8jlABO44Xyzv9HD3EGYwlHgiRsBbzlWZBzPnZBnxF1LfhZAswSO0ryk7I3vaaQEqy9TobQa3Hh1FTKZBxTpiPMzw3QDrLch7CnD6W49bGPtZCkWGAbLddZAzoWIelsiuwESHB90RmO7JRBfh4ojzJhhRM1wOVsvyvRjFBr1yvAdmCrkNdfiuX9jcAzvp2UIFwZDZD";
            $http = \Http::get("https://graph.facebook.com/v2.6/", [
                'fields' => 'created_time,reactions.limit(0).summary(true).as(like),shares.limit(0).summary(true).as(shares),comments.limit(0).summary(true).as(comments),type,link,from,object_id,parent_id',
                'access_token' => $token,
                'ids' => implode(',', $ids)
            ]);
            $json = $http->json();
            if (isset($json['error'])) {
                if ($json['error']['code'] == 100) {
                    TelegramService::sendMessage($json['error']['message']);
                    TelegramService::sendMessage(@$json['error']['error_user_title']);
                    TelegramService::sendMessage(@$json['error']['error_user_msg']);
                }
                return [
                    'code' => $json['error']['code'],
                    'message' => $json['error']['message'],
                    'error' => true
                ];
            }
            if (!$http->successful() || empty($http->json())) {
                throw new \Exception('Facebook API failed or returned empty response');
            }
            return $json;
        } catch (\Exception $exception) {
            throw $exception;
        }
    }

    public function facebookReactionSingle($id)
    {
        try {
            $http = \Http::get("https://graph.facebook.com/v2.6/{$id}", [
                'fields' => 'created_time,reactions.limit(0).summary(true).as(like),shares.limit(0).summary(true).as(shares),comments.limit(0).summary(true).as(comments),type,link,from,object_id,parent_id',
                'access_token' => $this->getTokenFacebook(),
            ]);

            $json = $http->json();
            if (isset($json['error'])) {
                if ($json['error']['code'] == 100) {
                    TelegramService::sendMessage($json['error']['message']);
                    TelegramService::sendMessage(@$json['error']['error_user_title']);
                    TelegramService::sendMessage(@$json['error']['error_user_msg']);
                }
                return [
                    'code' => $json['error']['code'],
                    'message' => $json['error']['message'],
                    'error' => true
                ];
            }

            if (!$http->successful()) {
                return null;
            }
            return $json;

        } catch (\Exception $exception) {
            return null;
        }
    }

    private function getTokenFacebook()
    {
        $keys = explode("\n", trim(get_config('fb_tokens'))) ?? [];
        if (empty($keys)) {
            return null;
        }
        return $keys[array_rand($keys)];
    }
}
