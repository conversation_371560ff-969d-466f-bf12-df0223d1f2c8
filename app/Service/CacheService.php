<?php

namespace App\Service;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class CacheService
{
    const CONFIG_FE = 'config_fe';

    public function __construct()
    {
        if (! Cache::has(self::CONFIG_FE)) {
            $this->loadConfig();
        }
    }

    public function loadConfig()
    {
        return Cache::remember(self::CONFIG_FE, 604800, function () {
            return Setting::select(['key', 'value'])->get()->keyBy('key')->toArray();
        });
    }

    public function getConfig($key, $default = null)
    {
        $data = Cache::get(CacheService::CONFIG_FE, []);
        if (isset($data[$key]['value'])) {
            return $data[$key]['value'];
        }

        return Setting::where('key', $key)->value('value') ?? $default;
    }
}
