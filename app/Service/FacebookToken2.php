<?php

namespace App\Service;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class FacebookToken2
{
    protected array $cookies;
    protected string $appId;
    protected array $header;

    //    $message Android = '256002347743983';
    //    $message Ios = '454999426878';
    //    $fb_android = '275254692598279';
    //    Instagram for Android	 = '124024574287414';
    //    Facebook Pages Manager = '1418496762136177';
    //    Instagram = '389801252';

    public function __construct(string $cookieString, string $appId = '256002347743983')
    {
        $this->cookies = $this->parseCookies($cookieString);
        $this->appId = $appId;
        $this->header = [
            'authority' => 'www.facebook.com',
            'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/jxl,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language' => 'vi,en-US;q=0.9,en;q=0.8',
            'cache-control' => 'max-age=0',
            'dnt' => '1',
            'dpr' => '1.25',
            'sec-ch-ua' => '"Chromium";v="117", "Not;A=Brand";v="8"',
            'sec-ch-ua-full-version-list' => '"Chromium";v="117.0.5938.157", "Not;A=Brand";v="8.0.0.0"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-model' => '""',
            'sec-ch-ua-platform' => '"Windows"',
            'sec-ch-ua-platform-version' => '"15.0.0"',
            'sec-fetch-dest' => 'document',
            'sec-fetch-mode' => 'navigate',
            'sec-fetch-site' => 'same-origin',
            'sec-fetch-user' => '?1',
            'upgrade-insecure-requests' => '1',
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
            'viewport-width' => '1038',
        ];
    }

    private function getCUser(): ?string
    {
        return $this->cookies['c_user'] ?? null;
    }

    private function parseCookies(string $cookieString): array
    {
        $cookies = [];
        foreach (explode(';', $cookieString) as $pair) {
            [$key, $value] = array_pad(explode('=', trim($pair), 2), 2, '');
            $cookies[$key] = $value;
        }
        return array_filter($cookies);
    }

    private function getFbDtsg(): ?string
    {
        $response = Http::withHeaders($this->header)
            ->withCookies($this->cookies, 'facebook.com')
            ->get('https://www.facebook.com/v2.3/dialog/oauth', [
                'redirect_uri' => 'fbconnect://success',
                'scope' => 'email,publish_actions,business_management,publish_pages,user_about_me,user_actions.books,user_actions.music,user_actions.news,user_actions.video,user_activities,user_birthday,user_education_history,user_events,user_games_activity,user_groups,user_hometown,user_interests,user_likes,user_location,user_notes,user_photos,user_questions,user_relationship_details,user_relationships,user_religion_politics,user_status,user_subscriptions,user_videos,user_website,user_work_history,friends_about_me,friends_actions.books,friends_actions.music,friends_actions.news,friends_actions.video,friends_activities,friends_birthday,friends_education_history,friends_events,friends_games_activity,friends_groups,friends_hometown,friends_interests,friends_likes,friends_location,friends_notes,friends_photos,friends_questions,friends_relationship_details,friends_relationships,friends_religion_politics,friends_status,friends_subscriptions,friends_videos,friends_website,friends_work_history,ads_management,create_event,create_note,export_stream,friends_online_presence,manage_friendlists,manage_notifications,manage_pages,photo_upload,publish_stream,read_friendlists,read_insights,read_mailbox,read_page_mailboxes,read_requests,read_stream,rsvp_event,share_item,sms,status_update,user_online_presence,video_upload,xmpp_login',
                'response_type' => 'token,code',
                'client_id' => '356275264482347',
            ]);
        if (preg_match('/\["DTSGInitData"\s*,\s*\[\]\s*,\s*{"token":"(.*?)"/', $response->body(), $matches)) {
            return $matches[1];
        }
        return null;
    }

    private function changeToken(string $accessToken): ?string
    {
        $response = Http::asForm()->post('https://api.facebook.com/method/auth.getSessionforApp', [
            'access_token' => $accessToken,
            'format' => 'json',
            'new_app_id' => $this->appId,
            'generate_session_cookies' => '0',
        ]);
        $token = $response['access_token'] ?? null;
        Http::delete('https://graph.facebook.com/me/permissions', [
            'method' => 'DELETE',
            'access_token' => $token,
        ]);
        return $token;
    }

    private function token($app_id)
    {
        $c_user = (string)$this->getCUser();
        $fb_dtsg = $this->getFbDtsg();
        if (is_null($fb_dtsg)) {
            return null;
        }
        $uuid = Str::uuid()->toString();
        $uuid2 = Str::uuid()->toString();

        $data = [
            'av' => $c_user,
            '__user' => $c_user,
            'fb_dtsg' => $fb_dtsg,
            'fb_api_caller_class' => 'RelayModern',
            'fb_api_req_friendly_name' => 'useCometConsentPromptEndOfFlowBatchedMutation',
            'variables' => "{'input':{'client_mutation_id':'4','actor_id':'{$c_user}','config_enum':'GDP_READ','device_id':null,'experience_id':'{$uuid}','extra_params_json':'{\\'app_id\\':\\'{$app_id}\\',\\'display\\':\\'\\\\\\'popup\\\\\\'\\',\\'kid_directed_site\\':\\'false\\',\\'logger_id\\':\\'\\\\\\'{$uuid2}\\\\\\'\\',\\'next\\':\\'\\\\\\'read\\\\\\'\\',\\'redirect_uri\\':\\'\\\\\\'https:\\\\\\\\\\\\/\\\\\\\\\\\\/www.facebook.com\\\\\\\\\\\\/connect\\\\\\\\\\\\/login_success.html\\\\\\'\\',\\'response_type\\':\\'\\\\\\'token\\\\\\'\\',\\'return_scopes\\':\\'false\\',\\'scope\\':\\'[\\\\\\'email\\\\\\',\\\\\\'manage_fundraisers\\\\\\',\\\\\\'read_custom_friendlists\\\\\\',\\\\\\'read_insights\\\\\\',\\\\\\'rsvp_event\\\\\\',\\\\\\'xmpp_login\\\\\\',\\\\\\'offline_access\\\\\\',\\\\\\'publish_video\\\\\\',\\\\\\'openid\\\\\\',\\\\\\'catalog_management\\\\\\',\\\\\\'user_messenger_contact\\\\\\',\\\\\\'gaming_user_locale\\\\\\',\\\\\\'private_computation_access\\\\\\',\\\\\\'user_managed_groups\\\\\\',\\\\\\'groups_show_list\\\\\\',\\\\\\'pages_manage_cta\\\\\\',\\\\\\'pages_manage_instant_articles\\\\\\',\\\\\\'pages_show_list\\\\\\',\\\\\\'pages_messaging\\\\\\',\\\\\\'pages_messaging_phone_number\\\\\\',\\\\\\'pages_messaging_subscriptions\\\\\\',\\\\\\'read_page_mailboxes\\\\\\',\\\\\\'ads_management\\\\\\',\\\\\\'ads_read\\\\\\',\\\\\\'business_management\\\\\\',\\\\\\'instagram_basic\\\\\\',\\\\\\'instagram_manage_comments\\\\\\',\\\\\\'instagram_manage_insights\\\\\\',\\\\\\'instagram_content_publish\\\\\\',\\\\\\'publish_to_groups\\\\\\',\\\\\\'groups_access_member_info\\\\\\',\\\\\\'leads_retrieval\\\\\\',\\\\\\'whatsapp_business_management\\\\\\',\\\\\\'instagram_manage_messages\\\\\\',\\\\\\'attribution_read\\\\\\',\\\\\\'page_events\\\\\\',\\\\\\'business_creative_transfer\\\\\\',\\\\\\'pages_read_engagement\\\\\\',\\\\\\'pages_manage_metadata\\\\\\',\\\\\\'pages_read_user_content\\\\\\',\\\\\\'pages_manage_ads\\\\\\',\\\\\\'pages_manage_posts\\\\\\',\\\\\\'pages_manage_engagement\\\\\\',\\\\\\'manage_pages\\\\\\',\\\\\\'whatsapp_business_messaging\\\\\\',\\\\\\'instagram_shopping_tag_products\\\\\\',\\\\\\'read_audience_network_insights\\\\\\']\\',\\'sso_key\\':\\'\\\\\\'com\\\\\\'\\',\\'steps\\':\\'{\\\\\\'read\\\\\\':[\\\\\\'email\\\\\\',\\\\\\'openid\\\\\\',\\\\\\'baseline\\\\\\',\\\\\\'public_profile\\\\\\',\\\\\\'read_audience_network_insights\\\\\\']}\\',\\'tp\\':\\'\\\\\\'unspecified\\\\\\'\\',\\'cui_gk\\':\\'\\\\\\'[PASS]:\\\\\\'\\',\\'is_limited_login_shim\\':\\'false\\'}','flow_name':'GDP','flow_step_type':'STANDALONE','outcome':'APPROVED','source':'gdp_delegated','surface':'FACEBOOK_COMET'}}",
            'server_timestamps' => 'true',
            'doc_id' => '6494107973937368',
        ];
        $response = Http::withHeaders([
            'authority' => 'www.facebook.com',
            'accept' => '*/*',
            'accept-language' => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
            'content-type' => 'application/x-www-form-urlencoded',
            'dnt' => '1',
            'origin' => 'https://www.facebook.com',
            'sec-ch-prefers-color-scheme' => 'dark',
            'sec-ch-ua' => '"Chromium";v="117", "Not;A=Brand";v="8"',
            'sec-ch-ua-full-version-list' => '"Chromium";v="117.0.5938.157", "Not;A=Brand";v="8.0.0.0"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-model' => '""',
            'sec-ch-ua-platform' => '"Windows"',
            'sec-ch-ua-platform-version' => '"15.0.0"',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-origin',
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
            'x-fb-friendly-name' => 'useCometConsentPromptEndOfFlowBatchedMutation',
        ])
            ->withCookies($this->cookies, 'facebook.com')
            ->asForm()
            ->post('https://www.facebook.com/api/graphql/', $data);
        $response = $response->json();
        $uri = data_get($response, 'data.run_post_flow_action.uri');

        if (!$uri) {
            return null;
        }
        $parsedUrl = parse_url($uri);
        parse_str($parsedUrl['query'] ?? '', $queryParams);
        $closeUri = $queryParams['close_uri'] ?? null;
        if (!$closeUri) {
            return null;
        }
        $decodedCloseUri = urldecode($closeUri);
        $fragment = parse_url($decodedCloseUri, PHP_URL_FRAGMENT);
        parse_str($fragment ?? '', $fragmentParams);
        return $fragmentParams['access_token'] ?? null;
    }


    public function getToken()
    {
        $token = $this->token(' 350685531728');
        $token2 = $this->changeToken($token);
        return compact('token', 'token2');
    }
}
