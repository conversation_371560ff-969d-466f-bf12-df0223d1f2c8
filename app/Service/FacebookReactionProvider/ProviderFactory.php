<?php

namespace App\Service\FacebookReactionProvider;

use App\Enum\ReactionProviderEnum;
use App\Service\FacebookReactionProvider\Contracts\ProviderInterface;
use App\Service\FacebookReactionProvider\Providers\Sub247Provider;
use InvalidArgumentException;

class ProviderFactory
{
    public static function make(string $key): ProviderInterface
    {
        return match ($key) {
           ReactionProviderEnum::SUB247->value => new Sub247Provider(),
            default   => throw new InvalidArgumentException("API Provider [{$key}] not found."),
        };
    }
}
