<?php

namespace App\Service\FacebookReactionProvider\Providers;

use App\Service\FacebookReactionProvider\Contracts\ProviderInterface;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\Response;

class Sub247Provider implements ProviderInterface
{
    protected string $baseUrl;
    protected string $token;

    public function __construct()
    {
        $this->baseUrl = 'https://sub247.net/api/v2';
        $this->token = 'kmuSiMpPtfiA2WbMg6Nw6DPMxI5phOxRLSujcSGae1ef497b';
    }

    protected function sendRequest(array $payload): mixed
    {
        try{
            $response = Http::post($this->baseUrl, array_merge([
                'key' => $this->token,
                'action' => 'add',
            ], $payload));

            if (!$response->ok()) {
                return $response->json()['error'] ?? null;
            }
            return $response->json();
        }catch (Exception $exception){
            return $exception->getMessage();
        }
    }

    public function share($postId, $serviceId, $quantity = 50): mixed
    {
        return $this->sendRequest([
            'service' => $serviceId,
            'link' => $postId,
            'quantity' => $quantity,
        ]);
    }

    public function like($postId, $serviceId, $quantity = 50): mixed
    {
        return $this->share($postId, $serviceId, $quantity);
    }
    public function comment($postId, $serviceId, array $list, $quantity = 50): mixed
    {
        return $this->sendRequest([
            'service' => $serviceId,
            'link' => $postId,
            'quantity' => $quantity,
            'comments' => implode("\n", $list),
        ]);
    }

    public function getOrder($orderId): array
    {
        return [];
    }
}
