<?php

namespace App\Service;

use Exception;
use Illuminate\Support\Facades\Http;

class TelegramService
{
    public static function sendMessage($content, $chatId = null)
    {
        $cId = "-1002623121529";
        if ($chatId != null) {
            $cId = $chatId;
        }
        if (empty($cId)) {
            return;
        }
//        $url = "https://api.telegram.org/bot7923617875:AAHQIX2_qV8XbpczpYGraNFLoUEDCrQdVMc/sendMessage";
        $url = "http://66.135.16.218:8000/send-message";
        try {
            $res = Http::withHeaders([
                'accept' => 'application/json',
                'Content-Type' => 'application/json'
            ])->post($url, [
                'chat_id' => $cId,
                'message' => $content,
                'parse_mode' => "HTML",
                'token' => '7923617875:AAHQIX2_qV8XbpczpYGraNFLoUEDCrQdVMc'
            ]);
        } catch (Exception $exception) {
            dd($exception->getMessage());
        }
    }

}
