<?php
//
//namespace App\Filament\Pages;
//
//use App\Models\Setting;
//use Filament\Forms\Components\Tabs;
//use Filament\Forms\Components\Textarea;
//use Filament\Forms\Components\TextInput;
//use Filament\Forms\Components\Toggle;
//use Filament\Forms\Concerns\InteractsWithForms;
//use Filament\Forms\Contracts\HasForms;
//use Filament\Forms\Form;
//use Filament\Notifications\Notification;
//use Filament\Pages\Page;
//use Illuminate\Support\Facades\Cache;
//use ValentinMorice\FilamentJsonColumn\JsonColumn;
//
//class Settings extends Page implements HasForms
//{
//    use InteractsWithForms;
//
//    protected static ?string $navigationIcon = 'heroicon-o-document-text';
//
//    protected static string $view = 'filament.pages.settings';
//
//    public ?array $data = [];
//
//    public function mount(): void
//    {
//        $data = Setting::all()->pluck('value', 'key')->toArray();
//        foreach ($data as $key => &$value) {
//            if (json_validate($value)) {
//                $value = json_decode($value, true);
//            }
//        }
//        $this->form->fill($data);
//    }
//
//    public function form(Form $form): Form
//    {
//        return $form
//            ->schema([
//                Tabs::make('Tabs')
//                    ->tabs([
//                        Tabs\Tab::make('Chung')
//                            ->schema([
//                                TextInput::make('total_worker')
//                                    ->numeric()
//                                    ->label("Chunk")
//                                    ->columnSpan(1),
//                                TextInput::make('thread')
//                                    ->numeric()
//                                    ->columnSpan(1),
//                                TextInput::make('first_crawl')
//                                    ->numeric()
//                                    ->label("Lấy tối đa {} (ngày) cho group mới add")
//                                    ->columnSpan(1),
//                                Toggle::make('cron_roating')->label("Cron Roating Proxy"),
//                                Toggle::make('enable')->label("Chạy"),
//                                Toggle::make('auto_truncate')->label("Auto Truncate"),
//                                Toggle::make('save_log')->label("Enable log"),
//
//                            ])->columns(2),
//                        Tabs\Tab::make('Youtube')->schema([
//                            Textarea::make('ytb_api_key')
//                                ->rows(5)
//                                ->label("Youtube API Key")
//                        ]),
//                        Tabs\Tab::make('Facebook')->schema([
//                            Textarea::make('fb_tokens')
//                                ->rows(5)
//                                ->label("Facebook Token")
//                        ]),
//                        Tabs\Tab::make('Proxies')->schema([
//                            Textarea::make('px_proxy')
//                                ->rows(5)
//                                ->label("Proxy"),
//
//                            Textarea::make('proxy_page')
//                                ->rows(5)
//                                ->label("Proxy page"),
//
//                            Textarea::make('proxy_page_main')
//                                ->rows(5)
//                                ->label("Proxy main page"),
//
//                            Textarea::make('proxy_user_page')
//                                ->rows(5)
//                                ->label("Proxy user page"),
//                            Textarea::make('proxy_group')
//                                ->rows(5)
//                                ->label("Proxy group"),
//                            JsonColumn::make('proxy_group_v2')
//                                ->label("Proxy group v2"),
//                            Textarea::make('proxy_tool')
//                                ->rows(5)
//                                ->label("Proxy Tools"),
//                        ])
//                    ]),
//
//            ])->statePath('data');
//    }
//
//    public function submit()
//    {
//        $data = $this->form->getState();
//        foreach ($data as $key => $value) {
//            Setting::updateOrCreate(
//                ['key' => $key],
//                ['value' => is_array($value) ? json_encode($value) : $value]
//            );
//        }
//        Cache::clear();
//        //clear cache
//        $setting = Setting::all()->pluck('value', 'key')->toArray();
//        \Illuminate\Support\Facades\Redis::client()->del('settings');
//        \Illuminate\Support\Facades\Redis::client()->sadd('settings', json_encode($setting));
//        Notification::make()
//            ->title('Saved successfully')
//            ->success()
//            ->send();
//    }
//}
