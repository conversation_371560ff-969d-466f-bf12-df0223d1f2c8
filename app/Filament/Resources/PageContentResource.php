<?php

namespace App\Filament\Resources;

use App\Enum\ReactionProviderEnum;
use App\Enum\ReactionStatusEnum;
use App\Enum\SourceTypeEnum;
use App\Filament\Resources\PageContentResource\Pages;
use App\Filament\Resources\PageContentResource\RelationManagers;
use App\Models\PageContent;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PageContentResource extends Resource
{
    protected static ?string $model = PageContent::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $label = "FB Page Content ";

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Textarea::make('content_raw')
                    ->rows(5)
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('content_ai')
                    ->rows(5)
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('image')
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('comment')
                    ->rows(5)
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('source')
                    ->columnSpanFull(),
                Forms\Components\Select::make('source_type')
                    ->options(SourceTypeEnum::listOptions())
                    ->columnSpanFull(),
                Fieldset::make('Reaction Setting')
                    ->schema([
                        Forms\Components\TextInput::make('like_total'),
                        Forms\Components\TextInput::make('comment_total'),
                        Forms\Components\TextInput::make('share_total'),
                        Forms\Components\Select::make('like_provider')
                            ->options(ReactionProviderEnum::listOptions()),
                        Forms\Components\Select::make('comment_provider')
                            ->options(ReactionProviderEnum::listOptions()),
                        Forms\Components\Select::make('share_provider')
                            ->options(ReactionProviderEnum::listOptions()),
                        Forms\Components\Select::make('like_status')
                            ->options(ReactionStatusEnum::listOptions()),
                        Forms\Components\Select::make('comment_status')
                            ->options(ReactionStatusEnum::listOptions()),
                        Forms\Components\Select::make('share_status')
                            ->options(ReactionStatusEnum::listOptions()),
                        Forms\Components\TextInput::make('reaction.like_response'),
                        Forms\Components\TextInput::make('reaction.comment_response'),
                        Forms\Components\TextInput::make('reaction.share_response'),
                    ])->columns(3),
                Forms\Components\TextInput::make('link_fb')
                    ->columnSpanFull(),

                Forms\Components\Select::make('status')
                    ->options([
                        0 => 'Pending',
                        1 => 'Done AI',
                        2 => 'Done',
                    ])
                    ->default(0),

                Forms\Components\TextInput::make('price')
                    ->numeric()
                    ->default(0),
            ])->columns(4);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->toggleable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('content_raw')
                    ->limit(50)
                    ->searchable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn($state) => match ($state) {
                        0 => 'Pending',
                        1 => 'Done AI',
                        2 => 'Done',
                        default => 'Unknown',
                    })
                    ->colors([
                        'warning' => fn($state) => $state == 0,
                        'info' => fn($state) => $state == 1,
                        'success' => fn($state) => $state == 2,
                        'gray' => fn($state) => !in_array($state, [0, 1, 2]),
                    ])
                ,
                Tables\Columns\TextColumn::make('source_type')
                    ->badge()
                    ->formatStateUsing(fn($state) => SourceTypeEnum::listOptions()[$state])
                    ->colors([
                        'warning' => fn($state) => $state == 1,
                        'success' => fn($state) => $state == 2,
                    ])
                ,
                Tables\Columns\TextColumn::make('link_fb')
                    ->toggleable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('like_total')
                    ->toggleable()
                    ->label("Like")->copyable(),
                Tables\Columns\TextColumn::make('comment_total')
                    ->toggleable()
                    ->label("Comment")->copyable(),
                Tables\Columns\TextColumn::make('share_total')
                    ->toggleable()
                    ->label("Share")->copyable(),
                Tables\Columns\TextColumn::make('price')
                    ->toggleable()
                    ->numeric()
                    ->copyable(),
                Tables\Columns\TextColumn::make('posted_at')
                    ->formatStateUsing(function ($state) {
                        return date('Y-m-d H:i:s', $state);
                    })
                    ->sortable()
                    ->toggleable()
                ,
                Tables\Columns\TextColumn::make('created_at')
                    ->toggleable()
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        0 => 'Pending',
                        1 => 'Done AI',
                        2 => 'Done',
                    ])
                    ->default(2) // lọc mặc định là 'Done'
                    ,
                SelectFilter::make('source_type')
                    ->label('Source')
                    ->options(SourceTypeEnum::listOptions())
                ,
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('posted_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPageContents::route('/'),
            'create' => Pages\CreatePageContent::route('/create'),
            'edit' => Pages\EditPageContent::route('/{record}/edit'),
        ];
    }
}
