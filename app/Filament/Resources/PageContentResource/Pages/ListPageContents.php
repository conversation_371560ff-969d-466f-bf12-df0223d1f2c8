<?php

namespace App\Filament\Resources\PageContentResource\Pages;

use App\Filament\Resources\PageContentResource;
use App\Models\Setting;
use Filament\Actions;
use Filament\Forms\Components\Builder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Http;

class ListPageContents extends ListRecords
{
    protected static string $resource = PageContentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('config')
                ->label("Cấu hình dịch vụ")
                ->icon("heroicon-c-sparkles")
                ->form(function () {

                    $setting = Setting::where('key', 'page_content_reaction')->first();
                    $data = $setting ? json_decode($setting->value, true) : [];
                    return [
                        Toggle::make('enable_reaction_like')
                            ->default($data['enable_reaction_like'] ?? '')
                            ->label("Bật Auto like"),
                        Toggle::make('enable_reaction_cmt')
                            ->default($data['enable_reaction_cmt'] ?? '')
                            ->label("Bật Auto Cmt"),
                        Toggle::make('enable_reaction_share')
                            ->default($data['enable_reaction_share'] ?? '')
                            ->label("Bật Auto Share"),
                        Builder::make('provider_config')
                            ->default($data['provider_config'] ?? [])
                            ->collapsed()
                            ->blocks([
                                Builder\Block::make('config')
                                    ->schema([
                                        TextInput::make('key')
                                            ->label('Key')
                                            ->columnSpanFull()
                                            ->required(),
                                        TextInput::make('like_service_id')
                                            ->label('Like Service ID')
                                            ->required(),
                                        TextInput::make('share_service_id')
                                            ->label('Share Service ID')
                                            ->required(),
                                        TextInput::make('comment_service_id')
                                            ->label('Comment Service ID')
                                            ->required(),
                                        TextInput::make('like_total')
                                            ->label('Like Total')
                                            ->required(),
                                        TextInput::make('share_total')
                                            ->label('Share Total')
                                            ->required(),
                                        TextInput::make('comment_total')
                                            ->label('Comment Total')
                                            ->required(),
                                    ])
                                    ->columns(3),

                            ])
                    ];
                })
                ->action(function ($data) {
                    Setting::updateOrCreate(
                        ['key' => 'page_content_reaction'],
                        ['value' => json_encode($data)]
                    );
                    Notification::make()
                        ->title("Cấu hình thành công")
                        ->success()
                        ->send();
                })
                ->slideOver()
        ];
    }
    function filterAndFormat($data, $category)
    {
        return collect($data)
            ->where('category', $category)
            ->mapWithKeys(function ($item) {
                return [
                    $item['service'] => "{$item['name']} - Rate {$item['rate']} - Min: {$item['min']}, Max: {$item['max']}"
                ];
            })
            ->toArray();
    }
}
