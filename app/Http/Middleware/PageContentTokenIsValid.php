<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PageContentTokenIsValid
{
    const HASH = '$2y$10$TPGDULtD5FatdWKcFNtdfO1sOceEB14NzkNJwbjI7n3cylUSvZ/9G';
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->header('Authorization') != self::HASH) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }
        return $next($request);
    }
}
