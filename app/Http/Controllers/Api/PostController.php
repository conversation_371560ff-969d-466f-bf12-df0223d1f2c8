<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FacebookPost;
use Illuminate\Http\Request;

class PostController extends Controller
{


    public function syncPost(Request $request)
    {
        $ids = $request->json('ids', []);
        if (empty($ids)) {
            return response([
                'status' => false,
                'mess' => 'No data'
            ]);
        }
        $dataInsert = [];
        $now = now();
        foreach ($ids as $id) {
            $dataInsert[] = [
                'post_id' => $id,
                'is_first' => true,
                'status' => true,
                'created_at' => $now,
                'updated_at' => $now,
                'type' => 4,
                'type_get' => 15
            ];
        }
        FacebookPost::insert($dataInsert);
        return response([
            'status' => true,
            'mess' => 'OK'
        ]);
    }
}
