<?php

namespace App\Http\Controllers\Api;

use App\Filament\Resources\ReactionBatchResource;
use App\Http\Controllers\Controller;
use App\Jobs\Redis\CreateReactionJob;
use App\Models\ReactionBatch;
use App\Models\ReactionPost;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ReactionController extends Controller
{


    public function facebookRation(Request $request)
    {
        $ids = $request->json('ids', []);
        if (empty($ids)) {
            return response([
                'status' => false,
                'mess' => 'No data'
            ]);
        }
        $callback = $request->json('callback_url', '');
        $type = $request->json('type', ['count_like', 'count_share', 'count_comment']);
        $uid = Str::uuid()->toString();
        $batch = ReactionBatch::create([
            'name' => $uid,
            'type' => ReactionBatchResource::FACEBOOK_2,
            'total' => count($ids),
            'batch_id' => $uid,
            'callback_url' => $callback
        ]);
        CreateReactionJob::dispatch($ids,$batch,$type)
            ->onConnection("redis")
//            ->onConnection("sync")
            ->onQueue('default');
        return response([
            'status' => true,
            'batch_id' => $batch->id
        ]);

    }

    public function getReactionById()
    {
        $id = \request('id');
        $batch = ReactionPost::where('batch_id', $id)
            ->where('status', 1)
            ->select('post_id', 'data','batch_id')
            ->orderBy('sort', 'asc')
            ->paginate(20);
        return response([
            'status' => true,
            'data' => $batch
        ]);
    }
}
