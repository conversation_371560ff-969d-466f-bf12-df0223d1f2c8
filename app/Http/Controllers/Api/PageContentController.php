<?php

namespace App\Http\Controllers\Api;

use App\Jobs\Redis\PageContentReactionJob;
use App\Models\Setting;
use App\Service\TelegramService;
use Illuminate\Support\Facades\Http;

class PageContentController
{

    public function getContent()
    {
        // source type = 1,2
//        $before = Setting::where('key', 'source_page_before')->first();
//        if (empty($before)){
//            $before = 1;
//        }else{
//            $before = $before->value;
//        }
        // get random in database
        $random = rand(1, 2);

        $page = \App\Models\PageContent::query()
            ->where('status', 1)
            ->where('point_ranking_content','>=',9)
            ->where('source_type',$random)
            ->select(['id', 'content_raw', 'content_ai', 'image', 'comment', 'reaction', 'source'])
            ->inRandomOrder()
            ->first();
        if (empty($page)) {
            return response([
                'status' => 'OK',
                'data' => null,
            ]);
        }
        $comment = $page->comment;
        if (!empty($comment) && json_validate($comment)) {
            $comment = @json_decode($comment, true)['binh_luan'] ?? [];
            $comment = implode("\n", array_slice($comment, 0, 10));
            $page->comment = $comment;
        }
        $setting = Setting::where('key', 'page_content_reaction')->first();
        $data = $setting ? json_decode($setting->value, true) : [];


        return response([
            'status' => 'OK',
            'data' => $page,
            'config' => $data
        ]);
    }

    public function updatePost($id)
    {
        $content = \App\Models\PageContent::query()
            ->where('id', $id)
            ->first();
        if (empty($content)) {
            return response([
                'status' => 'Not found'
            ]);
        }
        $content->update([
            'status' => 2,
            'link_fb' => request('link_fb', ''),
            'posted_at' => time()
        ]);

        PageContentReactionJob::dispatch($content)
            ->onQueue('default')
            ->onConnection('sync');


        return response([
            'status' => 'OK',
        ]);
    }

    public function calPrice($id = '')
    {
        $orderId = request('ids');
        $res = Http::post('https://sub247.net/api/v2', [
            'key' => 'kmuSiMpPtfiA2WbMg6Nw6DPMxI5phOxRLSujcSGae1ef497b',
            'action' => 'status',
            'order' => $orderId
        ]);
        if (!$res->ok()) {
            TelegramService::sendMessage(json_encode($res->body()));
            return response([
                'status' => false,
                'mess' => 'get order fail'
            ], 200);
        }
        $content = \App\Models\PageContent::query()
            ->where('id', $id)
            ->first();
        if (empty($content)) {
            return response([
                'status' => 'Not found'
            ]);
        }
        if (isset($res->json()['error'])) {
            return response([
                'status' => 'Not found'
            ]);
        }
        $res = $res->json();
        $price = !empty($content->price) ? $content->price : 0;
        $content->update([
            'price' => $price + $res['charge']
        ]);
        return response([
            'status' => 'OK',
        ]);
    }
}
