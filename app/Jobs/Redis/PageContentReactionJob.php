<?php

namespace App\Jobs\Redis;

use App\Enum\ReactionProviderEnum;
use App\Enum\ReactionStatusEnum;
use App\Models\PageContent;
use App\Models\Setting;
use App\Service\FacebookReactionProvider\ProviderFactory;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class PageContentReactionJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public PageContent $pageContent,
        public ?string $type = null
    ) {}

    public function handle(): void
    {
        if ($this->pageContent->status !== PageContent::STATUS_DONE) {
            return;
        }

        $this->pageContent->refresh();

        $setting = Setting::where('key', 'page_content_reaction')->first();
        $data = $setting ? json_decode($setting->value, true) : [];

        $configItems = collect($data['provider_config'] ?? []);
        $providerKey = ReactionProviderEnum::SUB247->value;

        $providerConfig = $configItems
            ->first(fn($item) => Arr::get($item, 'data.key') == $providerKey)['data'] ?? null;

        if (empty($providerConfig)) {
            return;
        }

        $service = ProviderFactory::make($providerKey);
        $postId = str_replace('https://fb.com/', '', $this->pageContent->link_fb);

        $reaction = [];
        $content = $this->pageContent;
        $likeTotal = $providerConfig['like_total'] ?? 50;
        $commentTotal = $providerConfig['comment_total'] ?? 10;
        $shareTotal = $providerConfig['share_total'] ?? 10;

        // LIKE
        if (!empty($data['enable_reaction_like']) &&
            $content->like_status === ReactionStatusEnum::PENDING->value
        ) {
            $like = $service->like($postId, $providerConfig['like_service_id'], $likeTotal);
            $reaction['like_response'] = is_array($like) ? json_encode($like) : $like;
            $reaction['like_service_id'] = $providerConfig['like_service_id'];
            $content->like_total = $likeTotal;
            $content->like_status = ReactionStatusEnum::DONE->value;
            $content->like_provider = $providerKey;
        }

        // COMMENT
        if (!empty($data['enable_reaction_cmt']) &&
            $content->comment_status === ReactionStatusEnum::PENDING->value
        ) {
            $commentRaw = $content->comment;

            if (empty($commentRaw)) {
                return;
            }

            $comments = json_validate($commentRaw)
                ? (json_decode($commentRaw, true)['binh_luan'] ?? [])
                : explode(',', $commentRaw);

            if (empty($comments)) {
                return;
            }

            $comments = array_slice($comments, 0, $commentTotal);

            $cmt = $service->comment($postId, $providerConfig['comment_service_id'], $comments, $commentTotal);
            $reaction['comment_response'] = is_array($cmt) ? json_encode($cmt) : $cmt;
            $reaction['comment_service_id'] = $providerConfig['comment_service_id'];
            $content->comment_total = $commentTotal;
            $content->comment_status = ReactionStatusEnum::DONE->value;
            $content->comment_provider = $providerKey;
        }

        // SHARE
        if (!empty($data['enable_reaction_share']) &&
            $content->share_status === ReactionStatusEnum::PENDING->value
        ) {
            $share = $service->share($postId, $providerConfig['share_service_id'], $shareTotal);
            $reaction['share_response'] = is_array($share) ? json_encode($share) : $share;
            $reaction['share_service_id'] = $providerConfig['share_service_id'];
            $content->share_total = $shareTotal;
            $content->share_status = ReactionStatusEnum::DONE->value;
            $content->share_provider = $providerKey;
        }

        $content->reaction = $reaction;
        $content->save();
    }

    public function maxAttempts(): int
    {
        return 1;
    }
}
