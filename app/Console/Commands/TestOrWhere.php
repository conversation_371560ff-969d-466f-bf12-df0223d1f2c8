<?php

namespace App\Console\Commands;

use App\Models\ES\Tvplus;
use Illuminate\Console\Command;

class TestOrWhere extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:or-where';

    /**
     * The console command description.
     */
    protected $description = 'Test the orWhere method functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Testing "orWhere" Methods ===');
        $this->newLine();

        // Test 1: Basic orWhere
        $this->info('1. Testing basic orWhere():');
        $query1 = Tvplus::query()
            ->where('object_id', '11440')
            ->orWhere('object_id', '11441')
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query1, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 2: Only OR conditions
        $this->info('2. Testing only OR conditions (should have minimum_should_match):');
        $query2 = Tvplus::query()
            ->orWhere('brand_id', '10586')
            ->orWhere('brand_id', '10587')
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query2, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 3: OR with different operators
        $this->info('3. Testing OR with different operators:');
        $query3 = Tvplus::query()
            ->where('object_id', '11440')
            ->orWhere('created_time', '>', '2024-01-01')
            ->orWhere('updated_time', '>=', '2024-01-01')
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query3, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 4: OR with IN clause
        $this->info('4. Testing orWhereIn():');
        $query4 = Tvplus::query()
            ->where('object_id', '11440')
            ->orWhereIn('brand_id', ['10586', '10587', '10588'])
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query4, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 5: Complex OR query
        $this->info('5. Testing complex OR combinations:');
        $query5 = Tvplus::query()
            ->where('object_id', '11440')
            ->where('is_delete', 0)
            ->orWhere('brand_id', '10586')
            ->orWhereIn('sub_brand_service_id', [41797, 41796])
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query5, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 6: Real query execution - Basic OR
        $this->info('6. Testing real query execution - Basic OR:');
        try {
            $results1 = Tvplus::query()
                ->where('object_id', '11440')
                ->where('brand_id', '10586')
                ->orWhere('brand_id', '10587')
                ->limit(5)
                ->get();

            $this->line("   ✓ Query executed successfully");
            $this->line("   ✓ Found {$results1->count()} records");
            $this->line("   ✓ Total hits: {$results1->total()}");
            $this->line("   ✓ Query took: {$results1->took()}ms");
        } catch (\Exception $e) {
            $this->error("   ✗ Query execution failed: " . $e->getMessage());
        }
        $this->newLine();

        // Test 7: Real query execution - OR with IN
        $this->info('7. Testing real query execution - OR with IN:');
        try {
            $results2 = Tvplus::query()
                ->where('object_id', '11440')
                ->orWhereIn('brand_id', ['10586', '10587', '10588'])
                ->limit(5)
                ->get();

            $this->line("   ✓ Query executed successfully");
            $this->line("   ✓ Found {$results2->count()} records");
            $this->line("   ✓ Total hits: {$results2->total()}");
            $this->line("   ✓ Query took: {$results2->took()}ms");
        } catch (\Exception $e) {
            $this->error("   ✗ Query execution failed: " . $e->getMessage());
        }
        $this->newLine();

        // Test 8: Performance comparison
        $this->info('8. Performance comparison - OR vs IN:');
        try {
            // Method 1: Multiple OR conditions
            $start = microtime(true);
            $results3 = Tvplus::query()
                ->where('object_id', '11440')
                ->orWhere('brand_id', '10586')
                ->orWhere('brand_id', '10587')
                ->orWhere('brand_id', '10588')
                ->count();
            $time1 = microtime(true) - $start;

            // Method 2: Single IN condition
            $start = microtime(true);
            $results4 = Tvplus::query()
                ->where('object_id', '11440')
                ->orWhereIn('brand_id', ['10586', '10587', '10588'])
                ->count();
            $time2 = microtime(true) - $start;

            $this->line("   Multiple OR: {$results3} results in " . round($time1 * 1000, 2) . "ms");
            $this->line("   Single IN: {$results4} results in " . round($time2 * 1000, 2) . "ms");
            
            if ($time1 > 0 && $time2 > 0) {
                $this->line("   IN method is " . round($time1 / $time2, 2) . "x faster");
            }
        } catch (\Exception $e) {
            $this->error("   ✗ Performance test failed: " . $e->getMessage());
        }
        $this->newLine();

        // Test 9: OR with scroll
        $this->info('9. Testing OR with scroll processing:');
        try {
            $processed = 0;
            Tvplus::query()
                ->where('object_id', '11440')
                ->orWhere('object_id', '11441')
                ->scrollChunk(100, function ($data, $page) use (&$processed) {
                    $processed += $data->count();
                    $this->line("   Page {$page}: " . $data->count() . " records");
                    
                    // Stop after 3 pages for demo
                    if ($page >= 3) return false;
                });
            
            $this->line("   ✓ Scroll processing completed");
            $this->line("   ✓ Total processed: {$processed} records");
        } catch (\Exception $e) {
            $this->error("   ✗ Scroll test failed: " . $e->getMessage());
        }
        $this->newLine();

        // Test 10: OR with when method
        $this->info('10. Testing OR with when method:');
        $includeBrandB = true;
        $includeBrandC = false;
        
        $query10 = Tvplus::query()
            ->where('object_id', '11440')
            ->where('brand_id', '10586') // Brand A
            ->when($includeBrandB, function ($query) {
                return $query->orWhere('brand_id', '10587'); // Brand B
            })
            ->when($includeBrandC, function ($query) {
                return $query->orWhere('brand_id', '10588'); // Brand C
            })
            ->toQuery();

        $this->line('   ✓ Brand B included (condition true)');
        $this->line('   ✓ Brand C excluded (condition false)');
        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query10, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        $this->info('=== "orWhere" Method Test Complete ===');
        
        return 0;
    }
}
