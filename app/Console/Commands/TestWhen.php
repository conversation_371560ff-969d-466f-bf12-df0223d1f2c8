<?php

namespace App\Console\Commands;

use App\Models\ES\Tvplus;
use Illuminate\Console\Command;

class TestWhen extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:when';

    /**
     * The console command description.
     */
    protected $description = 'Test the when method functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Testing "when" Method ===');
        $this->newLine();

        // Test 1: when with truthy value
        $this->info('1. Testing when() with truthy value:');
        $query1 = Tvplus::query()
            ->where('object_id', '11440')
            ->when(true, function ($query) {
                $this->line('   ✓ Callback executed (value is truthy)');
                return $query->where('status', 'active');
            })
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query1, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 2: when with falsy value
        $this->info('2. Testing when() with falsy value:');
        $query2 = Tvplus::query()
            ->where('object_id', '11440')
            ->when(false, function ($query) {
                $this->line('   ✗ This should not be executed');
                return $query->where('status', 'active');
            })
            ->toQuery();

        $this->line('   ✓ Callback not executed (value is falsy)');
        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query2, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 3: when with else callback
        $this->info('3. Testing when() with else callback:');
        $userRole = 'user'; // Not admin
        $query3 = Tvplus::query()
            ->where('object_id', '11440')
            ->when($userRole === 'admin', 
                function ($query) {
                    $this->line('   ✗ Admin callback (should not execute)');
                    return $query->where('access_level', 'all');
                },
                function ($query) {
                    $this->line('   ✓ Default callback executed (user is not admin)');
                    return $query->where('access_level', 'public');
                }
            )
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query3, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 4: unless method
        $this->info('4. Testing unless() method:');
        $includeDeleted = false;
        $query4 = Tvplus::query()
            ->where('object_id', '11440')
            ->unless($includeDeleted, function ($query) {
                $this->line('   ✓ Unless callback executed (includeDeleted is false)');
                return $query->where('is_delete', 0);
            })
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query4, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 5: chained when methods
        $this->info('5. Testing chained when() methods:');
        $searchTerm = 'test';
        $status = 'active';
        $category = null; // This should not add a condition

        $query5 = Tvplus::query()
            ->where('object_id', '11440')
            ->when($searchTerm, function ($query, $term) {
                $this->line("   ✓ Adding search for: {$term}");
                return $query->match('message', $term);
            })
            ->when($status, function ($query, $status) {
                $this->line("   ✓ Adding status filter: {$status}");
                return $query->where('status', $status);
            })
            ->when($category, function ($query, $category) {
                $this->line("   ✗ This should not execute (category is null)");
                return $query->where('category', $category);
            })
            ->toQuery();

        $this->line('   Generated query:');
        $this->line('   ' . json_encode($query5, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        $this->newLine();

        // Test 6: Real query execution
        $this->info('6. Testing actual query execution:');
        try {
            $results = Tvplus::query()
                ->where('object_id', '11440')
                ->when(true, function ($query) {
                    return $query->where('brand_id', '10586');
                })
                ->limit(3)
                ->get();

            $this->line("   ✓ Query executed successfully");
            $this->line("   ✓ Found {$results->count()} records");
            $this->line("   ✓ Total hits: {$results->total()}");
            $this->line("   ✓ Query took: {$results->took()}ms");
        } catch (\Exception $e) {
            $this->error("   ✗ Query execution failed: " . $e->getMessage());
        }

        $this->newLine();
        $this->info('=== "when" Method Test Complete ===');
        
        return 0;
    }
}
