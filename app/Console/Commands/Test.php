<?php

namespace App\Console\Commands;

use App\Models\ES\Paper;
use App\Models\ES\ReViewapp;
use App\Models\ES\ReviewGoogleBusiness;
use App\Models\ES\Tiktok;
use App\Models\ES\Tv;
use App\Models\ES\Tvplus;
use App\Models\ES\Website;
use App\Models\ES\Youtube;
use App\Models\ES\Zalo;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Concurrency;
use Illuminate\Support\Facades\Http;
use Lib\Elasticsearch\Model;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use QuickChart;
use Spatie\Fork\Fork;
use function PHPUnit\Framework\matches;
use function Psy\debug;
use function Laravel\Prompts\{table, spin};

use App\Models\User;
use App\Service\ApiReactionService;
use App\Service\FacebookToken;
use App\Service\FacebookToken2;
use App\Service\TelegramService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class Test extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:tesst';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public $benchmarkStartTime;
    public $benchmarkStartMemory;
    const MODELS = [
        Tvplus::class,
        Youtube::class,
        Tiktok::class,
        Zalo::class,
        Paper::class,
        Tv::class,
        Website::class,
        ReviewGoogleBusiness::class,
    ];
    const MAIN = 41777;
    const PROJECT = [
        41777 => 'Thương hiệu BIM và lãnh đạo',
        41778 => 'SKY M Hạ Long',
        41779 => 'Sora Bay HaLong',
        41780 => 'Marina Bayfront District',
        41781 => 'Grand Bay Halong Villas',
        41782 => 'Sailing Club Residences Ha Long Bay',
        41783 => 'Citadines Marina Halong',
        41784 => 'Aqua City HaLong',
        41785 => 'Horizon Bay',
        41786 => 'Hạ Long Marina',
        41787 => 'Intercontinental Ha Long Bay',

        41788 => 'Phu Quoc Marina',
        41789 => 'Phu Quoc Waterfront',
        41790 => 'Sailing Club Signature Resort Phu Quoc',
        41791 => 'Park Hyatt Phu Quoc Residences',

        41792 => 'Thanh Xuân Valley',
        41795 => 'Valley Park Residences',
        41796 => 'Valley Town',
        41793 => 'Intercontinental Thanh Xuan Valley',
        41794 => 'Spring Residences',

        41797 => 'Khu nghỉ dưỡng cao cấp Vĩnh Hy',


    ];

    const competitor = [
        [
            'brand' => 10586,
            'object' => 58591,
            'id_sub' => 41799,
            'sub_name' => 'Sun Centro Town'
        ],
        [
            'brand' => 10586,
            'object' => 58592,
            'id_sub' => 41800,
            'sub_name' => 'Sun Elite City Hạ Long'
        ],
        [
            'brand' => 10586,
            'object' => 58595,
            'id_sub' => 41803,
            'sub_name' => 'Xanh Island Cát Bà'
        ],
        [
            'brand' => 10586,
            'object' => 51068,
            'id_sub' => 41802,
            'sub_name' => 'Waterpoint Nam Long'
        ],
        [
            'brand' => 10586,
            'object' => 58593,
            'id_sub' => 41801,
            'sub_name' => 'Eco Retreat'
        ],
    ];
    public $startRow = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->startBenchmark();
        $main = 41777;
        $projects = [
            41797 => 'Khu nghỉ dưỡng cao cấp Vĩnh Hy',
            41796 => 'Valley Town',
            41795 => 'Valley Park Residences',
            41794 => 'Spring Residences',
            41793 => 'Intercontinental Thanh Xuan Valley',
            41792 => 'Thanh Xuân Valley',
            41791 => 'Park Hyatt Phu Quoc Residences',
            41790 => 'Sailing Club Signature Resort Phu Quoc',
            41789 => 'Phu Quoc Waterfront',
            41788 => 'Phu Quoc Marina',
            41787 => 'Intercontinental Ha Long Bay',
            41786 => 'Hạ Long Marina',
            41785 => 'Horizon Bay',
            41784 => 'Aqua City HaLong',
            41783 => 'Citadines Marina Halong',
            41782 => 'Sailing Club Residences Ha Long Bay',
            41781 => 'Grand Bay Halong Villas',
            41780 => 'Marina Bayfront District',
            41779 => 'Sora Bay HaLong',
            41778 => 'SKY M Hạ Long',
            41777 => 'Thương hiệu BIM và lãnh đạo',
        ];


        $now = Carbon::now(); // thời điểm hiện tại
        $startTime = $now->copy()->subDay()->setTime(15, 0)->format('Y-m-d H:i:s');
        $endTime = $now->copy()->setTime(15, 0)->format('Y-m-d H:i:s');
        $res = spin(
            callback: fn() => $this->start($projects, $startTime, $endTime),
            message: 'query ...'
        );


//        //reaction
//        $this->shareOfVoice($res, $projects);
//        $this->projectOfChannel($res, $projects);
        $listSentiment = $this->listSentmient($res, $main);
        $react = $this->reaction($res, $main);
        $shareOfVoice = $this->shareOfVoice($res, $projects);
        spin(
            callback: fn() => $this->writeToExecl($res, $listSentiment, $react, $shareOfVoice),
            message: 'export ...'
        );
        $this->endBenchmark($res);
    }

    protected function startBenchmark()
    {
        $this->benchmarkStartTime = microtime(true);
        $this->benchmarkStartMemory = memory_get_usage();
    }

    private function listSentmient($res, $main)
    {
//        Tvplus::class,
//        Youtube::class,
//        Tiktok::class,
//        Zalo::class,
//        Paper::class,
//        Tv::class,
//        Website::class,
//        ReviewGoogleBusiness::class,
        $mainFacebook = $res[0]->where('sub_brand_service_id', $main)->count();
        $mainYoutube = $res[1]->where('sub_brand_service_id', $main)->count();
        $mainTiktok = $res[2]->where('sub_brand_service_id', $main)->count();
        $mainZalo = $res[3]->where('sub_brand_service_id', $main)->count();
        $mainPaper = $res[4]->where('sub_brand_service_id', $main)->count();
        $mainTv = $res[5]->where('sub_brand_service_id', $main)->count();
        $mainOnline = $res[6]->where('sub_brand_service_id', $main)->where('web_status_4', 0)->count();
        $mainWebsite = $res[6]->where('sub_brand_service_id', $main)->where('web_status_4', 1)->count();
        $mainReviewGoogleBusiness = $res[7]->where('sub_brand_service_id', $main)->count();


        $total = array_sum([
            $mainFacebook,
            $mainYoutube,
            $mainOnline,
            $mainWebsite,
            $mainZalo,
            $mainTiktok,
            $mainReviewGoogleBusiness,
            $mainPaper,
            $mainTv,
        ]);
        $rows = [
            [
                'Facebook', $mainFacebook, count($res[0]) - $mainFacebook
            ],
            [
                'Youtube', $mainYoutube, count($res[1]) - $mainYoutube
            ],

            [
                'Online News', $mainOnline, count($res[6]) - $mainOnline
            ],
            [
                'Blog/Forum/Website', $mainWebsite, count($res[6]) - $mainWebsite
            ],
            [
                'Zalo', $mainZalo, count($res[3]) - $mainZalo
            ],
            [
                'Tiktok', $mainTiktok, count($res[2]) - $mainTiktok
            ],
            [
                'Review Map', $mainReviewGoogleBusiness, count($res[7]) - $mainReviewGoogleBusiness
            ],
            [
                'Paper', $mainPaper, count($res[4]) - $mainPaper
            ],
            [
                'TV', $mainTv, count($res[5]) - $mainTv
            ],
            [
                "Tổng", $total, array_sum(array_map('count', $res)) - $total
            ]
        ];
        $headers = ['Lượng tin', 'Thương hiệu và lãnh đạo', 'Các dự án'];
        table(
            headers: $headers,
            rows: $rows
        );
        return [
            'headers' => $headers,
            'rows' => $rows
        ];
    }

    protected function shareOfVoice($res, $projects)
    {
        $rows = [];
        foreach ($projects as $k => $project) {
            $total = 0;
            foreach ($res as $item) {
                $total += $item->where('sub_brand_service_id', $k)->count();
            }
            $rows[] = [$project, $total];
        }
        table(
            headers: ["Dự án", 'Số lượng'],
            rows: $rows
        );
        return $rows;
    }

    protected function reaction($res, $main)
    {
        $positive = 0;
        $neutral = 0;
        $negative = 0;
        $positiveMain = 0;
        $neutralMain = 0;
        $negativeMain = 0;
        /** @var Collection $item */
        foreach ($res as $item) {
            $positive += $item->where('state', 1)->count();
            $positiveMain += $item->where('state', 1)->where('sub_brand_service_id', $main)->count();
            $neutral += $item->where('state', 2)->count();
            $neutralMain += $item->where('state', 2)->where('sub_brand_service_id', $main)->count();
            $negative += $item->where('state', 0)->count();
            $negativeMain += $item->where('state', 0)->where('sub_brand_service_id', $main)->count();
        }

        $reactionFinaly = ($positive + $neutral) > 0
            ? ($positive - $neutral) / ($positive + $neutral)
            : 0;

        $reactionMainFinaly = ($positiveMain + $neutralMain) > 0
            ? ($positiveMain - $neutralMain) / ($positiveMain + $neutralMain)
            : 0;

        $headers = ["Chỉ số cảm xúc", 'Thương hiệu và lãnh đạo', 'Các dự án'];
        $rows = [
            [
                "Tích cực", $positiveMain, ($positive - $positiveMain),
            ],
            [
                "Tiêu cực", $neutralMain, ($neutral - $neutralMain),
            ],
            [
                "Trung tính", $negativeMain, ($negative - $negativeMain),
            ],
            [
                "Chỉ số cảm xúc", $reactionMainFinaly, $reactionFinaly
            ]
        ];
        table(
            headers: $headers,
            rows: $rows,

        );

        return [
            'headers' => $headers,
            'rows' => $rows
        ];
    }

    protected function projectOfChannel($res, $projects)
    {
        $rows = [];
        foreach ($projects as $k => $name) {
            $rows[] = [
                $name,
                $res[0]->where('sub_brand_service_id', $k)->count(),
                $res[1]->where('sub_brand_service_id', $k)->count(),
                $res[2]->where('sub_brand_service_id', $k)->count(),
                $res[3]->where('sub_brand_service_id', $k)->count(),
                $res[4]->where('sub_brand_service_id', $k)->count(),
                $res[5]->where('sub_brand_service_id', $k)->count(),
                $res[6]->where('sub_brand_service_id', $k)->count(),
                $res[7]->where('sub_brand_service_id', $k)->count(),
                array_sum(array_map(fn($item) => $item->where('sub_brand_service_id', $k)->count(), $res))
            ];
        }
        $clone = collect($rows);
        $rows[] = [
            'Tổng',
            $clone->map(fn($item) => $item[1])->sum(),
            $clone->map(fn($item) => $item[2])->sum(),
            $clone->map(fn($item) => $item[3])->sum(),
            $clone->map(fn($item) => $item[4])->sum(),
            $clone->map(fn($item) => $item[5])->sum(),
            $clone->map(fn($item) => $item[6])->sum(),
            $clone->map(fn($item) => $item[7])->sum(),
            $clone->map(fn($item) => $item[8])->sum(),
            $clone->map(fn($item) => $item[9])->sum(),
        ];

        table(
            headers: ['Đối tượng', 'Facebook', 'Youtube', 'Ifollow', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Tổng'],
            rows: $rows
        );
    }

    protected function newsOfPlatform($res)
    {

    }

    protected function start($projects, $start = null, $end = null)
    {

        /** @var Model $model */
        $makeQuery = function ($model) use ($projects, $start, $end) {
            return fn() => $model::query()
                ->where('object_id', '11440')
                ->where('brand_id', '10586')
                ->whereIn('sub_brand_service_id', array_keys($projects))
                ->when($model == Website::class, fn($query) => $query->where('web_sub_parent_service_id', 1))
                ->when($model == Paper::class, fn($query) => $query->where('paper_sub_parent_service_id', 1))
                ->when($model == Tv::class, fn($query) => $query->where('tv_sub_parent_service_id', 1))
                ->when(!in_array($model,[Website::class, Paper::class, Tv::class]),
                    fn($query) => $query->where('sub_parent_service_id', 1))
                ->when($start, fn($query) => $query->where('content_created', '>=', $start))
                ->when($end, fn($query) => $query->where('content_created', '<=', $end))
                ->all(1500);
        };
        $tasks = array_map($makeQuery, self::MODELS);
        return Concurrency::run($tasks);
    }

    protected function start2($start = null, $end = null)
    {
        $makeQuery = function ($model) use ($start, $end) {
            return fn() => $model::query()
                ->whereIn('object_id', [58593, 51068])
                ->whereIn('brand_id', [10586])
                ->whereIn('sub_brand_service_id', [41801, 41802])
                ->when($start, fn($query) => $query->where('content_created', '>=', $start))
                ->when($end, fn($query) => $query->where('content_created', '<=', $end))
                ->all(1500);
        };
        $tasks = array_map($makeQuery, self::MODELS);
        return Concurrency::run($tasks);
    }
    protected function start3($start = null, $end = null)
    {
        $makeQuery = function ($model) use ($start, $end) {
            return fn() => $model::query()
                ->whereIn('object_id', [58591, 58592,58595])
                ->whereIn('brand_id', [10586])
                ->whereIn('sub_brand_service_id', [41799, 41800,41803])
                ->when($start, fn($query) => $query->where('content_created', '>=', $start))
                ->when($end, fn($query) => $query->where('content_created', '<=', $end))
                ->all(1500);
        };
        $tasks = array_map($makeQuery, self::MODELS);
        return Concurrency::run($tasks);
    }
    protected function getDataCompe($competitor)
    {

    }


    protected function writeToExecl($dataQuery, array $listSentiment, array $reation, array $shareOfVoice)
    {
        $filePath = storage_path('app/Form_2.xlsx');
        $spreadsheet = IOFactory::load($filePath);
        $sheet = $spreadsheet->getActiveSheet();
        $spreadsheet->getDefaultStyle()
            ->getFont()
            ->setName('Arial')
            ->setSize(14)
            ->setBold(true);
        $sheet->getStyle('A3')->applyFromArray([
            'font' => [
                'color' => [
                    'rgb' => '492CA0', // không cần dấu #
                ],
            ],
        ]);
        $spreadsheet->setActiveSheetIndex(0);
        $sheet->setTitle('Báo cáo ngày');
        $sheet->setCellValue('F5', strtoupper("Test") . ' REPORT');
        $sheet->setCellValue('F6', 'Ngày: ' . date('d/m/Y'));
        $sheet->setCellValue('C9', "Cannv");
        $sheet->setCellValue('C10', 'Ngày: ' . date('d/m/Y'));
        $sheet->setCellValue('C11', 'Báo cáo ' . " Test");

        [$styleBlueColor, $styleBoldText] = $this->style();
        $this->startRow = 15;
        $i = 15;
        $col = 1;
        $cell = Coordinate::stringFromColumnIndex($col + 1) . $this->startRow;
        $sheet->setCellValue($cell, 'I. THÔNG TIN CỤ THỂ VỀ ' . strtoupper('BIM GROUP'));
        $sheet->getStyle($cell)
            ->applyFromArray($styleBlueColor)
            ->applyFromArray($styleBoldText);
        spin(
            callback: fn() => $this->writeSentiment($sheet, $listSentiment),
            message: 'vẽ table 1 ...'
        );
        spin(
            callback: fn() => $this->writeReaction($sheet, $reation),
            message: 'vẽ reaction ...'
        );
        spin(
            callback: fn() => $this->writeShareOfVoice($sheet, $shareOfVoice),
            message: 'vẽ share of voice ...'
        );
        spin(
            callback: fn() => $this->writenewsOnPlatform($sheet, $dataQuery),
            message: 'vẽ share of voice ...'
        );
        spin(
            callback: fn() => $this->writeHourlyTrend($sheet, $dataQuery),
            message: 'vẽ share of voice ...'
        );
        spin(
            callback: fn() => $this->emotionalRatio($sheet, $dataQuery),
            message: 'vẽ ratio ...'
        );
        spin(
            callback: fn() => $this->writeTableProject($sheet, $dataQuery),
            message: 'vẽ table project ...'
        );
        spin(
            callback: fn() => $this->writeTableEmotional($sheet, $dataQuery),
            message: 'vẽ table reaction ...'
        );
        spin(
            callback: fn() => $this->negativeNews($sheet, $dataQuery),
            message: 'vẽ table nguồn tin tiêu cực ...'
        );
        spin(
            callback: fn() => $this->topNewsSource($sheet, $dataQuery),
            message: 'vẽ table top nguồn tin  ...'
        );
        spin(
            callback: fn() => $this->topNewsReaction($sheet, $dataQuery),
            message: 'vẽ table top nguồn tin tương tác  ...'
        );
        spin(
            callback: fn() => $this->general($sheet, $dataQuery),
            message: 'vẽ table top nguồn tin tương tác  ...'
        );
        spin(
            callback: fn() => $this->general2($sheet, $dataQuery),
            message: 'vẽ table top nguồn tin tương tác  ...'
        );
        spin(
            callback: fn() => $this->general3($sheet, $dataQuery),
            message: 'vẽ table top nguồn tin tương tác  ...'
        );
        spin(
            callback: fn() => $this->general4($sheet, $dataQuery),
            message: 'vẽ table top nguồn tin tương tác  ...'
        );
        spin(
            callback: fn() => $this->general5($sheet, $dataQuery),
            message: 'vẽ table top nguồn tin tương tác  ...'
        );

        spin(
            callback: fn() => $this->writeData($spreadsheet, $dataQuery),
            message: 'vẽ table top nguồn tin tương tác  ...'
        );

        $newFile = storage_path('app/myfile_new.xlsx');
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save($newFile);
    }
    protected static function getPlat($class)
    {
        $r = [
            Tvplus::class => 'Facebook',
            Youtube::class => 'Youtube',
            Zalo::class => 'Zalo',
            Tiktok::class => 'Tikok',
            Tv::class => 'Tv',
            ReviewGoogleBusiness::class => 'Review Map',
        ];
        return $r[$class];
    }
    protected function writeData($spreadsheet, $dataQuery)
    {
        $data = collect($dataQuery)->flatten(1);
        $headers = [
            "Thương hiệu", "Phương tiện", "Nguồn phát hành", "Nội dung", "Loại tin",
            "Ngày phát hành", "Like", "Share", "Comment"
        ];
        // Tạo các sheet
        $sheetBIM = new Worksheet($spreadsheet, "Thương hiệu BIM và Lãnh Đạo");
        $sheetHL = new Worksheet($spreadsheet, "Khu vực Hạ Long");
        $sheetPQ = new Worksheet($spreadsheet, "Khu vực Phú Quốc");
        $sheetVP = new Worksheet($spreadsheet, "Khu vực Vĩnh Phúc");
        $sheetOther = new Worksheet($spreadsheet, "Khu vực Khác");

        // Thêm sheet vào file
        $spreadsheet->addSheet($sheetBIM);
        $spreadsheet->addSheet($sheetHL);
        $spreadsheet->addSheet($sheetPQ);
        $spreadsheet->addSheet($sheetVP);
        $spreadsheet->addSheet($sheetOther);

        // ID theo vùng
        $idSheet1 = [self::MAIN => 'Thương hiệu BIM và Lãnh Đạo'];
        $idHL = [
            41786 => 'Hạ Long Marina',
            41785 => 'Horizon Bay',
            41787 => 'Intercontinental Ha Long Bay',
            41784 => 'Aqua City HaLong',
            41783 => 'Citadines Marina Halong',
            41782 => 'Sailing Club Residences Ha Long Bay',
            41779 => 'Sora Bay HaLong',
            41778 => 'SKY M Hạ Long',
        ];
        $idPQ = [
            41791 => 'Park Hyatt Phu Quoc Residences',
            41790 => 'Sailing Club Signature Resort Phu Quoc',
            41789 => 'Phu Quoc Waterfront',
            41788 => 'Phu Quoc Marina',
        ];
        $idVP = [
            41792 => 'Thanh Xuân Valley',
            41796 => 'Valley Town',
            41795 => 'Valley Park Residences',
            41794 => 'Spring Residences',
            41793 => 'Intercontinental Thanh Xuan Valley',
        ];
        $idOther = [
            41801 => 'Eco Retreat',
            41802 => 'Waterpoint Nam Long',

        ];
        $now = Carbon::now(); // thời điểm hiện tại
        $startTime = $now->copy()->subDay()->setTime(15, 0)->format('Y-m-d H:i:s');
        $endTime = $now->copy()->setTime(15, 0)->format('Y-m-d H:i:s');
        $other = $this->start2($startTime, $endTime);

        $other = collect($other)->flatten(1);

        // Filter data theo vùng
        $sheetBIMd = $data->whereIn('sub_brand_service_id', array_keys($idSheet1));
        $sheetHLd = $data->whereIn('sub_brand_service_id', array_keys($idHL));
        $sheetPQd = $data->whereIn('sub_brand_service_id', array_keys($idPQ));
        $sheetVPd = $data->whereIn('sub_brand_service_id', array_keys($idVP));

        // Ghi header
        $sheetBIM->fromArray([$headers], null, 'A1');
        $sheetHL->fromArray([$headers], null, 'A1');
        $sheetPQ->fromArray([$headers], null, 'A1');
        $sheetVP->fromArray([$headers], null, 'A1');
        $sheetOther->fromArray([$headers], null, 'A1');

        // Hàm ghi data + auto format column width
        $writeSheet = function (Worksheet $sheet, $filteredData, $projectList, $startRow = 2) use ($headers) {
            foreach ($filteredData as $row) {
                $brandName = $projectList[$row->sub_brand_service_id] ?? 'Không rõ';
                $sheet->fromArray([
                    $brandName,
                    self::getPlat(get_class($row)),
                    $row->page_name ?? '',
                    $row->message ?? '',
                    self::formatState($row->state) ?? '',
                    $row->content_created ?? '',
                    $row->total_like ?? 0,
                    $row->total_share ?? 0,
                    $row->child_count ?? 0,
                ], null, "A{$startRow}");
                $message = $row->message ?? '';

                $link = "";
                if (get_class($row) === Tvplus::class && !empty($row->fb_id)) {
                    $link = "https://www.fb.com/" . $row->fb_id;
                }
                if (get_class($row) === Website::class) {
                    $link = $row->web_url;
                }
                if (get_class($row) === Tiktok::class) {
                    $link = "https://www.tiktok.com/@{$row->content_from_id}/video/{$row->fb_id}";
                }
                if (get_class($row) == Paper::class){
                    $link = "http://social.monitaz.com/detail/index/{$row->es_id}?_channel=2";
                }
                if (get_class($row) == Tv::class){
                    $link = "https://data.monitaz.asia/detail/index/{$row->es_id}?_channel=3";
                }

                $sheet->setCellValue("D{$startRow}", $message);
                if ($link) {
                    $sheet->getCell("D{$startRow}")->getHyperlink()->setUrl($link);
                    $sheet->getStyle("D{$startRow}")->getFont()->getColor()->setARGB(
                        \PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLUE);
                    $sheet->getStyle("D{$startRow}")->getFont()->setUnderline(true);
                }
                $startRow++;
            }

            // Auto-size all columns (A -> I)
            foreach (range('A', chr(ord('A') + count($headers) - 1)) as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        };

        // Ghi dữ liệu vào từng sheet
        $writeSheet($sheetBIM, $sheetBIMd, $idSheet1);
        $writeSheet($sheetHL, $sheetHLd, $idHL);
        $writeSheet($sheetPQ, $sheetPQd, $idPQ);
        $writeSheet($sheetVP, $sheetVPd, $idVP);
        $writeSheet($sheetOther, $other, $idOther);

        // Set sheet đầu tiên active
        $spreadsheet->setActiveSheetIndex(0);
    }


    protected function getByBrand(array $ids)
    {

    }

    protected function writeSentiment($sheet, $listSentiment)
    {
        $rows = $listSentiment['rows'];
        $headers = $listSentiment['headers'];
        $this->startRow = $this->startRow + 2;
        $startRow = $this->startRow;
        $startCol = 2; // B

// 1. Ghi headers
        $colIndex = $startCol;
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

// 2. Ghi rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

// Xác định toàn bộ range của table
        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

// 3. Style cho toàn bộ table: border + text align center
        $sheet->getStyle($tableRange)->applyFromArray([
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

// 4. In đậm header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
        ]);

// 5. Auto size cột + rộng thêm 2 đơn vị
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
//        }
//
//// 6. Tăng width thêm chút sau khi autosize
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $currentWidth = $sheet->getColumnDimension($colLetter)->getWidth();
//            $sheet->getColumnDimension($colLetter)->setWidth($currentWidth + 2); // rộng hơn chút
//        }
        $lastRow = $currentRow - 1; // dòng cuối
        $firstTotalCell = Coordinate::stringFromColumnIndex($startCol) . $lastRow;
        $lastTotalCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $totalRange = $firstTotalCell . ':' . $lastTotalCell;

        $sheet->getStyle($totalRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9D9D9'],
            ],
        ]);
        $this->startRow = $lastRow;
    }

    protected function writeReaction($sheet, $listReaction)
    {
        $headers = $listReaction['headers'];
        $rows = $listReaction['rows'];

        $startCol = 6; // G
        $this->startRow = $this->startRow + 1;
        $startRow = $this->startRow;
        // 1. Ghi headers
        $colIndex = $startCol;
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // 2. Ghi rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

        // Xác định range table
        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

        // 3. Style toàn table: căn giữa + border
        $sheet->getStyle($tableRange)->applyFromArray([
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // 4. In đậm header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
        ]);

        // 5. Auto size + rộng hơn một chút
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
//        }
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $currentWidth = $sheet->getColumnDimension($colLetter)->getWidth();
//            $sheet->getColumnDimension($colLetter)->setWidth($currentWidth + 2);
//        }

        // 6. Highlight dòng cuối (Tổng)
        $firstTotalCell = Coordinate::stringFromColumnIndex($startCol) . $lastRow;
        $lastTotalCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $totalRange = $firstTotalCell . ':' . $lastTotalCell;
        $sheet->getStyle($totalRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9D9D9'],
            ],
        ]);
        $this->startRow = $lastRow;
    }

    protected function writeShareOfVoice($sheet, $shareOfVoice, $row = 31)
    {
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');

        $labels = array_column($shareOfVoice, 0);
        $data = array_column($shareOfVoice, 1);

        $pieConfig = [
            'type' => 'pie',
            'data' => [
                'datasets' => [[
                    'data' => $data,
                    'backgroundColor' => [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                        '#FF9F40', '#E7E9ED', '#FF6384', '#36A2EB', '#FFCE56',
                        '#4BC0C0', '#9966FF', '#FF9F40', '#E7E9ED', '#FF6384',
                        '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
                        '#E7E9ED'
                    ],
                ]],
                'labels' => $labels,
            ],
            'options' => [
                'borderWidth' => 2,
                'fontColor' => '#FFFFFF',
                'fontStyle' => 'bold',
                'responsive' => true,
                'legend' => [
                    'position' => 'left',
                    'display' => true,
                    'labels' => [
                        'fontFamily' => 'Cambria',
                        'fontStyle' => 'bold',
                    ],
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#FFFFFF',
                        'align' => 'end',
                        'font' => [
                            'size' => 12,
                            'weight' => 'bold',
                            'family' => 'Cambria',
                        ],
                        'formatter' => "function(value) {
                    return (value ? value + '%' : '')
                }",
                    ],
                ],
                'title' => [
                    'display' => true,
                    'align' => 'right',
                    'text' => 'Share of Voice',
                    'x' => 500,
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                    'fontFamily' => 'Cambria',
                ],
            ],
        ];
        $qc->setConfig(json_encode($pieConfig));
        $pathToPieImg = storage_path('app/pie_chart11.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Share of Voice');
        $drawing->setDescription('Share of Voice');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $this->startRow + 3); // vị trí bắt đầu (ví dụ A20)
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);
        $this->startRow = $this->startRow + 3;
    }

    protected function writenewsOnPlatform($sheet, $data)
    {
        $collect = collect($this->formatPlatform($data));



        $platforms = $collect->keys()->toArray(); // ['facebook', 'youtube', ...]
        $mainData = [];
        $otherData = [];
        unset($platforms['ifollow']);
        $platforms[] = 'Online';
        $platforms[] = 'Blog/Forum/Website';
        foreach ($collect as $platform => $items) {
            $mainData[] = $items->where('sub_brand_service_id', self::MAIN)->count();
            $otherData[] = $items->where('sub_brand_service_id', '!=', self::MAIN)->count();
        }
        $mainData[] = 0;
        $mainData[] = 0;
        $otherData[] = 0;
        $otherData[] = 0;
        $config = [
            'type' => 'bar',
            'data' => [
                'labels' => $platforms,
                'datasets' => [
                    [
                        'label' => 'Thương hiệu và lãnh đạo',
                        'data' => $mainData,
                        'fill' => true,
                    ],
                    [
                        'label' => 'Các ngành còn lại',
                        'data' => $otherData,
                        'fill' => true,
                    ],
                ],
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'position' => 'bottom',
                    'display' => true
                ],
                'plugins' => [
                    'labels' => [
                        'fontSize' => 10
                    ],
                    'datalabels' => [
                        'anchor' => 'end',
                        'align' => 'top',
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Lượng Tin Trên Các Nền Tảng',
                    'fontSize' => 14,
                    'padding' => 16,
                    'fontColor' => '#000000',
                ]
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $row = $this->startRow + 20;
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($config));
        $pathToPieImg = storage_path('app/line_chart22.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Lượng Tin Trên Các Nền Tảng');
        $drawing->setDescription('Lượng Tin Trên Các Nền Tảng');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $row); // vị trí bắt đầu (ví dụ A20)

        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);
        $this->startRow = $row;
    }

    protected function writeHourlyTrend($sheet, $queryData)
    {
        $flat = collect($queryData)->flatten(1);
        $groupedByHour = $flat->groupBy(function ($item) {
            return $item->content_created->format('Y-m-d H:00');
        });
        $hours = $groupedByHour->keys()->sort()->values();
        $datasets = collect(self::PROJECT)->map(function ($projectName, $projectId) use ($hours, $groupedByHour) {
            $data = $hours->map(function ($hour) use ($groupedByHour, $projectId) {
                $group = $groupedByHour[$hour] ?? collect();
                return collect($group)->where('sub_brand_service_id', $projectId)->count();
            })->toArray();

            return [
                'label' => $projectName,
                'data' => $data,
                'fill' => false,
                'borderWidth' => 2,
                'lineTension' => 0.4,
                'cubicInterpolationMode' => 'monotone',
            ];
        })->values()->toArray();
        $chartLineConfig = [
            'type' => 'line',
            'data' => [
                'labels' => range(1, 24), // 0 -> 23
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [
                        ['display' => true],
                    ],
                    'yAxes' => [
                        ['display' => true],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Xu Hướng Tin Bài Theo Giờ',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $row = $this->startRow + 20;
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartLineConfig));
        $pathToPieImg = storage_path('app/line_1_chart33.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setDescription('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $row);

        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);
        $this->startRow = $row;
    }

    protected function emotionalRatio($sheet, $queryData)
    {
        $flat = collect($queryData)->flatten(1);
        $counts = [];
        foreach (self::PROJECT as $projectId => $projectName) {
            $projectItems = $flat->where('sub_brand_service_id', $projectId);
            $total = $projectItems->count();

            if ($total === 0) continue; // bỏ qua project không có bài

            $positive = $projectItems->where('state', 1)->count();
            $neutral = $projectItems->where('state', 0)->count();
            $negative = $projectItems->where('state', 2)->count();

            // Tính % cảm xúc
            $counts[] = [
                'project' => $projectName,
                'positive' => round($positive / $total * 100, 2),
                'neutral' => round($neutral / $total * 100, 2),
                'negative' => round($negative / $total * 100, 2),
            ];
        }
        $labels = array_column($counts, 'project');
        $positive = array_column($counts, 'positive');
        $neutral = array_column($counts, 'neutral');
        $negative = array_column($counts, 'negative');

        $datasets = [
            [
                'label' => 'Positive',
                'backgroundColor' => '#4CAF50',
                'data' => $positive,
            ],
            [
                'label' => 'Neutral',
                'backgroundColor' => '#FFC107',
                'data' => $neutral,
            ],
            [
                'label' => 'Negative',
                'backgroundColor' => '#F44336',
                'data' => $negative,
            ],
        ];

        $chartStackedBarConfig = [
            'type' => 'horizontalBar',
            'data' => [
                'labels' => $labels,  // mảng PHP
                'datasets' => $datasets,  // mảng PHP
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [[
                        'stacked' => true,
                        'ticks' => [
                            'min' => 0,
                            'max' => 100,
                        ],
                        'scaleLabel' => [
                            'display' => true,
                            'labelString' => '%', // để chú thích là %
                        ]
                    ]],
                    'yAxes' => [[
                        'stacked' => true,
                    ]],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Tỷ lệ cảm xúc',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                        'formatter' => '%', // QuickChart cho phép hiển thị ký tự
                    ],
                ],
            ],
        ];


        // Xuất ra ảnh và chèn vào Excel
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $row = $this->startRow + 20;

        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartStackedBarConfig));
        $imgPath = storage_path('app/emotional_ratio44.png');
        $qc->toFile($imgPath);
        $drawing = new Drawing();
        $drawing->setName('Tỷ lệ cảm xúc');
        $drawing->setDescription('Tỷ lệ cảm xúc');
        $drawing->setPath($imgPath);
        $drawing->setCoordinates('C' . $row);
//        $drawing->setOffsetX(50);
//        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);

        $this->startRow = $row;
    }

    protected function writeTableProject($sheet, $queryData)
    {
        $data = collect($this->formatPlatform($queryData));
//        [
//            'Online News', $mainOnline, count($res[2]) - $mainOnline
//        ],
//            [
//                'Blog/Forum/Website', $mainWebsite, count($res[2]) - $mainWebsite
//            ],
        $headers = [
            'Đối tượng',
            'Facebook',
            'Youtube',
            'Online News',
            'Blog/Forum/Website',
            'Zalo',
            'Tiktok',
            'Review Map',
            'Paper',
            'TV',
            'Tổng'
        ];

// Chia ifollow thành 2 collection riêng theo web_status_4
        $ifollowOnline = $data['ifollow']->filter(fn($item) => $item->web_status_4 == 0);
        $ifollowForum = $data['ifollow']->filter(fn($item) => $item->web_status_4 == 1);

// Build lại data để đồng nhất key -> collection
        $platforms = collect([
            'facebook' => $data['facebook'],
            'youtube' => $data['youtube'],
            'online' => $ifollowOnline,
            'forum' => $ifollowForum,
            'zalo' => $data['zalo'],
            'tiktok' => $data['tiktok'],
            'review' => $data['review_google'],
            'paper' => $data['paper'],
            'tv' => $data['tv'],
        ]);

        $rows = [];

        foreach (self::PROJECT as $projectId => $projectName) {
            $counts = $platforms->map(function ($collection) use ($projectId) {
                return $collection->where('sub_brand_service_id', $projectId)->count();
            });

            $row = [
                $projectName,
                $counts['facebook'] ?? 0,
                $counts['youtube'] ?? 0,
                $counts['online'] ?? 0,
                $counts['forum'] ?? 0,
                $counts['zalo'] ?? 0,
                $counts['tiktok'] ?? 0,
                $counts['review'] ?? 0,
                $counts['paper'] ?? 0,
                $counts['tv'] ?? 0,
                $counts->sum()
            ];

            $rows[] = $row;
        }

        // Tính tổng
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }

        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);

        // Thêm hàng "Tỉ lệ %"
        $grandTotal = end($totals);
        $ratios = array_map(function ($value) use ($grandTotal) {
            return $grandTotal > 0 ? round($value / $grandTotal * 100, 2) . '%' : '0%';
        }, $totals);
        $rows[] = array_merge(['Tỉ lệ %'], $ratios);

        // === Vẽ bảng vào Excel ===
        $startRow = $this->startRow + 20;;
        $startCol = 2;
        $colIndex = $startCol;

        // Header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Data rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

        // Style chung cho bảng
        $sheet->getStyle($tableRange)->applyFromArray([
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Style header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'] // xám nhẹ cho header
            ]
        ]);

//        // Auto width
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
//            $currentWidth = $sheet->getColumnDimension($colLetter)->getWidth();
//            $sheet->getColumnDimension($colLetter)->setWidth($currentWidth + 2);
//        }
        // === Highlight 2 dòng cuối ===
        $highlightRows = [$lastRow - 1, $lastRow]; // dòng "Tổng" và "Tỉ lệ %"
        foreach ($highlightRows as $rowNum) {
            $firstCellRow = Coordinate::stringFromColumnIndex($startCol) . $rowNum;
            $lastCellRow = Coordinate::stringFromColumnIndex($startCol + $columnCount - 1) . $rowNum;
            $sheet->getStyle($firstCellRow . ':' . $lastCellRow)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'color' => ['rgb' => 'FFFFCC'] // vàng nhạt highlight
                ]
            ]);
        }
        // === Màu chữ xanh cho cột cuối ===
        // === Màu chữ xanh cho dòng "Tỉ lệ %" ===
        $lastRowLetterStart = Coordinate::stringFromColumnIndex($startCol);
        $lastRowLetterEnd = Coordinate::stringFromColumnIndex($startCol + $columnCount - 1);
        $sheet->getStyle($lastRowLetterStart . $lastRow . ':' . $lastRowLetterEnd . $lastRow)
            ->getFont()->getColor()->setRGB('0070C0');

        $this->startRow = $lastRow;
    }

    protected function writeTableEmotional($sheet, $queryData)
    {
        $data = collect($this->formatPlatform($queryData));

        $headers = ['Sentiment', 'Positive', 'Negative', 'Neutral', 'Chỉ số cảm xúc'];
        $rows = [];
        foreach (self::PROJECT as $projectId => $projectName) {
            $positive = $data->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 1)
                    ->count();
            });
            $negative = $data->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 2)
                    ->count();
            });
            $neutral = $data->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 0)
                    ->count();
            });
            $reactionFinaly = ($positive + $negative) > 0
                ? max(($positive - $negative) / ($positive + $negative), 0)
                : 0;

            $row = [
                $projectName,
                $positive,
                $negative,
                $neutral,
                $reactionFinaly
            ];
            $rows[] = $row;
        }
        // Tính tổng
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }

        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);
        $startRow = $this->startRow + 5;
        $startCol = 2;
        $colIndex = $startCol;

        // Header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Data rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

        // Style chung cho bảng
        $sheet->getStyle($tableRange)->applyFromArray([
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Style header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'] // xám nhẹ cho header
            ]
        ]);

        // Auto width
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
//            $currentWidth = $sheet->getColumnDimension($colLetter)->getWidth();
//            $sheet->getColumnDimension($colLetter)->setWidth($currentWidth + 2);
//        }

        $firstTotalCell = Coordinate::stringFromColumnIndex($startCol) . $lastRow;
        $lastTotalCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $totalRange = $firstTotalCell . ':' . $lastTotalCell;
        $sheet->getStyle($totalRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9D9D9'],
            ],
        ]);
        $this->startRow = $lastRow;
    }

    protected function negativeNews($sheet, $queryData)
    {
        $headers = [
            'Đối tượng', 'Nền tảng', 'Top nguồn', 'Nội dung',
            'Time', 'Sentiment', 'Like', 'Share/View', 'Comment', 'Tổng tương tác'
        ];

        $r = $this->startRow + 5;
        // 🔹 Ghi tiêu đề đỏ ở B152
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, 'NGUỒN TIN TIÊU CỰC ĐÁNG LƯU Ý');
        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FF0000'], // đỏ
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
        $startRow = $r + 2;
        $startCol = 2; // Cột B
        $colIndex = $startCol;

        foreach ($headers as $header) {
            $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Style header bảng
        $firstHeaderCell = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeaderCell = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeaderCell . ':' . $lastHeaderCell;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);
        $this->startRow = $startRow;
    }

    protected function topNewsSource($sheet, $queryData)
    {
        $allPlatforms = [
            'facebook' => 'Facebook',
            'ifollow' => 'Ifollow',
            'youtube' => 'Youtube',
            'tiktok' => 'Tiktok',
            'zalo' => 'Zalo',
            'review_google' => 'Review Map',
            'paper' => 'Paper',
            'tv' => 'Tv',
        ];

        $data = $this->formatPlatform($queryData);

        // Lọc ra các platform có dữ liệu (ít nhất 1 project có data)
        $availablePlatforms = [];
        foreach ($allPlatforms as $key => $label) {
            if (isset($data[$key]) && $data[$key]->isNotEmpty()) {
                // Kiểm tra xem có ít nhất 1 giá trị không rỗng trong tất cả các projects
                $hasData = false;
                foreach (self::PROJECT as $projectId => $projectName) {
                    if ($data[$key]->where('sub_brand_service_id', $projectId)->isNotEmpty()) {
                        $hasData = true;
                        break;
                    }
                }
                if ($hasData) {
                    $availablePlatforms[$key] = $label;
                }
            }
        }

        // Tạo headers động, gồm 'Đối tượng' và từng platform có data, mỗi platform 2 cột (tên + số lượng)
        $headers = ['Đối tượng'];
        foreach ($availablePlatforms as $label) {
            $headers[] = $label;
            $headers[] = 'Số lượng';
        }

        // Tạo $rows: mỗi row cho 1 project, chứa các collection top10ByPage của từng platform có data
        $rows = [];
        foreach (self::PROJECT as $projectId => $projectName) {
            $row = ['object' => $projectName];
            foreach ($availablePlatforms as $key => $label) {
                $row[$key] = $this->top10ByPage($data[$key], $projectId);
            }
            $rows[] = $row;
        }

        // Viết tiêu đề
        $r = $this->startRow + 2;
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, 'TOP NGUỒN TIN VỀ BIM GROUP.');
        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => ['bold' => true, 'size' => 12],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        $startRow = $r + 2;
        $startCol = 2; // cột B
        $colIndex = $startCol;

        // Viết header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Style header
        $firstHeaderCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeaderCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeaderCell . ':' . $lastHeaderCell;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'DDDDDD']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ],
        ]);

        // Render rows
        $currentRow = $startRow + 1;

        foreach ($rows as $row) {
            // Tính max rows của 1 dòng (dựa vào max count trong các platform hiện có)
            $maxRows = 1;
            foreach ($availablePlatforms as $key => $label) {
                $count = $row[$key]->count();
                if ($count > $maxRows) $maxRows = $count;
            }

            // Viết tên đối tượng và merge nếu cần
            $sheet->setCellValue('B' . $currentRow, $row['object']);
            if ($maxRows > 1) {
                $sheet->mergeCells('B' . $currentRow . ':B' . ($currentRow + $maxRows - 1));
            }

            for ($i = 0; $i < $maxRows; $i++) {
                $col = 3; // bắt đầu từ cột C
                foreach ($availablePlatforms as $key => $label) {
                    $platformData = $row[$key];
                    $sheet->setCellValue(Coordinate::stringFromColumnIndex($col) . ($currentRow + $i), $platformData->keys()[$i] ?? '');
                    $sheet->setCellValue(Coordinate::stringFromColumnIndex($col + 1) . ($currentRow + $i), $platformData->values()[$i] ?? '');
                    $col += 2;
                }
            }

            $currentRow += $maxRows;
        }

        // Style toàn bảng
        $lastColLetter = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1);
        $lastRow = $currentRow - 1;
        $tableRange = 'B' . $startRow . ':' . $lastColLetter . $lastRow;

        $sheet->getStyle($tableRange)->applyFromArray([
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);
        $this->startRow = $lastRow;
    }

    protected function topNewsReaction($sheet, $queryData)
    {
        $headers = [
            'Đối tượng', 'Nền tảng', 'Top nguồn', 'Nội dung', 'Time', 'Sentiment', 'Like', 'Share/View', 'Comment', 'Tổng tương tác'
        ];
        $data = $this->formatPlatform($queryData);

        $rows = [];

        foreach (self::PROJECT as $projectId => $projectName) {
            $platformRows = [];
            foreach ($data as $platform => $collection) {
                $grouped = $collection
                    ->where('sub_brand_service_id', $projectId)
                    ->groupBy('page_name')
                    ->map(function ($items, $pageName) {
                        $total = $items->sum(fn($item) => $item->total_share + $item->total_like + $item->child_count);
                        $topItem = $items->sortByDesc(fn($item) => $item->total_share + $item->total_like + $item->child_count)->first();
                        return [
                            'page_name' => $pageName,
                            'content' => $topItem->message,
                            'time' => $topItem->content_created,
                            'sentiment' => self::formatState($topItem->state),
                            'like' => $topItem->total_like,
                            'share' => $topItem->total_share,
                            'comment' => $topItem->child_count,
                            'total' => $total,
                        ];
                    })
//                    ->filter(fn($item) => $item['total'] > 0)
                    ->sortByDesc('total')
                    ->take(10)
                    ->values();
                $platformRows[$platform] = $grouped->toArray();
            }
            $rows[$projectName] = $platformRows;
        }
        // Tiêu đề
        $r = $this->startRow + 2;
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, 'TOP NGUỒN TƯƠNG TÁC VỀ BIM GROUP.');
        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);

        $startRow = $r + 2;
        $startCol = 2; // Cột B
        $colIndex = $startCol;

        // Header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Style header
        $headerRange = Coordinate::stringFromColumnIndex($startCol) . $startRow . ':' .
            Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'],
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // --- Render dữ liệu ---
        $currentRow = $startRow + 1;
        foreach ($rows as $projectName => $platformRows) {
            // Tính tổng số dòng để merge Đối tượng
            $totalRowsForProject = collect($platformRows)->sum(fn($p) => count($p));

            $projectStartRow = $currentRow;

            foreach ($platformRows as $platform => $pages) {
                $platformStartRow = $currentRow;

                foreach ($pages as $page) {
                    $sheet->fromArray([
                        $projectName,                  // Đối tượng
                        ucfirst($platform),            // Nền tảng
                        $page['page_name'],            // Top nguồn
                        $page['content'],              // Nội dung
                        $page['time'],                 // Time
                        $page['sentiment'],            // Sentiment
                        $page['like'] ?? 0,
                        $page['share'] ?? 0,
                        $page['comment'] ?? 0,
                        $page['total'] ?? 0,                // Tổng tương tác
                    ], null, 'B' . $currentRow, true);

                    $currentRow++;
                }

                // Merge cột Nền tảng
                if (count($pages) > 1) {
                    $sheet->mergeCells("C{$platformStartRow}:C" . ($currentRow - 1));
                    $sheet->getStyle("C{$platformStartRow}:C" . ($currentRow - 1))
                        ->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
                }
            }

            // Merge cột Đối tượng
            if ($totalRowsForProject > 1) {
                $sheet->mergeCells("B{$projectStartRow}:B" . ($currentRow - 1));
                $sheet->getStyle("B{$projectStartRow}:B" . ($currentRow - 1))
                    ->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
            }
        }

        // Style borders cho toàn bộ bảng
        $lastCol = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1);
        $lastRow = $currentRow - 1;
        $tableRange = "B{$startRow}:{$lastCol}{$lastRow}";
        $sheet->getStyle($tableRange)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ]
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);
        $this->startRow = $lastRow;
        // Render rows
    }

    protected function general($sheet, $queryData)
    {
        $r = $this->startRow + 2;
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, 'II. TỔNG QUAN BIM GROUP VÀ ĐỐI TỦ');
        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
        $r = $r + 2;
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, '1.Khu vực Hạ Long');
        $hl = [
            41777 => 'Thương hiệu BIM và lãnh đạo',
            41778 => 'SKY M Hạ Long',
            41779 => 'Sora Bay HaLong',
            41780 => 'Marina Bayfront District',
            41781 => 'Grand Bay Halong Villas',
            41782 => 'Sailing Club Residences Ha Long Bay',
            41783 => 'Citadines Marina Halong',
            41784 => 'Aqua City HaLong',
            41785 => 'Horizon Bay',
            41786 => 'Hạ Long Marina',
            41787 => 'Intercontinental Ha Long Bay',
            41799 => 'Sun Centro Town',
            41800 => 'Sun Elite City Hạ Long',
            41803 => 'Xanh Island Cát Bà',
        ];
        $now = Carbon::now(); // thời điểm hiện tại

        $startTime = $now->copy()->subDay()->setTime(15, 0)->format('Y-m-d H:i:s');
        $endTime = $now->copy()->setTime(15, 0)->format('Y-m-d H:i:s');


        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
        $headers = ['Lượng tin', 'Facebook', 'Youtube', 'Online', 'Blog/Forum/Website', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Tổng'];
        $data2 = $this->start3($startTime, $endTime);

        $data = $this->formatPlatform($queryData);
        $data2 = $this->formatPlatform($data);

        $rows = [];

        foreach ($hl as $projectId => $projectName) {
            $platformRows = [];
            $total = 0;

            foreach ($data as $platform => $collection) {
                if ($platform === 'ifollow') {
                    // Tách riêng iFollow
                    $onlineCount = $collection
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 0)
                        ->count();

                    $blogCount = $collection
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 1)
                        ->count();

                    $platformRows['ifollow_online'] = $onlineCount;
                    $platformRows['ifollow_blog'] = $blogCount;

                    $total += $onlineCount + $blogCount;
                } else {
                    $count = $collection->where('sub_brand_service_id', $projectId)->count();
                    $platformRows[$platform] = $count;
                    $total += $count;
                }
            }

            foreach ($data2 as $p => $c) {
                if ($platform === 'ifollow') {
                    // Tách riêng iFollow
                    $onlineCount = $c
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 0)
                        ->count();

                    $blogCount = $c
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 1)
                        ->count();

                    $platformRows['ifollow_online'] = $onlineCount;
                    $platformRows['ifollow_blog'] = $blogCount;

                    $total += $onlineCount + $blogCount;
                } else {
                    $count = $c->where('sub_brand_service_id', $projectId)->count();
                    $platformRows[$platform] = $count;
                    $total += $count;
                }
            }

            // Thêm cột tổng
            $platformRows['total'] = $total;

            $rows[$projectName] = $platformRows;
        }
// Tạo table trong Excel
        $startRow = $r + 2; // dòng bắt đầu (sau tiêu đề)
        $colStart = 'B';

        $sheet->fromArray([$headers], null, $colStart . $startRow); // header
        $sheet->getStyle($colStart . $startRow . ':' . chr(ord($colStart) + count($headers) - 1) . $startRow)
            ->getFont()->setBold(true);

        $currentRow = $startRow + 1;
        foreach ($rows as $projectName => $platformRows) {
            $rowData = array_merge([$projectName], array_values($platformRows));
            $sheet->fromArray([$rowData], null, $colStart . $currentRow, true);
            $currentRow++;
        }

// Thêm dòng tổng cuối cùng
        $totals = [];
        foreach (array_keys(current($rows)) as $platform) {
            $totals[$platform] = array_sum(array_column($rows, $platform));
        }

        $totalRow = array_merge(['Tổng'], array_values($totals));
        $sheet->fromArray([$totalRow], null, $colStart . $currentRow);
        $sheet->getStyle($colStart . $currentRow . ':' . chr(ord($colStart) + count($headers) - 1) . $currentRow)
            ->getFont()->setBold(true);

// Optional: Thêm border cho đẹp
        $endCol = chr(ord($colStart) + count($headers) - 1);
        $sheet->getStyle($colStart . $startRow . ':' . $endCol . $currentRow)
            ->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $this->startRow = $currentRow;
        // Sentiment

        $headers = ['SENTIMENT', 'Positive', 'Negavite', 'Neutral', 'CHỈ SỐ CẢM XÚC'];
        $c = collect($this->formatPlatform($queryData));

        $rows = [];
        foreach ($hl as $projectId => $projectName) {
            $positive = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 1)
                    ->count();
            });
            $negative = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 2)
                    ->count();
            });
            $neutral = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 0)
                    ->count();
            });
            $reactionFinaly = ($positive + $negative) > 0
                ? max(($positive - $negative) / ($positive + $negative), 0)
                : 0;

            $row = [
                $projectName,
                $positive,
                $negative,
                $neutral,
                $reactionFinaly
            ];
            $rows[] = $row;
        }
        // Tính tổng
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }

        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);
        $startRow = $this->startRow + 2;
        $startCol = 2;
        $colIndex = $startCol;

        // Header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Data rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

        // Style chung cho bảng
        $sheet->getStyle($tableRange)->applyFromArray([
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Style header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'] // xám nhẹ cho header
            ]
        ]);
        $this->startRow = $lastRow;
        // Auto width
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
//            $currentWidth = $sheet->getColumnDimension($colLetter)->getWidth();
//            $sheet->getColumnDimension($colLetter)->setWidth($currentWidth + 2);
//        }
        $this->startRow = $this->startRow + 20;
        $firstTotalCell = Coordinate::stringFromColumnIndex($startCol) . $lastRow;
        $lastTotalCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $totalRange = $firstTotalCell . ':' . $lastTotalCell;
        $sheet->getStyle($totalRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9D9D9'],
            ],
        ]);

        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $shareOfVoice = $this->shareOfVoice($queryData, $hl);
        $labels = array_column($shareOfVoice, 0);
        $data = array_column($shareOfVoice, 1);

        $pieConfig = [
            'type' => 'pie',
            'data' => [
                'datasets' => [[
                    'data' => $data,
                    'backgroundColor' => [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                        '#FF9F40', '#E7E9ED', '#FF6384', '#36A2EB', '#FFCE56',
                        '#4BC0C0', '#9966FF', '#FF9F40', '#E7E9ED', '#FF6384',
                        '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
                        '#E7E9ED'
                    ],
                ]],
                'labels' => $labels,
            ],
            'options' => [
                'borderWidth' => 2,
                'fontColor' => '#FFFFFF',
                'fontStyle' => 'bold',
                'responsive' => true,
                'legend' => [
                    'position' => 'left',
                    'display' => true,
                    'labels' => [
                        'fontFamily' => 'Cambria',
                        'fontStyle' => 'bold',
                    ],
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#FFFFFF',
                        'align' => 'end',
                        'font' => [
                            'size' => 12,
                            'weight' => 'bold',
                            'family' => 'Cambria',
                        ],
                        'formatter' => "function(value) {
                    return (value ? value + '%' : '')
                }",
                    ],
                ],
                'title' => [
                    'display' => true,
                    'align' => 'right',
                    'text' => 'Share of Voice',
                    'x' => 500,
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                    'fontFamily' => 'Cambria',
                ],
            ],
        ];
        $qc->setConfig(json_encode($pieConfig));
        $pathToPieImg = storage_path('app/pie_chart.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Share of Voice');
        $drawing->setDescription('Share of Voice');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $project = $hl;
        $collect = collect($this->formatPlatform($queryData));
        $datasets = [];
        $platforms = ['Facebook', 'Youtube', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Online News', 'Blog/Forum/Website'];

        foreach ($project as $k => $v) {
            $d = [];

            foreach ($platforms as $platform) {
                if ($platform === 'Online News' || $platform === 'Blog/Forum/Website') {
                    // Map platform name to web_status_4
                    $status = $platform === 'Online News' ? 0 : 1;

                    // Count only Ifollow data with specific web_status_4
                    $count = $collect['ifollow']
                        ->where('sub_brand_service_id', $k)
                        ->where('web_status_4', $status)
                        ->count();
                } else {
                    $math = match($platform){
                        'Review Map' => 'review_google',
                        'Facebook' => 'facebook',
                        'Zalo' => 'zalo',
                        'Youtube' => 'youtube',
                        'Tiktok' => 'tiktok',
                        'Paper' => 'paper',
                        'TV' => 'tv',

                    };
                    // Count normally for other platforms
                    $count = $collect[strtolower($math)]
                        ->where('sub_brand_service_id', $k)
                        ->count();
                }

                $d[] = $count;
            }

            $datasets[] = [
                'label' => $v,
                'data' => $d,
                'fill' => true,
            ];
        }

        $config = [
            'type' => 'bar',
            'data' => [
                'labels' => $platforms,
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'position' => 'bottom',
                    'display' => true
                ],
                'plugins' => [
                    'labels' => [
                        'fontSize' => 10
                    ],
                    'datalabels' => [
                        'anchor' => 'end',
                        'align' => 'top',
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Lượng Tin Trên Các Nền Tảng',
                    'fontSize' => 14,
                    'padding' => 16,
                    'fontColor' => '#000000',
                ]
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $this->startRow = $this->startRow + 20;
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($config));
        $pathToPieImg = storage_path('app/line_chart.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Lượng Tin Trên Các Nền Tảng');
        $drawing->setDescription('Lượng Tin Trên Các Nền Tảng');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $groupedByHour = $flat->groupBy(function ($item) {
            return $item->content_created->format('Y-m-d H:00');
        });
        $hours = $groupedByHour->keys()->sort()->values();
        $datasets = collect($hl)->map(function ($projectName, $projectId) use ($hours, $groupedByHour) {
            $data = $hours->map(function ($hour) use ($groupedByHour, $projectId) {
                $group = $groupedByHour[$hour] ?? collect();
                return collect($group)->where('sub_brand_service_id', $projectId)->count();
            })->toArray();

            return [
                'label' => $projectName,
                'data' => $data,
                'fill' => false,
                'borderWidth' => 2,
                'lineTension' => 0.4,
                'cubicInterpolationMode' => 'monotone',
            ];
        })->values()->toArray();
        $chartLineConfig = [
            'type' => 'line',
            'data' => [
                'labels' => range(1, 24), // 0 -> 23
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [
                        ['display' => true],
                    ],
                    'yAxes' => [
                        ['display' => true],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Xu Hướng Tin Bài Theo Giờ',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartLineConfig));
        $pathToPieImg = storage_path('app/line_1_chart.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $this->startRow = $this->startRow + 20;
        $drawing->setName('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setDescription('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $counts = [];
        foreach ($hl as $projectId => $projectName) {
            $projectItems = $flat->where('sub_brand_service_id', $projectId);
            $total = $projectItems->count();

            if ($total === 0) continue; // bỏ qua project không có bài

            $positive = $projectItems->where('state', 1)->count();
            $neutral = $projectItems->where('state', 0)->count();
            $negative = $projectItems->where('state', 2)->count();

            // Tính % cảm xúc
            $counts[] = [
                'project' => $projectName,
                'positive' => round($positive / $total * 100, 2),
                'neutral' => round($neutral / $total * 100, 2),
                'negative' => round($negative / $total * 100, 2),
            ];
        }
        $labels = array_column($counts, 'project');
        $positive = array_column($counts, 'positive');
        $neutral = array_column($counts, 'neutral');
        $negative = array_column($counts, 'negative');

        $datasets = [
            [
                'label' => 'Positive',
                'backgroundColor' => '#4CAF50',
                'data' => $positive,
            ],
            [
                'label' => 'Neutral',
                'backgroundColor' => '#FFC107',
                'data' => $neutral,
            ],
            [
                'label' => 'Negative',
                'backgroundColor' => '#F44336',
                'data' => $negative,
            ],
        ];

        $chartStackedBarConfig = [
            'type' => 'horizontalBar',
            'data' => [
                'labels' => $labels,  // mảng PHP
                'datasets' => $datasets,  // mảng PHP
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [[
                        'stacked' => true,
                        'ticks' => [
                            'min' => 0,
                            'max' => 100,
                        ],
                        'scaleLabel' => [
                            'display' => true,
                            'labelString' => '%', // để chú thích là %
                        ]
                    ]],
                    'yAxes' => [[
                        'stacked' => true,
                    ]],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Tỷ lệ cảm xúc',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                        'formatter' => '%', // QuickChart cho phép hiển thị ký tự
                    ],
                ],
            ],
        ];


        // Xuất ra ảnh và chèn vào Excel
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartStackedBarConfig));
        $imgPath = storage_path('app/emotional_ratio.png');
        $qc->toFile($imgPath);
        $drawing = new Drawing();
        $this->startRow = $this->startRow + 20;
        $drawing->setName('Tỷ lệ cảm xúc');
        $drawing->setDescription('Tỷ lệ cảm xúc');
        $drawing->setPath($imgPath);
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);
    }

    protected function general2($sheet, $queryData)
    {
        $r = $this->startRow + 2;
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, '2.Khu vực Phú Quốc');

        $hl = [
            41788 => 'Phu Quoc Marina',
            41789 => 'Phu Quoc Waterfront',
            41790 => 'Sailing Club Signature Resort Phu Quoc',
            41791 => 'Park Hyatt Phu Quoc Residences',
        ];


        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
        $headers = ['Lượng tin', 'Facebook', 'Youtube', 'Online', 'Blog/Forum/Website', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Tổng'];

        $data = $this->formatPlatform($queryData);

        $rows = [];

        foreach ($hl as $projectId => $projectName) {
            $platformRows = [];
            $total = 0;

            foreach ($data as $platform => $collection) {
                if ($platform === 'ifollow') {
                    // Tách riêng iFollow
                    $onlineCount = $collection
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 0)
                        ->count();

                    $blogCount = $collection
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 1)
                        ->count();

                    $platformRows['ifollow_online'] = $onlineCount;
                    $platformRows['ifollow_blog'] = $blogCount;

                    $total += $onlineCount + $blogCount;
                } else {
                    $count = $collection->where('sub_brand_service_id', $projectId)->count();
                    $platformRows[$platform] = $count;
                    $total += $count;
                }
            }

            // Thêm cột tổng
            $platformRows['total'] = $total;

            $rows[$projectName] = $platformRows;
        }

// Tạo table trong Excel
        $startRow = $r + 2; // dòng bắt đầu (sau tiêu đề)
        $colStart = 'B';

        $sheet->fromArray([$headers], null, $colStart . $startRow); // header
        $sheet->getStyle($colStart . $startRow . ':' . chr(ord($colStart) + count($headers) - 1) . $startRow)
            ->getFont()->setBold(true);

        $currentRow = $startRow + 1;
        foreach ($rows as $projectName => $platformRows) {
            $rowData = array_merge([$projectName], array_values($platformRows));
            $sheet->fromArray([$rowData], null, $colStart . $currentRow, true);
            $currentRow++;
        }

// Thêm dòng tổng cuối cùng
        $totals = [];
        foreach (array_keys(current($rows)) as $platform) {
            $totals[$platform] = array_sum(array_column($rows, $platform));
        }

        $totalRow = array_merge(['Tổng'], array_values($totals));
        $sheet->fromArray([$totalRow], null, $colStart . $currentRow, true);
        $sheet->getStyle($colStart . $currentRow . ':' . chr(ord($colStart) + count($headers) - 1) . $currentRow)
            ->getFont()->setBold(true);

// Optional: Thêm border cho đẹp
        $endCol = chr(ord($colStart) + count($headers) - 1);
        $sheet->getStyle($colStart . $startRow . ':' . $endCol . $currentRow)
            ->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
        $this->startRow = $currentRow;
        // Sentiment

        $headers = ['SENTIMENT', 'Positive', 'Negavite', 'Neutral', 'CHỈ SỐ CẢM XÚC'];
        $c = collect($this->formatPlatform($queryData));

        $rows = [];
        foreach ($hl as $projectId => $projectName) {
            $positive = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 1)
                    ->count();
            });
            $negative = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 2)
                    ->count();
            });
            $neutral = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 0)
                    ->count();
            });
            $reactionFinaly = ($positive + $negative) > 0
                ? max(($positive - $negative) / ($positive + $negative), 0)
                : 0;

            $row = [
                $projectName,
                $positive,
                $negative,
                $neutral,
                $reactionFinaly
            ];
            $rows[] = $row;
        }
        // Tính tổng
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }

        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);
        $startRow = $this->startRow + 2;
        $startCol = 2;
        $colIndex = $startCol;

        // Header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Data rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

        // Style chung cho bảng
        $sheet->getStyle($tableRange)->applyFromArray([
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Style header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'] // xám nhẹ cho header
            ]
        ]);

        // Auto width
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
//            $currentWidth = $sheet->getColumnDimension($colLetter)->getWidth();
//            $sheet->getColumnDimension($colLetter)->setWidth($currentWidth + 2);
//        }

        $firstTotalCell = Coordinate::stringFromColumnIndex($startCol) . $lastRow;
        $lastTotalCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $totalRange = $firstTotalCell . ':' . $lastTotalCell;
        $sheet->getStyle($totalRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9D9D9'],
            ],
        ]);
        $this->startRow = $lastRow;

        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $shareOfVoice = $this->shareOfVoice($queryData, $hl);
        $labels = array_column($shareOfVoice, 0);
        $data = array_column($shareOfVoice, 1);

        $pieConfig = [
            'type' => 'pie',
            'data' => [
                'datasets' => [[
                    'data' => $data,
                    'backgroundColor' => [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                        '#FF9F40', '#E7E9ED', '#FF6384', '#36A2EB', '#FFCE56',
                        '#4BC0C0', '#9966FF', '#FF9F40', '#E7E9ED', '#FF6384',
                        '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
                        '#E7E9ED'
                    ],
                ]],
                'labels' => $labels,
            ],
            'options' => [
                'borderWidth' => 2,
                'fontColor' => '#FFFFFF',
                'fontStyle' => 'bold',
                'responsive' => true,
                'legend' => [
                    'position' => 'left',
                    'display' => true,
                    'labels' => [
                        'fontFamily' => 'Cambria',
                        'fontStyle' => 'bold',
                    ],
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#FFFFFF',
                        'align' => 'end',
                        'font' => [
                            'size' => 12,
                            'weight' => 'bold',
                            'family' => 'Cambria',
                        ],
                        'formatter' => "function(value) {
                    return (value ? value + '%' : '')
                }",
                    ],
                ],
                'title' => [
                    'display' => true,
                    'align' => 'right',
                    'text' => 'Share of Voice',
                    'x' => 500,
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                    'fontFamily' => 'Cambria',
                ],
            ],
        ];
        $qc->setConfig(json_encode($pieConfig));
        $pathToPieImg = storage_path('app/pie_chart111111111.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $this->startRow = $this->startRow + 20;
        $drawing->setName('Share of Voice');
        $drawing->setDescription('Share of Voice');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $project = $hl;
        $collect = collect($this->formatPlatform($queryData));
        $datasets = [];
        $platforms = ['Facebook', 'Youtube', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Online News', 'Blog/Forum/Website'];

        foreach ($project as $k => $v) {
            $d = [];

            foreach ($platforms as $platform) {
                if ($platform === 'Online News' || $platform === 'Blog/Forum/Website') {
                    // Map platform name to web_status_4
                    $status = $platform === 'Online News' ? 0 : 1;

                    // Count only Ifollow data with specific web_status_4
                    $count = $collect['ifollow']
                        ->where('sub_brand_service_id', $k)
                        ->where('web_status_4', $status)
                        ->count();
                } else {
                    $math = match($platform){
                        'Review Map' => 'review_google',
                        'Facebook' => 'facebook',
                        'Zalo' => 'zalo',
                        'Youtube' => 'youtube',
                        'Tiktok' => 'tiktok',
                        'Paper' => 'paper',
                        'TV' => 'tv',

                    };
                    // Count normally for other platforms
                    $count = $collect[strtolower($math)]
                        ->where('sub_brand_service_id', $k)
                        ->count();
                }

                $d[] = $count;
            }

            $datasets[] = [
                'label' => $v,
                'data' => $d,
                'fill' => true,
            ];
        }

        $config = [
            'type' => 'bar',
            'data' => [
                'labels' => $platforms,
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'position' => 'bottom',
                    'display' => true
                ],
                'plugins' => [
                    'labels' => [
                        'fontSize' => 10
                    ],
                    'datalabels' => [
                        'anchor' => 'end',
                        'align' => 'top',
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Lượng Tin Trên Các Nền Tảng',
                    'fontSize' => 14,
                    'padding' => 16,
                    'fontColor' => '#000000',
                ]
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($config));
        $pathToPieImg = storage_path('app/line_chart11111111.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $this->startRow = $this->startRow + 20;
        $drawing->setName('Lượng Tin Trên Các Nền Tảng');
        $drawing->setDescription('Lượng Tin Trên Các Nền Tảng');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $groupedByHour = $flat->groupBy(function ($item) {
            return $item->content_created->format('Y-m-d H:00');
        });
        $hours = $groupedByHour->keys()->sort()->values();
        $datasets = collect($hl)->map(function ($projectName, $projectId) use ($hours, $groupedByHour) {
            $data = $hours->map(function ($hour) use ($groupedByHour, $projectId) {
                $group = $groupedByHour[$hour] ?? collect();
                return collect($group)->where('sub_brand_service_id', $projectId)->count();
            })->toArray();

            return [
                'label' => $projectName,
                'data' => $data,
                'fill' => false,
                'borderWidth' => 2,
                'lineTension' => 0.4,
                'cubicInterpolationMode' => 'monotone',
            ];
        })->values()->toArray();
        $chartLineConfig = [
            'type' => 'line',
            'data' => [
                'labels' => range(1, 24), // 0 -> 23
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [
                        ['display' => true],
                    ],
                    'yAxes' => [
                        ['display' => true],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Xu Hướng Tin Bài Theo Giờ',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartLineConfig));
        $pathToPieImg = storage_path('app/line_1_chart11111111.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $this->startRow = $this->startRow + 20;
        $drawing->setName('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setDescription('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $counts = [];
        foreach ($hl as $projectId => $projectName) {
            $projectItems = $flat->where('sub_brand_service_id', $projectId);
            $total = $projectItems->count();

            if ($total === 0) continue; // bỏ qua project không có bài

            $positive = $projectItems->where('state', 1)->count();
            $neutral = $projectItems->where('state', 0)->count();
            $negative = $projectItems->where('state', 2)->count();

            // Tính % cảm xúc
            $counts[] = [
                'project' => $projectName,
                'positive' => round($positive / $total * 100, 2),
                'neutral' => round($neutral / $total * 100, 2),
                'negative' => round($negative / $total * 100, 2),
            ];
        }
        $labels = array_column($counts, 'project');
        $positive = array_column($counts, 'positive');
        $neutral = array_column($counts, 'neutral');
        $negative = array_column($counts, 'negative');

        $datasets = [
            [
                'label' => 'Positive',
                'backgroundColor' => '#4CAF50',
                'data' => $positive,
            ],
            [
                'label' => 'Neutral',
                'backgroundColor' => '#FFC107',
                'data' => $neutral,
            ],
            [
                'label' => 'Negative',
                'backgroundColor' => '#F44336',
                'data' => $negative,
            ],
        ];

        $chartStackedBarConfig = [
            'type' => 'horizontalBar',
            'data' => [
                'labels' => $labels,  // mảng PHP
                'datasets' => $datasets,  // mảng PHP
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [[
                        'stacked' => true,
                        'ticks' => [
                            'min' => 0,
                            'max' => 100,
                        ],
                        'scaleLabel' => [
                            'display' => true,
                            'labelString' => '%', // để chú thích là %
                        ]
                    ]],
                    'yAxes' => [[
                        'stacked' => true,
                    ]],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Tỷ lệ cảm xúc',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                        'formatter' => '%', // QuickChart cho phép hiển thị ký tự
                    ],
                ],
            ],
        ];


        // Xuất ra ảnh và chèn vào Excel
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartStackedBarConfig));
        $imgPath = storage_path('app/emotional_ratio111111.png');
        $qc->toFile($imgPath);
        $drawing = new Drawing();
        $this->startRow = $this->startRow + 20;
        $drawing->setName('Tỷ lệ cảm xúc');
        $drawing->setDescription('Tỷ lệ cảm xúc');
        $drawing->setPath($imgPath);
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);
    }

    protected function general3($sheet, $queryData)
    {
        $r = $this->startRow + 2;
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, '3.Khu vực Vĩnh Phúc');

        $hl = [
            41792 => 'Thanh Xuân Valley',
            41796 => 'Valley Town',
            41795 => 'Valley Park Residences',
            41794 => 'Spring Residences',
            41793 => 'Intercontinental Thanh Xuan Valley',
        ];


        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
        $headers = ['Lượng tin', 'Facebook', 'Youtube', 'Online', 'Blog/Forum/Website', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Tổng'];

        $data = $this->formatPlatform($queryData);

        $rows = [];

        foreach ($hl as $projectId => $projectName) {
            $platformRows = [];
            $total = 0;

            foreach ($data as $platform => $collection) {
                if ($platform === 'ifollow') {
                    // Tách riêng iFollow
                    $onlineCount = $collection
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 0)
                        ->count();

                    $blogCount = $collection
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 1)
                        ->count();

                    $platformRows['ifollow_online'] = $onlineCount;
                    $platformRows['ifollow_blog'] = $blogCount;

                    $total += $onlineCount + $blogCount;
                } else {
                    $count = $collection->where('sub_brand_service_id', $projectId)->count();
                    $platformRows[$platform] = $count;
                    $total += $count;
                }
            }

            // Thêm cột tổng
            $platformRows['total'] = $total;

            $rows[$projectName] = $platformRows;
        }

// Tạo table trong Excel
        $startRow = $r + 2; // dòng bắt đầu (sau tiêu đề)
        $colStart = 'B';

        $sheet->fromArray([$headers], null, $colStart . $startRow); // header
        $sheet->getStyle($colStart . $startRow . ':' . chr(ord($colStart) + count($headers) - 1) . $startRow)
            ->getFont()->setBold(true);

        $currentRow = $startRow + 1;
        foreach ($rows as $projectName => $platformRows) {
            $rowData = array_merge([$projectName], array_values($platformRows));
            $sheet->fromArray([$rowData], null, $colStart . $currentRow, true);
            $currentRow++;
        }

// Thêm dòng tổng cuối cùng
        $totals = [];
        foreach (array_keys(current($rows)) as $platform) {
            $totals[$platform] = array_sum(array_column($rows, $platform));
        }

        $totalRow = array_merge(['Tổng'], array_values($totals));
        $sheet->fromArray([$totalRow], null, $colStart . $currentRow, true);
        $sheet->getStyle($colStart . $currentRow . ':' . chr(ord($colStart) + count($headers) - 1) . $currentRow)
            ->getFont()->setBold(true);

// Optional: Thêm border cho đẹp
        $endCol = chr(ord($colStart) + count($headers) - 1);
        $sheet->getStyle($colStart . $startRow . ':' . $endCol . $currentRow)
            ->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
        $this->startRow = $currentRow;
        // Sentiment

        $headers = ['SENTIMENT', 'Positive', 'Negavite', 'Neutral', 'CHỈ SỐ CẢM XÚC'];
        $c = collect($this->formatPlatform($queryData));

        $rows = [];
        foreach ($hl as $projectId => $projectName) {
            $positive = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 1)
                    ->count();
            });
            $negative = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 2)
                    ->count();
            });
            $neutral = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 0)
                    ->count();
            });
            $reactionFinaly = ($positive + $negative) > 0
                ? max(($positive - $negative) / ($positive + $negative), 0)
                : 0;

            $row = [
                $projectName,
                $positive,
                $negative,
                $neutral,
                $reactionFinaly
            ];
            $rows[] = $row;
        }
        // Tính tổng
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }

        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);
        $startRow = $this->startRow + 2;
        $startCol = 2;
        $colIndex = $startCol;

        // Header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Data rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

        // Style chung cho bảng
        $sheet->getStyle($tableRange)->applyFromArray([
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Style header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'] // xám nhẹ cho header
            ]
        ]);

        // Auto width
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
//            $currentWidth = $sheet->getColumnDimension($colLetter)->getWidth();
//            $sheet->getColumnDimension($colLetter)->setWidth($currentWidth + 2);
//        }

        $firstTotalCell = Coordinate::stringFromColumnIndex($startCol) . $lastRow;
        $lastTotalCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $totalRange = $firstTotalCell . ':' . $lastTotalCell;
        $sheet->getStyle($totalRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9D9D9'],
            ],
        ]);
        $this->startRow = $lastRow;

        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $shareOfVoice = $this->shareOfVoice($queryData, $hl);
        $labels = array_column($shareOfVoice, 0);
        $data = array_column($shareOfVoice, 1);

        $pieConfig = [
            'type' => 'pie',
            'data' => [
                'datasets' => [[
                    'data' => $data,
                    'backgroundColor' => [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                        '#FF9F40', '#E7E9ED', '#FF6384', '#36A2EB', '#FFCE56',
                        '#4BC0C0', '#9966FF', '#FF9F40', '#E7E9ED', '#FF6384',
                        '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
                        '#E7E9ED'
                    ],
                ]],
                'labels' => $labels,
            ],
            'options' => [
                'borderWidth' => 2,
                'fontColor' => '#FFFFFF',
                'fontStyle' => 'bold',
                'responsive' => true,
                'legend' => [
                    'position' => 'left',
                    'display' => true,
                    'labels' => [
                        'fontFamily' => 'Cambria',
                        'fontStyle' => 'bold',
                    ],
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#FFFFFF',
                        'align' => 'end',
                        'font' => [
                            'size' => 12,
                            'weight' => 'bold',
                            'family' => 'Cambria',
                        ],
                        'formatter' => "function(value) {
                    return (value ? value + '%' : '')
                }",
                    ],
                ],
                'title' => [
                    'display' => true,
                    'align' => 'right',
                    'text' => 'Share of Voice',
                    'x' => 500,
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                    'fontFamily' => 'Cambria',
                ],
            ],
        ];
        $qc->setConfig(json_encode($pieConfig));
        $pathToPieImg = storage_path('app/pie_chartvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $this->startRow = $this->startRow + 20;
        $drawing->setName('Share of Voice');
        $drawing->setDescription('Share of Voice');
        $drawing->setPath($pathToPieImg);
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $project = $hl;
        $collect = collect($this->formatPlatform($queryData));
        $datasets = [];
        $platforms = ['Facebook', 'Youtube', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Online News', 'Blog/Forum/Website'];

        foreach ($project as $k => $v) {
            $d = [];

            foreach ($platforms as $platform) {
                if ($platform === 'Online News' || $platform === 'Blog/Forum/Website') {
                    // Map platform name to web_status_4
                    $status = $platform === 'Online News' ? 0 : 1;

                    // Count only Ifollow data with specific web_status_4
                    $count = $collect['ifollow']
                        ->where('sub_brand_service_id', $k)
                        ->where('web_status_4', $status)
                        ->count();
                } else {
                    $math = match($platform){
                        'Review Map' => 'review_google',
                        'Facebook' => 'facebook',
                        'Zalo' => 'zalo',
                        'Youtube' => 'youtube',
                        'Tiktok' => 'tiktok',
                        'Paper' => 'paper',
                        'TV' => 'tv',

                    };
                    // Count normally for other platforms
                    $count = $collect[strtolower($math)]
                        ->where('sub_brand_service_id', $k)
                        ->count();
                }

                $d[] = $count;
            }

            $datasets[] = [
                'label' => $v,
                'data' => $d,
                'fill' => true,
            ];
        }
        $config = [
            'type' => 'bar',
            'data' => [
                'labels' => $platforms,
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'position' => 'bottom',
                    'display' => true
                ],
                'plugins' => [
                    'labels' => [
                        'fontSize' => 10
                    ],
                    'datalabels' => [
                        'anchor' => 'end',
                        'align' => 'top',
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Lượng Tin Trên Các Nền Tảng',
                    'fontSize' => 14,
                    'padding' => 16,
                    'fontColor' => '#000000',
                ]
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($config));
        $pathToPieImg = storage_path('app/line_chartvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Lượng Tin Trên Các Nền Tảng');
        $drawing->setDescription('Lượng Tin Trên Các Nền Tảng');
        $drawing->setPath($pathToPieImg);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $groupedByHour = $flat->groupBy(function ($item) {
            return $item->content_created->format('Y-m-d H:00');
        });
        $hours = $groupedByHour->keys()->sort()->values();
        $datasets = collect($hl)->map(function ($projectName, $projectId) use ($hours, $groupedByHour) {
            $data = $hours->map(function ($hour) use ($groupedByHour, $projectId) {
                $group = $groupedByHour[$hour] ?? collect();
                return collect($group)->where('sub_brand_service_id', $projectId)->count();
            })->toArray();

            return [
                'label' => $projectName,
                'data' => $data,
                'fill' => false,
                'borderWidth' => 2,
                'lineTension' => 0.4,
                'cubicInterpolationMode' => 'monotone',
            ];
        })->values()->toArray();
        $chartLineConfig = [
            'type' => 'line',
            'data' => [
                'labels' => range(1, 24), // 0 -> 23
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [
                        ['display' => true],
                    ],
                    'yAxes' => [
                        ['display' => true],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Xu Hướng Tin Bài Theo Giờ',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartLineConfig));
        $pathToPieImg = storage_path('app/line_1_chartvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setDescription('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setPath($pathToPieImg);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $counts = [];
        foreach ($hl as $projectId => $projectName) {
            $projectItems = $flat->where('sub_brand_service_id', $projectId);
            $total = $projectItems->count();

            if ($total === 0) continue; // bỏ qua project không có bài

            $positive = $projectItems->where('state', 1)->count();
            $neutral = $projectItems->where('state', 0)->count();
            $negative = $projectItems->where('state', 2)->count();

            // Tính % cảm xúc
            $counts[] = [
                'project' => $projectName,
                'positive' => round($positive / $total * 100, 2),
                'neutral' => round($neutral / $total * 100, 2),
                'negative' => round($negative / $total * 100, 2),
            ];
        }
        $labels = array_column($counts, 'project');
        $positive = array_column($counts, 'positive');
        $neutral = array_column($counts, 'neutral');
        $negative = array_column($counts, 'negative');

        $datasets = [
            [
                'label' => 'Positive',
                'backgroundColor' => '#4CAF50',
                'data' => $positive,
            ],
            [
                'label' => 'Neutral',
                'backgroundColor' => '#FFC107',
                'data' => $neutral,
            ],
            [
                'label' => 'Negative',
                'backgroundColor' => '#F44336',
                'data' => $negative,
            ],
        ];

        $chartStackedBarConfig = [
            'type' => 'horizontalBar',
            'data' => [
                'labels' => $labels,  // mảng PHP
                'datasets' => $datasets,  // mảng PHP
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [[
                        'stacked' => true,
                        'ticks' => [
                            'min' => 0,
                            'max' => 100,
                        ],
                        'scaleLabel' => [
                            'display' => true,
                            'labelString' => '%', // để chú thích là %
                        ]
                    ]],
                    'yAxes' => [[
                        'stacked' => true,
                    ]],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Tỷ lệ cảm xúc',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                        'formatter' => '%', // QuickChart cho phép hiển thị ký tự
                    ],
                ],
            ],
        ];


        // Xuất ra ảnh và chèn vào Excel
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartStackedBarConfig));
        $imgPath = storage_path('app/emotional_ratiovp.png');
        $qc->toFile($imgPath);
        $drawing = new Drawing();
        $drawing->setName('Tỷ lệ cảm xúc');
        $drawing->setDescription('Tỷ lệ cảm xúc');
        $drawing->setPath($imgPath);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);
    }

    protected function general4($sheet, $queryData)
    {
        $r = $this->startRow + 2;
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, '3.Khu vực Vĩnh Phúc');

        $hl = [
            41792 => 'Thanh Xuân Valley',
            41795 => 'Valley Park Residences',
            41796 => 'Valley Town',
            41793 => 'Intercontinental Thanh Xuan Valley',
            41794 => 'Spring Residences',

        ];


        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
        $headers = ['Lượng tin', 'Facebook', 'Youtube', 'Online', 'Blog/Forum/Website', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Tổng'];

        $data = $this->formatPlatform($queryData);

        $rows = [];

        foreach ($hl as $projectId => $projectName) {
            $platformRows = [];
            $total = 0;

            foreach ($data as $platform => $collection) {
                if ($platform === 'ifollow') {
                    // Tách riêng iFollow
                    $onlineCount = $collection
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 0)
                        ->count();

                    $blogCount = $collection
                        ->where('sub_brand_service_id', $projectId)
                        ->where('web_status_4', 1)
                        ->count();

                    $platformRows['ifollow_online'] = $onlineCount;
                    $platformRows['ifollow_blog'] = $blogCount;

                    $total += $onlineCount + $blogCount;
                } else {
                    $count = $collection->where('sub_brand_service_id', $projectId)->count();
                    $platformRows[$platform] = $count;
                    $total += $count;
                }
            }

            // Thêm cột tổng
            $platformRows['total'] = $total;

            $rows[$projectName] = $platformRows;
        }

// Tạo table trong Excel
        $startRow = $this->startRow + 2; // dòng bắt đầu (sau tiêu đề)
        $colStart = 'B';

        $sheet->fromArray([$headers], null, $colStart . $startRow); // header
        $sheet->getStyle($colStart . $startRow . ':' . chr(ord($colStart) + count($headers) - 1) . $startRow)
            ->getFont()->setBold(true);

        $currentRow = $startRow + 1;
        foreach ($rows as $projectName => $platformRows) {
            $rowData = array_merge([$projectName], array_values($platformRows));
            $sheet->fromArray([$rowData], null, $colStart . $currentRow, true);
            $currentRow++;
        }

// Thêm dòng tổng cuối cùng
        $totals = [];
        foreach (array_keys(current($rows)) as $platform) {
            $totals[$platform] = array_sum(array_column($rows, $platform));
        }

        $totalRow = array_merge(['Tổng'], array_values($totals));
        $sheet->fromArray([$totalRow], null, $colStart . $currentRow, true);
        $sheet->getStyle($colStart . $currentRow . ':' . chr(ord($colStart) + count($headers) - 1) . $currentRow)
            ->getFont()->setBold(true);

// Optional: Thêm border cho đẹp
        $endCol = chr(ord($colStart) + count($headers) - 1);
        $sheet->getStyle($colStart . $startRow . ':' . $endCol . $currentRow)
            ->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $this->startRow = $currentRow;
        // Sentiment

        $headers = ['SENTIMENT', 'Positive', 'Negavite', 'Neutral', 'CHỈ SỐ CẢM XÚC'];
        $c = collect($this->formatPlatform($queryData));

        $rows = [];
        foreach ($hl as $projectId => $projectName) {
            $positive = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 1)
                    ->count();
            });
            $negative = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 2)
                    ->count();
            });
            $neutral = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 0)
                    ->count();
            });
            $reactionFinaly = ($positive + $negative) > 0
                ? max(($positive - $negative) / ($positive + $negative), 0)
                : 0;

            $row = [
                $projectName,
                $positive,
                $negative,
                $neutral,
                $reactionFinaly
            ];
            $rows[] = $row;
        }
        // Tính tổng
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }

        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);
        $startRow = $this->startRow + 2;
        $startCol = 2;
        $colIndex = $startCol;

        // Header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Data rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

        // Style chung cho bảng
        $sheet->getStyle($tableRange)->applyFromArray([
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Style header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'] // xám nhẹ cho header
            ]
        ]);


        $firstTotalCell = Coordinate::stringFromColumnIndex($startCol) . $lastRow;
        $lastTotalCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $totalRange = $firstTotalCell . ':' . $lastTotalCell;
        $sheet->getStyle($totalRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9D9D9'],
            ],
        ]);
        $this->startRow = $lastRow;

        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $shareOfVoice = $this->shareOfVoice($queryData, $hl);
        $labels = array_column($shareOfVoice, 0);
        $data = array_column($shareOfVoice, 1);

        $pieConfig = [
            'type' => 'pie',
            'data' => [
                'datasets' => [[
                    'data' => $data,
                    'backgroundColor' => [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                        '#FF9F40', '#E7E9ED', '#FF6384', '#36A2EB', '#FFCE56',
                        '#4BC0C0', '#9966FF', '#FF9F40', '#E7E9ED', '#FF6384',
                        '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
                        '#E7E9ED'
                    ],
                ]],
                'labels' => $labels,
            ],
            'options' => [
                'borderWidth' => 2,
                'fontColor' => '#FFFFFF',
                'fontStyle' => 'bold',
                'responsive' => true,
                'legend' => [
                    'position' => 'left',
                    'display' => true,
                    'labels' => [
                        'fontFamily' => 'Cambria',
                        'fontStyle' => 'bold',
                    ],
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#FFFFFF',
                        'align' => 'end',
                        'font' => [
                            'size' => 12,
                            'weight' => 'bold',
                            'family' => 'Cambria',
                        ],
                        'formatter' => "function(value) {
                    return (value ? value + '%' : '')
                }",
                    ],
                ],
                'title' => [
                    'display' => true,
                    'align' => 'right',
                    'text' => 'Share of Voice',
                    'x' => 500,
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                    'fontFamily' => 'Cambria',
                ],
            ],
        ];
        $qc->setConfig(json_encode($pieConfig));
        $pathToPieImg = storage_path('app/pie_chartvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Share of Voice');
        $drawing->setDescription('Share of Voice');
        $drawing->setPath($pathToPieImg);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $project = $hl;
        $collect = collect($this->formatPlatform($queryData));
        $datasets = [];
        $platforms = ['Facebook', 'Youtube', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Online News', 'Blog/Forum/Website'];

        foreach ($project as $k => $v) {
            $d = [];

            foreach ($platforms as $platform) {
                if ($platform === 'Online News' || $platform === 'Blog/Forum/Website') {
                    // Map platform name to web_status_4
                    $status = $platform === 'Online News' ? 0 : 1;

                    // Count only Ifollow data with specific web_status_4
                    $count = $collect['ifollow']
                        ->where('sub_brand_service_id', $k)
                        ->where('web_status_4', $status)
                        ->count();
                } else {
                    $math = match($platform){
                        'Review Map' => 'review_google',
                        'Facebook' => 'facebook',
                        'Zalo' => 'zalo',
                        'Youtube' => 'youtube',
                        'Tiktok' => 'tiktok',
                        'Paper' => 'paper',
                        'TV' => 'tv',

                    };
                    // Count normally for other platforms
                    $count = $collect[strtolower($math)]
                        ->where('sub_brand_service_id', $k)
                        ->count();
                }

                $d[] = $count;
            }

            $datasets[] = [
                'label' => $v,
                'data' => $d,
                'fill' => true,
            ];
        }

        $config = [
            'type' => 'bar',
            'data' => [
                'labels' => $platforms,
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'position' => 'bottom',
                    'display' => true
                ],
                'plugins' => [
                    'labels' => [
                        'fontSize' => 10
                    ],
                    'datalabels' => [
                        'anchor' => 'end',
                        'align' => 'top',
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Lượng Tin Trên Các Nền Tảng',
                    'fontSize' => 14,
                    'padding' => 16,
                    'fontColor' => '#000000',
                ]
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($config));
        $pathToPieImg = storage_path('app/line_chartvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Lượng Tin Trên Các Nền Tảng');
        $drawing->setDescription('Lượng Tin Trên Các Nền Tảng');
        $drawing->setPath($pathToPieImg);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $groupedByHour = $flat->groupBy(function ($item) {
            return $item->content_created->format('Y-m-d H:00');
        });
        $hours = $groupedByHour->keys()->sort()->values();
        $datasets = collect($hl)->map(function ($projectName, $projectId) use ($hours, $groupedByHour) {
            $data = $hours->map(function ($hour) use ($groupedByHour, $projectId) {
                $group = $groupedByHour[$hour] ?? collect();
                return collect($group)->where('sub_brand_service_id', $projectId)->count();
            })->toArray();

            return [
                'label' => $projectName,
                'data' => $data,
                'fill' => false,
                'borderWidth' => 2,
                'lineTension' => 0.4,
                'cubicInterpolationMode' => 'monotone',
            ];
        })->values()->toArray();
        $chartLineConfig = [
            'type' => 'line',
            'data' => [
                'labels' => range(1, 24), // 0 -> 23
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [
                        ['display' => true],
                    ],
                    'yAxes' => [
                        ['display' => true],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Xu Hướng Tin Bài Theo Giờ',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartLineConfig));
        $pathToPieImg = storage_path('app/line_1_chartvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setDescription('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setPath($pathToPieImg);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $counts = [];
        foreach ($hl as $projectId => $projectName) {
            $projectItems = $flat->where('sub_brand_service_id', $projectId);
            $total = $projectItems->count();

            if ($total === 0) continue; // bỏ qua project không có bài

            $positive = $projectItems->where('state', 1)->count();
            $neutral = $projectItems->where('state', 0)->count();
            $negative = $projectItems->where('state', 2)->count();

            // Tính % cảm xúc
            $counts[] = [
                'project' => $projectName,
                'positive' => round($positive / $total * 100, 2),
                'neutral' => round($neutral / $total * 100, 2),
                'negative' => round($negative / $total * 100, 2),
            ];
        }
        $labels = array_column($counts, 'project');
        $positive = array_column($counts, 'positive');
        $neutral = array_column($counts, 'neutral');
        $negative = array_column($counts, 'negative');

        $datasets = [
            [
                'label' => 'Positive',
                'backgroundColor' => '#4CAF50',
                'data' => $positive,
            ],
            [
                'label' => 'Neutral',
                'backgroundColor' => '#FFC107',
                'data' => $neutral,
            ],
            [
                'label' => 'Negative',
                'backgroundColor' => '#F44336',
                'data' => $negative,
            ],
        ];

        $chartStackedBarConfig = [
            'type' => 'horizontalBar',
            'data' => [
                'labels' => $labels,  // mảng PHP
                'datasets' => $datasets,  // mảng PHP
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [[
                        'stacked' => true,
                        'ticks' => [
                            'min' => 0,
                            'max' => 100,
                        ],
                        'scaleLabel' => [
                            'display' => true,
                            'labelString' => '%', // để chú thích là %
                        ]
                    ]],
                    'yAxes' => [[
                        'stacked' => true,
                    ]],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Tỷ lệ cảm xúc',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                        'formatter' => '%', // QuickChart cho phép hiển thị ký tự
                    ],
                ],
            ],
        ];


        // Xuất ra ảnh và chèn vào Excel
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartStackedBarConfig));
        $imgPath = storage_path('app/emotional_ratiovp.png');
        $qc->toFile($imgPath);
        $drawing = new Drawing();
        $drawing->setName('Tỷ lệ cảm xúc');
        $drawing->setDescription('Tỷ lệ cảm xúc');
        $drawing->setPath($imgPath);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);
    }

    protected function general5($sheet, $queryData)
    {
        $now = Carbon::now(); // thời điểm hiện tại
        $startTime = $now->copy()->subDay()->setTime(15, 0)->format('Y-m-d H:i:s');
        $endTime = $now->copy()->setTime(15, 0)->format('Y-m-d H:i:s');
        $r = $this->startRow + 2;
        $queryData = $this->start2($startTime, $endTime);
        $titleCell = 'B' . $r;
        $sheet->setCellValue($titleCell, '5. Khu vực khác');

        $hl = [
            41801 => 'Eco Retreat',
            41802 => 'Waterpoint Nam Long',
        ];


        $sheet->getStyle($titleCell)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ]);
        $headers = ['Lượng tin', 'Facebook', 'Youtube', 'Ifollow', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Tổng'];
        $data = $this->formatPlatform($queryData);

        $rows = [];
        foreach ($hl as $projectId => $projectName) {
            $platformRows = [];
            $total = 0;

            foreach ($data as $platform => $collection) {
                $count = $collection->where('sub_brand_service_id', $projectId)->count();
                $platformRows[$platform] = $count;
                $total += $count;
            }

            // Thêm cột tổng
            $platformRows['total'] = $total;

            $rows[$projectName] = $platformRows;
        }

// Tạo table trong Excel
        $startRow = $r + 2; // dòng bắt đầu (sau tiêu đề)
        $colStart = 'B';

        $sheet->fromArray([$headers], null, $colStart . $startRow); // header
        $sheet->getStyle($colStart . $startRow . ':' . chr(ord($colStart) + count($headers) - 1) . $startRow)
            ->getFont()->setBold(true);

        $currentRow = $startRow + 1;
        foreach ($rows as $projectName => $platformRows) {
            $rowData = array_merge([$projectName], array_values($platformRows));
            $sheet->fromArray([$rowData], null, $colStart . $currentRow, true);
            $currentRow++;
        }

// Thêm dòng tổng cuối cùng
        $totals = [];
        foreach (array_keys(current($rows)) as $platform) {
            $totals[$platform] = array_sum(array_column($rows, $platform));
        }

        $totalRow = array_merge(['Tổng'], array_values($totals));
        $sheet->fromArray([$totalRow], null, $colStart . $currentRow, true);
        $sheet->getStyle($colStart . $currentRow . ':' . chr(ord($colStart) + count($headers) - 1) . $currentRow)
            ->getFont()->setBold(true);

// Optional: Thêm border cho đẹp
        $endCol = chr(ord($colStart) + count($headers) - 1);
        $sheet->getStyle($colStart . $startRow . ':' . $endCol . $currentRow)
            ->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $this->startRow = $currentRow;
        // Sentiment

        $headers = ['SENTIMENT', 'Positive', 'Negavite', 'Neutral', 'CHỈ SỐ CẢM XÚC'];
        $c = collect($this->formatPlatform($queryData));

        $rows = [];
        foreach ($hl as $projectId => $projectName) {
            $positive = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 1)
                    ->count();
            });
            $negative = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 2)
                    ->count();
            });
            $neutral = $c->sum(function ($item) use ($projectId) {
                return $item->where('sub_brand_service_id', $projectId)
                    ->where('state', 0)
                    ->count();
            });
            $reactionFinaly = ($positive + $negative) > 0
                ? max(($positive - $negative) / ($positive + $negative), 0)
                : 0;

            $row = [
                $projectName,
                $positive,
                $negative,
                $neutral,
                $reactionFinaly
            ];
            $rows[] = $row;
        }
        // Tính tổng
        $columnCount = count($headers);
        $totals = array_fill(1, $columnCount - 1, 0);
        foreach ($rows as $row) {
            for ($i = 1; $i < $columnCount; $i++) {
                $totals[$i] += $row[$i];
            }
        }

        // Thêm hàng "Tổng"
        $rows[] = array_merge(['Tổng'], $totals);
        $startRow = $this->startRow + 2;
        $startCol = 2;
        $colIndex = $startCol;

        // Header
        foreach ($headers as $header) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
            $sheet->setCellValue($colLetter . $startRow, $header);
            $colIndex++;
        }

        // Data rows
        $currentRow = $startRow + 1;
        foreach ($rows as $row) {
            $colIndex = $startCol;
            foreach ($row as $cellValue) {
                $colLetter = Coordinate::stringFromColumnIndex($colIndex);
                $sheet->setCellValue($colLetter . $currentRow, $cellValue);
                $colIndex++;
            }
            $currentRow++;
        }

        $lastRow = $currentRow - 1;
        $firstCell = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $tableRange = $firstCell . ':' . $lastCell;

        // Style chung cho bảng
        $sheet->getStyle($tableRange)->applyFromArray([
            'font' => ['bold' => false],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Style header
        $firstHeader = Coordinate::stringFromColumnIndex($startCol) . $startRow;
        $lastHeader = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $startRow;
        $headerRange = $firstHeader . ':' . $lastHeader;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'DDDDDD'] // xám nhẹ cho header
            ]
        ]);

        // Auto width
//        foreach (range($startCol, $startCol + count($headers) - 1) as $colIndex) {
//            $colLetter = Coordinate::stringFromColumnIndex($colIndex);
//            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
//            $currentWidth = $sheet->getColumnDimension($colLetter)->getWidth();
//            $sheet->getColumnDimension($colLetter)->setWidth($currentWidth + 2);
//        }

        $firstTotalCell = Coordinate::stringFromColumnIndex($startCol) . $lastRow;
        $lastTotalCell = Coordinate::stringFromColumnIndex($startCol + count($headers) - 1) . $lastRow;
        $totalRange = $firstTotalCell . ':' . $lastTotalCell;
        $sheet->getStyle($totalRange)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'D9D9D9'],
            ],
        ]);
        $this->startRow = $lastRow;

        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $shareOfVoice = $this->shareOfVoice($queryData, $hl);
        $labels = array_column($shareOfVoice, 0);
        $data = array_column($shareOfVoice, 1);

        $pieConfig = [
            'type' => 'pie',
            'data' => [
                'datasets' => [[
                    'data' => $data,
                    'backgroundColor' => [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                        '#FF9F40', '#E7E9ED', '#FF6384', '#36A2EB', '#FFCE56',
                        '#4BC0C0', '#9966FF', '#FF9F40', '#E7E9ED', '#FF6384',
                        '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
                        '#E7E9ED'
                    ],
                ]],
                'labels' => $labels,
            ],
            'options' => [
                'borderWidth' => 2,
                'fontColor' => '#FFFFFF',
                'fontStyle' => 'bold',
                'responsive' => true,
                'legend' => [
                    'position' => 'left',
                    'display' => true,
                    'labels' => [
                        'fontFamily' => 'Cambria',
                        'fontStyle' => 'bold',
                    ],
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#FFFFFF',
                        'align' => 'end',
                        'font' => [
                            'size' => 12,
                            'weight' => 'bold',
                            'family' => 'Cambria',
                        ],
                        'formatter' => "function(value) {
                    return (value ? value + '%' : '')
                }",
                    ],
                ],
                'title' => [
                    'display' => true,
                    'align' => 'right',
                    'text' => 'Share of Voice',
                    'x' => 500,
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                    'fontFamily' => 'Cambria',
                ],
            ],
        ];
        $qc->setConfig(json_encode($pieConfig));
        $pathToPieImg = storage_path('app/pie_chartvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Share of Voice');
        $drawing->setDescription('Share of Voice');
        $drawing->setPath($pathToPieImg);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $project = $hl;
        $collect = collect($this->formatPlatform($queryData));
        $datasets = [];
        $platforms = ['Facebook', 'Youtube', 'Zalo', 'Tiktok', 'Review Map', 'Paper', 'TV', 'Online News', 'Blog/Forum/Website'];

        foreach ($project as $k => $v) {
            $d = [];

            foreach ($platforms as $platform) {
                if ($platform === 'Online News' || $platform === 'Blog/Forum/Website') {
                    // Map platform name to web_status_4
                    $status = $platform === 'Online News' ? 0 : 1;

                    // Count only Ifollow data with specific web_status_4
                    $count = $collect['ifollow']
                        ->where('sub_brand_service_id', $k)
                        ->where('web_status_4', $status)
                        ->count();
                } else {
                    // Count normally for other platforms
                    $math = match($platform){
                        'Review Map' => 'review_google',
                        'Facebook' => 'facebook',
                        'Zalo' => 'zalo',
                        'Youtube' => 'youtube',
                        'Tiktok' => 'tiktok',
                        'Paper' => 'paper',
                        'TV' => 'tv',

                    };
                    $count = $collect[$math]
                        ->where('sub_brand_service_id', $k)
                        ->count();
                }

                $d[] = $count;
            }

            $datasets[] = [
                'label' => $v,
                'data' => $d,
                'fill' => true,
            ];
        }

        $config = [
            'type' => 'bar',
            'data' => [
                'labels' => $platforms,
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'position' => 'bottom',
                    'display' => true
                ],
                'plugins' => [
                    'labels' => [
                        'fontSize' => 10
                    ],
                    'datalabels' => [
                        'anchor' => 'end',
                        'align' => 'top',
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Lượng Tin Trên Các Nền Tảng',
                    'fontSize' => 14,
                    'padding' => 16,
                    'fontColor' => '#000000',
                ]
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($config));
        $pathToPieImg = storage_path('app/line_cha2222rtvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Lượng Tin Trên Các Nền Tảng');
        $drawing->setDescription('Lượng Tin Trên Các Nền Tảng');
        $drawing->setPath($pathToPieImg);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow); // vị trí bắt đầu (ví dụ A20)
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $groupedByHour = $flat->groupBy(function ($item) {
            return $item->content_created->format('Y-m-d H:00');
        });
        $hours = $groupedByHour->keys()->sort()->values();
        $datasets = collect($hl)->map(function ($projectName, $projectId) use ($hours, $groupedByHour) {
            $data = $hours->map(function ($hour) use ($groupedByHour, $projectId) {
                $group = $groupedByHour[$hour] ?? collect();
                return collect($group)->where('sub_brand_service_id', $projectId)->count();
            })->toArray();

            return [
                'label' => $projectName,
                'data' => $data,
                'fill' => false,
                'borderWidth' => 2,
                'lineTension' => 0.4,
                'cubicInterpolationMode' => 'monotone',
            ];
        })->values()->toArray();
        $chartLineConfig = [
            'type' => 'line',
            'data' => [
                'labels' => range(1, 24), // 0 -> 23
                'datasets' => $datasets,
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [
                        ['display' => true],
                    ],
                    'yAxes' => [
                        ['display' => true],
                    ],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Xu Hướng Tin Bài Theo Giờ',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
            ],
        ];
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartLineConfig));
        $pathToPieImg = storage_path('app/line_1_ch2222artvp.png');
        $qc->toFile($pathToPieImg);
        $drawing = new Drawing();
        $drawing->setName('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setDescription('Xu Hướng Tin Bài Theo Giờ');
        $drawing->setPath($pathToPieImg);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);


        $flat = collect($queryData)->flatten(1);
        $counts = [];
        foreach ($hl as $projectId => $projectName) {
            $projectItems = $flat->where('sub_brand_service_id', $projectId);
            $total = $projectItems->count();

            if ($total === 0) continue; // bỏ qua project không có bài

            $positive = $projectItems->where('state', 1)->count();
            $neutral = $projectItems->where('state', 0)->count();
            $negative = $projectItems->where('state', 2)->count();

            // Tính % cảm xúc
            $counts[] = [
                'project' => $projectName,
                'positive' => round($positive / $total * 100, 2),
                'neutral' => round($neutral / $total * 100, 2),
                'negative' => round($negative / $total * 100, 2),
            ];
        }
        $labels = array_column($counts, 'project');
        $positive = array_column($counts, 'positive');
        $neutral = array_column($counts, 'neutral');
        $negative = array_column($counts, 'negative');

        $datasets = [
            [
                'label' => 'Positive',
                'backgroundColor' => '#4CAF50',
                'data' => $positive,
            ],
            [
                'label' => 'Neutral',
                'backgroundColor' => '#FFC107',
                'data' => $neutral,
            ],
            [
                'label' => 'Negative',
                'backgroundColor' => '#F44336',
                'data' => $negative,
            ],
        ];

        $chartStackedBarConfig = [
            'type' => 'horizontalBar',
            'data' => [
                'labels' => $labels,  // mảng PHP
                'datasets' => $datasets,  // mảng PHP
            ],
            'options' => [
                'responsive' => true,
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'scales' => [
                    'xAxes' => [[
                        'stacked' => true,
                        'ticks' => [
                            'min' => 0,
                            'max' => 100,
                        ],
                        'scaleLabel' => [
                            'display' => true,
                            'labelString' => '%', // để chú thích là %
                        ]
                    ]],
                    'yAxes' => [[
                        'stacked' => true,
                    ]],
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Tỷ lệ cảm xúc',
                    'fontSize' => 16,
                    'fontColor' => '#000000',
                ],
                'plugins' => [
                    'datalabels' => [
                        'color' => '#333333',
                        'font' => [
                            'size' => 10,
                        ],
                        'formatter' => '%', // QuickChart cho phép hiển thị ký tự
                    ],
                ],
            ],
        ];


        // Xuất ra ảnh và chèn vào Excel
        $qc = new QuickChart([
            'width' => 800,
            'height' => 400
        ]);
        $qc->setApiKey('q-hrcceah0ie6s4bbn9no37w24h139su6k');
        $qc->setConfig(json_encode($chartStackedBarConfig));
        $imgPath = storage_path('app/emotional_rati22222ovp.png');
        $qc->toFile($imgPath);
        $drawing = new Drawing();
        $drawing->setName('Tỷ lệ cảm xúc');
        $drawing->setDescription('Tỷ lệ cảm xúc');
        $drawing->setPath($imgPath);
        $this->startRow = $this->startRow + 20;
        $drawing->setCoordinates('C' . $this->startRow);
        $drawing->setOffsetX(50);
        $drawing->setOffsetY(5);
        $drawing->setWidth(500);
        $drawing->setHeight(300);
        $drawing->setWorksheet($sheet);
    }

    protected function formatPlatform($dataQuery)
    {
        return [
            'facebook' => $dataQuery[0],
            'youtube' => $dataQuery[1],
            'tiktok' => $dataQuery[2],
            'zalo' => $dataQuery[3],
            'paper' => $dataQuery[4],
            'tv' => $dataQuery[5],
            'ifollow' => $dataQuery[6],
            'review_google' => $dataQuery[7],
        ];
    }
//Tvplus::class,
//Youtube::class,
//Tiktok::class,
//Zalo::class,
//Paper::class,
//Tv::class,
//Website::class,
//ReviewGoogleBusiness::class,


    private function top10ByPage(Collection $collection, int|string $subBrandId)
    {
        return $collection
            ->where('sub_brand_service_id', $subBrandId)
            ->groupBy('page_name')
            ->map(fn($items) => $items->count())
            ->sortDesc()
            ->take(10);
    }

    protected function style()
    {
        $styles = [
            'borders' => [
                'allBorders' => [ // Viết hoa camelCase
                    'style' => Border::BORDER_THIN
                ]
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'FFFFFF']
            ],
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleArrays = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleSmallText = [
            'font' => ['size' => 9]
        ];

        $styleRedColor = [
            'font' => ['color' => ['rgb' => 'FF0000']]
        ];

        $styleBlueColor = [
            'font' => ['color' => ['rgb' => '0070C0']]
        ];

        $styleLeftAlignment = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
        ];

        $styleRightAlignment = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT]
        ];

        $styleBoldText = [
            'font' => ['bold' => true]
        ];

        $styleAllBorders = [
            'borders' => [
                'allBorders' => ['style' => Border::BORDER_THIN]
            ]
        ];

        $styleItalicText = [
            'font' => ['italic' => true]
        ];

        $styleArray = [
            'borders' => [
                'allBorders' => ['style' => Border::BORDER_THIN]
            ],
            'font' => [
                'underline' => 'none',
                'color' => ['rgb' => '492CA0'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleText = [
            'borders' => [
                'allBorders' => ['style' => Border::BORDER_THIN]
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleWrapText = [
            'alignment' => [
                'wrapText' => true
            ]
        ];

        $styleCenterAlignment = [
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ]
        ];

        $styleLinkText = [
            'font' => [
                'color' => ['rgb' => '0000FF'],
                'size' => 11
            ]
        ];
        return [$styleBlueColor, $styleBoldText];
    }


    protected static function formatState($state)
    {
        $arr = [
            0 => 'Trung tính',
            1 => 'Tích cực',
            2 => 'Tiêu cực'
        ];
        return $arr[$state];
    }

    protected function endBenchmark($res)
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = $endTime - $this->benchmarkStartTime;
        $memoryUsed = $endMemory - $this->benchmarkStartMemory;
        $this->newLine();
        $this->line(sprintf(
            '⚡ <bg=bright-blue;fg=black> TIME: %s s </> <bg=bright-green;fg=black> MEM: %sMB </> <bg=bright-magenta;fg=black> ROWS: %s </>',
            round($executionTime, 2),
            round($memoryUsed / 1024 / 1024, 2),
            array_sum(array_map('count', $res))
        ));
        $this->newLine();
    }
}

