<?php

namespace App\Models\ES;


use Lib\Elasticsearch\Model;

class Twitter extends Model
{
    protected $index = 'twitter';

    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'point',
        'is_updated',
        'fb_id',
        'brand_id',
        'per_neutral',
        'status_6',
        'brand_service_id',
        'message',
        'is_delete',
        'total_like',
        'page_name',
        'parent_service_id',
        'type',
        'child_count',
        'sub_status',
        'own_type',
        'per_negative',
        'message_1',
        'message_0',
        'message_3',
        'message_2',
        'message_4',
        'mention_type',
        'total_share',
        'service_id',
        'content_id',
        'content_created',
        'per_positive',
        'sub_service_id',
        'sub_parent_service_id',
        'tag',
        'page_id',
        'content_from_id',
        'is_backup',
        'object_id',
        'is_duplicated',
        'state',
        'content_from_name',
        'concern_point',
        'total_page_like',
        'parent_object_id',
        'status_9',
        'status_8',
        'status_7',
        'sub_message',
        'status_5',
        'status_4',
        'status_3',
        'status_2',
        'status_1',
        'status_0',
        'created',
        'fb_parent_id',
        'sub_brand_service_id',
        'is_pin',
        'content_updated',

    ];
}
