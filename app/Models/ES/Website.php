<?php

namespace App\Models\ES;


use Lib\Elasticsearch\Model;

class Website extends Model
{
    protected $index = 'website';
    protected $type = 'filtered';

    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'web_brand_id',
        'web_object_id',
        'web_parent_object_id',
        'web_service_id',
        'web_brand_service_id',
        'web_parent_service_id',
        'web_sub_service_id',
        'web_sub_brand_service_id',
        'web_sub_parent_service_id',
        'web_point',
        'web_per_negative',
        'web_per_positive',
        'web_per_neutral',
        'web_message',
        'web_intro',
        'web_count_word',
        'web_price',
        'web_lead',
        'web_keyword',
        'web_content_created',
        'web_content_from_id',
        'web_content_from_name',
        'web_primary',
        'web_promote',
        'web_created',
        'web_state',
        'web_type',
        'web_tag',
        'web_child_count',
        'web_id',
        'web_parent_id',
        'web_url',
        'web_image',
        'web_mention_type',
        'web_page_id',
        'web_page_name',
        'web_channel',
        'web_category_name',
        'web_title',
        'web_category_url',
        'web_tag_keywords',
        'web_total_page_like',
        'web_total_like',
        'web_total_share',
        'web_own_type',
        'web_concern_point',
        'web_is_backup',
        'web_sub_status',
        'web_sub_message',
        'web_is_pin',
        'web_is_delete',
        'web_is_updated',
        'web_content_updated',
        'web_status_0',
        'web_status_1',
        'web_status_2',
        'web_status_3',
        'web_status_4',
        'web_message_0',
        'web_message_1',
        'web_message_2',
        'web_message_3',
        'web_message_4',
        'web_is_duplicated',

    ];
    protected $appends = ['content_created', 'sub_brand_service_id', 'object_id'];

    protected $casts = [
        'content_created' => 'datetime',
    ];
    public function getSubBrandServiceIdAttribute()
    {
        return $this->web_sub_brand_service_id;
    }
    public function getObjectIdAttribute()
    {
        return $this->web_object_id;
    }
    public function getContentCreatedAttribute()
    {
        return $this->web_content_created;
    }
}
