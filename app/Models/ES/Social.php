<?php

namespace App\Models\ES;


use Lib\Elasticsearch\Model;

class Social extends Model
{
    protected $index = 'social';

    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'topic_id',
        'topic_name',
        'topic_worker_id',
        'topic_worker_name',
        'content_created',
        'content_from_id',
        'content_from_name',
        'content_updated',
        'content_id',
        'content_message',
        'content_es_id',
        'content_page_id',
        'content_page_name',
        'content_status',
        'content_is_backup',
        'content_is_delete',
        'content_is_duplicated',
        'content_is_pin',
        'content_is_updated',
        'content_is_comment',
        'content_is_reply',
        'content_reply_created',
        'content_type',
        'content_parent_id',
        'status_0',
        'status_1',
        'status_2',
        'status_3',
        'status_4',
        'status_5',
        'status_6',
        'status_7',
        'status_8',
        'status_9',
        'message_0',
        'message_1',
        'message_2',
        'message_3',
        'message_4',
        'message_5',
        'message_6',
        'message_7',
        'message_8',
        'message_9',

    ];
}
