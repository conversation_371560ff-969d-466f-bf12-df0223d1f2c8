<?php

namespace App\Models\ES;


use Lib\Elasticsearch\Model;

class Tiktok extends Model
{
    protected $index = 'tiktok';
    protected $type = 'filtered';

    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'content_created',
        'per_positive',
        'point',
        'sub_service_id',
        'is_updated',
        'fb_id',
        'sub_parent_service_id',
        'brand_id',
        'tag',
        'object_id',
        'per_neutral',
        'page_id',
        'brand_service_id',
        'message',
        'sub_brand_service_id',
        'is_delete',
        'is_backup',
        'total_like',
        'is_duplicated',
        'child_count',
        'state',
        'page_name',
        'parent_service_id',
        'concern_point',
        'type',
        'content_from_name',
        'message_0',
        'total_page_like',
        'parent_object_id',
        'own_type',
        'per_negative',
        'sub_message',
        'status_9',
        'status_8',
        'status_7',
        'status_6',
        'status_5',
        'status_4',
        'status_3',
        'status_2',
        'status_1',
        'status_0',
        'message_1',
        'sub_status',
        'message_3',
        'message_2',
        'message_4',
        'created',
        'mention_type',
        'fb_parent_id',
        'content_from_id',
        'total_share',
        'service_id',
        'is_pin',
        'content_id',
        'content_updated',

    ];
    protected $casts = [
        'content_created' => 'datetime',
    ];
}

