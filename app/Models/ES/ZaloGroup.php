<?php

namespace App\Models\ES;


use Lib\Elasticsearch\Model;

class ZaloGroup extends Model
{
    protected $index = 'zalo-group';

    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'content_created',
        'status_6',
        'per_positive',
        'point',
        'sub_service_id',
        'is_updated',
        'fb_id',
        'sub_parent_service_id',
        'brand_id',
        'tag',
        'object_id',
        'per_neutral',
        'brand_service_id',
        'content_from_id',
        'message',
        'sub_brand_service_id',
        'is_delete',
        'is_backup',
        'total_like',
        'is_duplicated',
        'state',
        'page_name',
        'content_from_name',
        'concern_point',
        'type',
        'child_count',
        'sub_status',
        'total_page_like',
        'parent_object_id',
        'own_type',
        'page_id',
        'per_negative',
        'status_9',
        'status_8',
        'status_7',
        'sub_message',
        'status_5',
        'status_4',
        'status_3',
        'status_2',
        'status_1',
        'status_0',
        'message_1',
        'message_0',
        'message_3',
        'message_2',
        'message_4',
        'created',
        'mention_type',
        'fb_parent_id',
        'parent_service_id',
        'total_share',
        'es_id',
        'is_pin',
        'content_id',
        'content_updated',
        'service_id',
    ];
}


