<?php

namespace App\Models\ES;


class Tvplus extends \Lib\Elasticsearch\Model
{
    protected $index  = 'tvplus';
    protected $type = 'fb';
    protected $fillable = [
        '_index',
        '_id',
        '_score',
        'type',
        'parent_object_id',
        'page_name',
        'state',
        'tag',
        'own_type',
        'is_backup',
        'total_page_like',
        'created',
        'child_count',
        'mention_type',
        'object_id',
        'is_pin',
        'brand_id',
        'status_6',
        'status_5',
        'parent_service_id',
        'status_8',
        'status_7',
        'content_from_id',
        'total_share',
        'fb_id',
        'status_9',
        'concern_point',
        'status_0',
        'status_2',
        'is_duplicated',
        'status_1',
        'status_4',
        'status_3',
        'total_like',
        'per_positive',
        'content_updated',
        'brand_service_id',
        'point',
        'message_3',
        'page_id',
        'sub_service_id',
        'message_2',
        'message_1',
        'message_0',
        'is_updated',
        'content_from_name',
        'service_id',
        'message_4',
        'per_neutral',
        'content_created',
        'per_negative',
        'sub_status',
        'content_id',
        'sub_parent_service_id',
        'message',
        'is_delete',
        'sub_message',
        'fb_parent_id',
        'sub_brand_service_id',

    ];
    protected $casts = [
        'content_created' => 'datetime',
    ];
}

