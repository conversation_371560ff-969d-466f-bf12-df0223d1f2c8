<?php

namespace App\Models\ES;


use Lib\Elasticsearch\Model;

class Ecomerce extends Model
{
    protected $connection = 'elasticsearch';
    protected $index = 'ecomerce';

    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'content_created',
        'status_6',
        'per_positive',
        'point',
        'sub_service_id',
        'is_updated',
        'fb_id',
        'sub_parent_service_id',
        'message_8',
        'brand_id',
        'tag',
        'object_id',
        'per_neutral',
        'brand_service_id',
        'content_from_id',
        'message',
        'is_delete',
        'is_backup',
        'total_like',
        'is_duplicated',
        'child_count',
        'state',
        'page_name',
        'parent_service_id',
        'concern_point',
        'status_12',
        'status_11',
        'status_10',
        'type',
        'content_from_name',
        'rate_count',
        'message_10',
        'type_ecomerce',
        'star',
        'sub_status',
        'total_page_like',
        'parent_object_id',
        'own_type',
        'page_id',
        'per_negative',
        'status_9',
        'status_8',
        'status_7',
        'sub_message',
        'status_5',
        'status_4',
        'status_3',
        'status_2',
        'status_1',
        'status_0',
        'message_1',
        'message_0',
        'message_3',
        'message_2',
        'message_5',
        'message_4',
        'message_7',
        'created',
        'message_9',
        'mention_type',
        'message_6',
        'fb_parent_id',
        'sub_brand_service_id',
        'total_share',
        'sold_count',
        'service_id',
        'is_pin',
        'content_id',
        'content_updated',
    ];
}
