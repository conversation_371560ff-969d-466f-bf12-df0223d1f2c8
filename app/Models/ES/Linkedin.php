<?php

namespace App\Models\ES;


use Lib\Elasticsearch\Model;

class Linkedin extends Model
{
    protected $connection = 'elasticsearch';
    protected $index = 'linkedin';
    protected $fillable = [
        '_index',
        '_type',
        '_id',
        '_score',
        'content_created',
        'per_positive',
        'point',
        'sub_service_id',
        'is_updated',
        'fb_id',
        'sub_parent_service_id',
        'brand_id',
        'tag',
        'content_from_id',
        'object_id',
        'per_neutral',
        'status_6',
        'brand_service_id',
        'message',
        'is_delete',
        'is_backup',
        'total_like',
        'is_duplicated',
        'state',
        'page_name',
        'child_count',
        'concern_point',
        'content_from_name',
        'page_id',
        'parent_service_id',
        'total_share',
        'sub_status',
        'total_page_like',
        'parent_object_id',
        'own_type',
        'per_negative',
        'status_9',
        'status_8',
        'status_7',
        'sub_message',
        'status_5',
        'status_4',
        'status_3',
        'status_2',
        'status_1',
        'status_0',
        'message_1',
        'message_0',
        'message_3',
        'message_2',
        'message_4',
        'created',
        'url',
        'type',
        'fb_parent_id',
        'sub_brand_service_id',
        'mention_type',
        'service_id',
        'is_pin',
        'content_id',
        'content_updated',
    ];
}
