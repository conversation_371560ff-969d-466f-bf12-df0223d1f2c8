<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class PageContent extends Model
{
    protected $table = "pages_content";
    CONST STATUS_DONE = 2;
    protected $fillable = [
        'content_raw',
        'source',
        'content_ai',
        'image',
        'status',
        'comment',
        'link_fb',
        'reaction',
        'posted_at',
        'price',
        'comment_status',
        'like_status',
        'share_status',

        'like_total',
        'share_total',
        'comment_total',

        'comment_provider',
        'like_provider',
        'share_provider',
        'source_type',
        'point_ranking_content'
    ];

    protected $casts = [
        'reaction' => 'array',
//        'posted_at' => 'datetime'
    ];


}
