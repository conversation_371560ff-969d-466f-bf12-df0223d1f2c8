<?php

namespace Lib\Elasticsearch\Tests;

use PHPUnit\Framework\TestCase;
use Lib\Elasticsearch\Model;
use Lib\Elasticsearch\Client;

class WhenTestModel extends Model
{
    protected $index = 'when_test_models';
    protected $type = 'when_test_model';
    
    protected $fillable = [
        'name',
        'status',
        'age',
        'category',
    ];
}

class WhenMethodTest extends TestCase
{
    protected $model;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->model = new WhenTestModel();
        
        // Set up a mock client for testing
        $client = new Client([
            'hosts' => ['http://localhost:9200'],
            'timeout' => 30,
        ]);
        
        WhenTestModel::setClient($client);
    }

    public function test_when_method_with_truthy_value()
    {
        $searchTerm = 'test';
        
        $query = WhenTestModel::query()
            ->when($searchTerm, function ($query, $term) {
                return $query->match('name', $term);
            })
            ->toQuery();

        // Should include the match query
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertEquals('test', $query['query']['bool']['must'][0]['match']['name']);
    }

    public function test_when_method_with_falsy_value()
    {
        $searchTerm = null;
        
        $query = WhenTestModel::query()
            ->when($searchTerm, function ($query, $term) {
                return $query->match('name', $term);
            })
            ->toQuery();

        // Should not include the match query, only match_all
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('match_all', $query['query']);
    }

    public function test_when_method_with_default_callback()
    {
        $userRole = 'user';
        
        $query = WhenTestModel::query()
            ->when($userRole === 'admin', 
                function ($query) {
                    return $query->where('status', 'any');
                },
                function ($query) {
                    return $query->where('status', 'published');
                }
            )
            ->toQuery();

        // Should apply the default callback
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertEquals('published', $query['query']['bool']['must'][0]['term']['status']);
    }

    public function test_unless_method_with_falsy_value()
    {
        $includeInactive = false;
        
        $query = WhenTestModel::query()
            ->unless($includeInactive, function ($query) {
                return $query->where('status', 'active');
            })
            ->toQuery();

        // Should include the where clause since includeInactive is false
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertEquals('active', $query['query']['bool']['must'][0]['term']['status']);
    }

    public function test_unless_method_with_truthy_value()
    {
        $includeInactive = true;
        
        $query = WhenTestModel::query()
            ->unless($includeInactive, function ($query) {
                return $query->where('status', 'active');
            })
            ->toQuery();

        // Should not include the where clause since includeInactive is true
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('match_all', $query['query']);
    }

    public function test_chained_when_methods()
    {
        $searchTerm = 'test';
        $status = 'published';
        $category = null;
        
        $query = WhenTestModel::query()
            ->when($searchTerm, function ($query, $term) {
                return $query->match('name', $term);
            })
            ->when($status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->when($category, function ($query, $category) {
                return $query->where('category', $category);
            })
            ->toQuery();

        // Should include search and status, but not category
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        
        $mustClauses = $query['query']['bool']['must'];
        $this->assertCount(2, $mustClauses); // Only search and status
        
        // Check for match clause
        $hasMatch = false;
        $hasTerm = false;
        foreach ($mustClauses as $clause) {
            if (isset($clause['match']['name'])) {
                $hasMatch = true;
                $this->assertEquals('test', $clause['match']['name']);
            }
            if (isset($clause['term']['status'])) {
                $hasTerm = true;
                $this->assertEquals('published', $clause['term']['status']);
            }
        }
        
        $this->assertTrue($hasMatch, 'Should have match clause');
        $this->assertTrue($hasTerm, 'Should have term clause');
    }

    public function test_when_method_returns_query_builder()
    {
        $result = WhenTestModel::query()
            ->when(true, function ($query) {
                return $query->where('status', 'active');
            });

        $this->assertInstanceOf(\Lib\Elasticsearch\Builder::class, $result);
    }

    public function test_when_method_with_complex_conditions()
    {
        $filters = [
            'search' => 'test',
            'age_min' => 18,
            'age_max' => 65,
            'status' => 'active'
        ];
        
        $query = WhenTestModel::query()
            ->when($filters['search'] ?? null, function ($query, $term) {
                return $query->search($term, ['name']);
            })
            ->when($filters['age_min'] ?? null, function ($query, $min) {
                return $query->where('age', '>=', $min);
            })
            ->when($filters['age_max'] ?? null, function ($query, $max) {
                return $query->where('age', '<=', $max);
            })
            ->when($filters['status'] ?? null, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->toQuery();

        // Should include all conditions
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        
        $mustClauses = $query['query']['bool']['must'];
        $this->assertCount(4, $mustClauses); // All four conditions
    }

    public function test_static_when_method()
    {
        $searchTerm = 'test';
        
        $query = WhenTestModel::when($searchTerm, function ($query, $term) {
            return $query->match('name', $term);
        })->toQuery();

        // Should work the same as instance method
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertEquals('test', $query['query']['bool']['must'][0]['match']['name']);
    }

    public function test_static_unless_method()
    {
        $includeInactive = false;
        
        $query = WhenTestModel::unless($includeInactive, function ($query) {
            return $query->where('status', 'active');
        })->toQuery();

        // Should include the where clause
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertEquals('active', $query['query']['bool']['must'][0]['term']['status']);
    }
}
