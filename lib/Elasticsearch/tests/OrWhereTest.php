<?php

namespace Lib\Elasticsearch\Tests;

use PHPUnit\Framework\TestCase;
use Lib\Elasticsearch\Model;
use Lib\Elasticsearch\Client;

class OrWhereTestModel extends Model
{
    protected $index = 'or_where_test_models';
    protected $type = 'or_where_test_model';
    
    protected $fillable = [
        'name',
        'status',
        'age',
        'category',
        'location',
    ];
}

class OrWhereTest extends TestCase
{
    protected $model;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->model = new OrWhereTestModel();
        
        // Set up a mock client for testing
        $client = new Client([
            'hosts' => ['http://localhost:9200'],
            'timeout' => 30,
        ]);
        
        OrWhereTestModel::setClient($client);
    }

    public function test_or_where_basic()
    {
        $query = OrWhereTestModel::query()
            ->where('status', 'active')
            ->orWhere('status', 'pending')
            ->toQuery();

        // Should have both must and should clauses
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertArrayHasKey('should', $query['query']['bool']);
        
        // Check must clause
        $this->assertEquals('active', $query['query']['bool']['must'][0]['term']['status']);
        
        // Check should clause
        $this->assertEquals('pending', $query['query']['bool']['should'][0]['term']['status']);
    }

    public function test_or_where_only_should_clauses()
    {
        $query = OrWhereTestModel::query()
            ->orWhere('status', 'active')
            ->orWhere('status', 'pending')
            ->toQuery();

        // Should have only should clauses with minimum_should_match
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('should', $query['query']['bool']);
        $this->assertArrayHasKey('minimum_should_match', $query['query']['bool']);
        $this->assertEquals(1, $query['query']['bool']['minimum_should_match']);
        
        // Should have two should clauses
        $this->assertCount(2, $query['query']['bool']['should']);
        $this->assertEquals('active', $query['query']['bool']['should'][0]['term']['status']);
        $this->assertEquals('pending', $query['query']['bool']['should'][1]['term']['status']);
    }

    public function test_or_where_with_different_operators()
    {
        $query = OrWhereTestModel::query()
            ->where('age', '>', 18)
            ->orWhere('age', '<', 65)
            ->toQuery();

        // Should have must and should with range queries
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        
        // Check must clause (age > 18)
        $this->assertEquals(18, $query['query']['bool']['must'][0]['range']['age']['gt']);
        
        // Check should clause (age < 65)
        $this->assertEquals(65, $query['query']['bool']['should'][0]['range']['age']['lt']);
    }

    public function test_or_where_in()
    {
        $query = OrWhereTestModel::query()
            ->where('status', 'active')
            ->orWhereIn('category', ['tech', 'sports', 'music'])
            ->toQuery();

        // Should have must and should clauses
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertArrayHasKey('should', $query['query']['bool']);
        
        // Check should clause for terms
        $this->assertEquals(['tech', 'sports', 'music'], $query['query']['bool']['should'][0]['terms']['category']);
    }

    public function test_or_where_between()
    {
        $query = OrWhereTestModel::query()
            ->where('status', 'active')
            ->orWhereBetween('age', [18, 25])
            ->toQuery();

        // Should have must and should clauses
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        
        // Check should clause for range
        $shouldClause = $query['query']['bool']['should'][0]['range']['age'];
        $this->assertEquals(18, $shouldClause['gte']);
        $this->assertEquals(25, $shouldClause['lte']);
    }

    public function test_or_where_null()
    {
        $query = OrWhereTestModel::query()
            ->where('status', 'active')
            ->orWhereNull('email')
            ->toQuery();

        // Should have must and should clauses
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        
        // Check should clause for null (must_not exists)
        $shouldClause = $query['query']['bool']['should'][0];
        $this->assertArrayHasKey('bool', $shouldClause);
        $this->assertArrayHasKey('must_not', $shouldClause['bool']);
        $this->assertEquals('email', $shouldClause['bool']['must_not'][0]['exists']['field']);
    }

    public function test_or_where_not_null()
    {
        $query = OrWhereTestModel::query()
            ->where('status', 'active')
            ->orWhereNotNull('phone')
            ->toQuery();

        // Should have must and should clauses
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        
        // Check should clause for not null (exists)
        $shouldClause = $query['query']['bool']['should'][0];
        $this->assertArrayHasKey('exists', $shouldClause);
        $this->assertEquals('phone', $shouldClause['exists']['field']);
    }

    public function test_multiple_or_where_clauses()
    {
        $query = OrWhereTestModel::query()
            ->where('status', 'active')
            ->orWhere('category', 'tech')
            ->orWhere('location', 'New York')
            ->orWhereIn('age', [25, 30, 35])
            ->toQuery();

        // Should have must and multiple should clauses
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertArrayHasKey('should', $query['query']['bool']);
        
        // Should have 3 should clauses
        $this->assertCount(3, $query['query']['bool']['should']);
        
        // Check each should clause
        $this->assertEquals('tech', $query['query']['bool']['should'][0]['term']['category']);
        $this->assertEquals('New York', $query['query']['bool']['should'][1]['term']['location']);
        $this->assertEquals([25, 30, 35], $query['query']['bool']['should'][2]['terms']['age']);
    }

    public function test_or_where_with_array_syntax()
    {
        $query = OrWhereTestModel::query()
            ->where('status', 'active')
            ->orWhere([
                'category' => 'tech',
                'location' => 'New York'
            ])
            ->toQuery();

        // Should have must and should clauses
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        
        // Should have 2 should clauses from the array
        $this->assertCount(2, $query['query']['bool']['should']);
    }

    public function test_static_or_where_methods()
    {
        $query = OrWhereTestModel::where('status', 'active')
            ->orWhere('category', 'tech')
            ->toQuery();

        // Should work the same as instance methods
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertArrayHasKey('must', $query['query']['bool']);
        $this->assertArrayHasKey('should', $query['query']['bool']);
        
        $this->assertEquals('active', $query['query']['bool']['must'][0]['term']['status']);
        $this->assertEquals('tech', $query['query']['bool']['should'][0]['term']['category']);
    }

    public function test_or_where_returns_query_builder()
    {
        $result = OrWhereTestModel::query()
            ->where('status', 'active')
            ->orWhere('category', 'tech');

        $this->assertInstanceOf(\Lib\Elasticsearch\Builder::class, $result);
    }

    public function test_complex_or_where_combination()
    {
        $query = OrWhereTestModel::query()
            ->where('status', 'active')
            ->where('verified', true)
            ->orWhere('category', 'premium')
            ->orWhereBetween('age', [25, 35])
            ->orWhereIn('location', ['New York', 'Los Angeles'])
            ->toQuery();

        // Should have 2 must clauses and 3 should clauses
        $this->assertArrayHasKey('query', $query);
        $this->assertArrayHasKey('bool', $query['query']);
        $this->assertCount(2, $query['query']['bool']['must']);
        $this->assertCount(3, $query['query']['bool']['should']);
        
        // Verify must clauses
        $this->assertEquals('active', $query['query']['bool']['must'][0]['term']['status']);
        $this->assertEquals(true, $query['query']['bool']['must'][1]['term']['verified']);
        
        // Verify should clauses
        $this->assertEquals('premium', $query['query']['bool']['should'][0]['term']['category']);
        $this->assertArrayHasKey('range', $query['query']['bool']['should'][1]);
        $this->assertArrayHasKey('terms', $query['query']['bool']['should'][2]);
    }
}
