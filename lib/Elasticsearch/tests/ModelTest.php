<?php

namespace Lib\Elasticsearch\Tests;

use PHPUnit\Framework\TestCase;
use Lib\Elasticsearch\Model;
use Lib\Elasticsearch\Client;

class TestModel extends Model
{
    protected $index = 'test_models';
    protected $type = 'test_model';
    
    protected $fillable = [
        'name',
        'email',
        'age',
    ];
    
    protected $casts = [
        'age' => 'integer',
    ];
}

class ModelTest extends TestCase
{
    protected $model;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->model = new TestModel();
        
        // Set up a mock client for testing
        $client = new Client([
            'hosts' => ['http://localhost:9200'],
            'timeout' => 30,
        ]);
        
        TestModel::setClient($client);
    }

    public function test_model_can_be_instantiated()
    {
        $this->assertInstanceOf(TestModel::class, $this->model);
    }

    public function test_model_has_correct_index_and_type()
    {
        $this->assertEquals('test_models', $this->model->getIndex());
        $this->assertEquals('test_model', $this->model->getType());
    }

    public function test_model_can_fill_attributes()
    {
        $attributes = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'age' => 25,
        ];
        
        $this->model->fill($attributes);
        
        $this->assertEquals('John Doe', $this->model->name);
        $this->assertEquals('<EMAIL>', $this->model->email);
        $this->assertEquals(25, $this->model->age);
    }

    public function test_model_casts_attributes()
    {
        $this->model->age = '30';
        $this->assertSame(30, $this->model->age);
    }

    public function test_model_can_convert_to_array()
    {
        $attributes = [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'age' => 28,
        ];
        
        $this->model->fill($attributes);
        $array = $this->model->toArray();
        
        $this->assertEquals($attributes, $array);
    }

    public function test_model_can_convert_to_json()
    {
        $attributes = [
            'name' => 'Bob Smith',
            'email' => '<EMAIL>',
            'age' => 35,
        ];
        
        $this->model->fill($attributes);
        $json = $this->model->toJson();
        
        $this->assertJson($json);
        $this->assertEquals($attributes, json_decode($json, true));
    }

    public function test_model_can_create_query_builder()
    {
        $builder = $this->model->newQuery();
        $this->assertInstanceOf(\Lib\Elasticsearch\Builder::class, $builder);
    }

    public function test_model_static_query_method()
    {
        $builder = TestModel::query();
        $this->assertInstanceOf(\Lib\Elasticsearch\Builder::class, $builder);
    }

    public function test_model_fillable_attributes()
    {
        $this->assertTrue($this->model->isFillable('name'));
        $this->assertTrue($this->model->isFillable('email'));
        $this->assertTrue($this->model->isFillable('age'));
        $this->assertFalse($this->model->isFillable('_id'));
    }

    public function test_model_primary_key()
    {
        $this->assertEquals('_id', $this->model->getKeyName());
        
        $this->model->setId('test_id_123');
        $this->assertEquals('test_id_123', $this->model->getKey());
    }

    public function test_model_score_and_highlight()
    {
        $this->model->setScore(0.85);
        $this->assertEquals(0.85, $this->model->getScore());
        
        $highlight = ['title' => ['highlighted text']];
        $this->model->setHighlight($highlight);
        $this->assertEquals($highlight, $this->model->getHighlight());
    }
}
