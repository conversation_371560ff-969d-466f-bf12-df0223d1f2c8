<?php

namespace Lib\Elasticsearch\Tests;

use PHPUnit\Framework\TestCase;
use Lib\Elasticsearch\Client;

class ClientTest extends TestCase
{
    protected $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->client = new Client([
            'hosts' => ['http://localhost:9200'],
            'timeout' => 30,
        ]);
    }

    public function test_client_can_be_instantiated()
    {
        $this->assertInstanceOf(Client::class, $this->client);
    }

    public function test_client_can_get_cluster_info()
    {
        try {
            $info = $this->client->info();
            $this->assertIsArray($info);
            $this->assertArrayHasKey('cluster_name', $info);
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }

    public function test_client_can_check_index_exists()
    {
        try {
            $exists = $this->client->indexExists('non_existent_index_' . time());
            $this->assertFalse($exists);
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }

    public function test_client_can_create_and_delete_index()
    {
        $indexName = 'test_index_' . time();
        
        try {
            // Create index
            $response = $this->client->createIndex($indexName, [
                'settings' => [
                    'number_of_shards' => 1,
                    'number_of_replicas' => 0,
                ]
            ]);
            
            $this->assertArrayHasKey('acknowledged', $response);
            $this->assertTrue($response['acknowledged']);
            
            // Check it exists
            $this->assertTrue($this->client->indexExists($indexName));
            
            // Delete index
            $response = $this->client->deleteIndex($indexName);
            $this->assertArrayHasKey('acknowledged', $response);
            $this->assertTrue($response['acknowledged']);
            
            // Check it's gone
            $this->assertFalse($this->client->indexExists($indexName));
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }
}
