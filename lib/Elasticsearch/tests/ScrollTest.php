<?php

namespace Lib\Elasticsearch\Tests;

use PHPUnit\Framework\TestCase;
use Lib\Elasticsearch\Model;
use Lib\Elasticsearch\Client;

class ScrollTestModel extends Model
{
    protected $index = 'scroll_test_models';
    protected $type = 'scroll_test_model';
    
    protected $fillable = [
        'name',
        'value',
        'category',
    ];
}

class ScrollTest extends TestCase
{
    protected $client;
    protected $indexName = 'scroll_test_models';

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->client = new Client([
            'hosts' => ['http://localhost:9200'],
            'timeout' => 30,
        ]);
        
        ScrollTestModel::setClient($this->client);
    }

    protected function tearDown(): void
    {
        // Clean up test index
        try {
            if ($this->client->indexExists($this->indexName)) {
                $this->client->deleteIndex($this->indexName);
            }
        } catch (\Exception $e) {
            // Ignore cleanup errors
        }
        
        parent::tearDown();
    }

    public function test_scroll_functionality()
    {
        try {
            // Create test index
            $this->client->createIndex($this->indexName, [
                'settings' => [
                    'number_of_shards' => 1,
                    'number_of_replicas' => 0,
                ]
            ]);

            // Index test documents
            $testData = [];
            for ($i = 1; $i <= 50; $i++) {
                $testData[] = [
                    'name' => "Test Item {$i}",
                    'value' => $i,
                    'category' => $i % 3 === 0 ? 'A' : ($i % 2 === 0 ? 'B' : 'C')
                ];
            }

            // Bulk index the test data
            $bulkBody = [];
            foreach ($testData as $i => $data) {
                $bulkBody[] = [
                    'index' => [
                        '_index' => $this->indexName,
                        '_type' => 'scroll_test_model',
                        '_id' => $i + 1
                    ]
                ];
                $bulkBody[] = $data;
            }

            $this->client->bulk($bulkBody);

            // Wait for indexing to complete
            sleep(1);

            // Test scroll functionality
            $results = ScrollTestModel::scroll(10); // 10 items per scroll batch
            
            $this->assertGreaterThan(0, $results->count());
            $this->assertEquals(50, $results->total());
            
            // Verify all items are retrieved
            $retrievedIds = [];
            foreach ($results as $item) {
                $retrievedIds[] = $item->getKey();
            }
            
            $this->assertCount(50, $retrievedIds);
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }

    public function test_scroll_chunk_functionality()
    {
        try {
            // Create test index if it doesn't exist
            if (!$this->client->indexExists($this->indexName)) {
                $this->client->createIndex($this->indexName, [
                    'settings' => [
                        'number_of_shards' => 1,
                        'number_of_replicas' => 0,
                    ]
                ]);

                // Index test documents
                $bulkBody = [];
                for ($i = 1; $i <= 25; $i++) {
                    $bulkBody[] = [
                        'index' => [
                            '_index' => $this->indexName,
                            '_type' => 'scroll_test_model',
                            '_id' => $i
                        ]
                    ];
                    $bulkBody[] = [
                        'name' => "Chunk Test Item {$i}",
                        'value' => $i,
                        'category' => 'test'
                    ];
                }

                $this->client->bulk($bulkBody);
                sleep(1); // Wait for indexing
            }

            $processedCount = 0;
            $pageCount = 0;

            $totalProcessed = ScrollTestModel::scrollChunk(10, function ($items, $page) use (&$processedCount, &$pageCount) {
                $processedCount += $items->count();
                $pageCount++;
                
                $this->assertGreaterThan(0, $items->count());
                $this->assertLessThanOrEqual(10, $items->count());
                
                return true; // Continue processing
            });

            $this->assertGreaterThan(0, $processedCount);
            $this->assertGreaterThan(0, $pageCount);
            $this->assertEquals($processedCount, $totalProcessed);
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }

    public function test_chunk_functionality()
    {
        try {
            $processedCount = 0;
            $pageCount = 0;

            $totalProcessed = ScrollTestModel::chunk(5, function ($items, $page) use (&$processedCount, &$pageCount) {
                $processedCount += $items->count();
                $pageCount++;
                
                $this->assertGreaterThan(0, $items->count());
                $this->assertLessThanOrEqual(5, $items->count());
                
                // Stop after 3 pages for testing
                if ($page >= 3) {
                    return false;
                }
                
                return true;
            });

            $this->assertGreaterThan(0, $processedCount);
            $this->assertEquals(3, $pageCount);
            $this->assertEquals($processedCount, $totalProcessed);
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }

    public function test_all_method_chooses_appropriate_strategy()
    {
        try {
            // Test with small dataset (should use regular query)
            $results = ScrollTestModel::limit(5)->all();
            $this->assertInstanceOf(\Lib\Elasticsearch\Collection::class, $results);
            
            // Test with larger scroll size
            $results = ScrollTestModel::all(1000);
            $this->assertInstanceOf(\Lib\Elasticsearch\Collection::class, $results);
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }

    public function test_scroll_with_query_conditions()
    {
        try {
            // Test scroll with where conditions
            $results = ScrollTestModel::where('category', 'A')->scroll(5);
            
            $this->assertInstanceOf(\Lib\Elasticsearch\Collection::class, $results);
            
            // Verify all results match the condition
            foreach ($results as $item) {
                $this->assertEquals('A', $item->category);
            }
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }

    public function test_scroll_early_termination()
    {
        try {
            $processedPages = 0;
            
            ScrollTestModel::scrollChunk(5, function ($items, $page) use (&$processedPages) {
                $processedPages++;
                
                // Stop after 2 pages
                if ($page >= 2) {
                    return false;
                }
                
                return true;
            });
            
            $this->assertEquals(2, $processedPages);
            
        } catch (\Exception $e) {
            $this->markTestSkipped('Elasticsearch not available: ' . $e->getMessage());
        }
    }
}
