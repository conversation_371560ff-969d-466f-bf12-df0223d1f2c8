<?php

namespace Lib\Elasticsearch\Commands;

use Illuminate\Console\Command;
use <PERSON>b\Elasticsearch\Client;

class ElasticsearchCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'elasticsearch:manage 
                            {action : The action to perform (info|create-index|delete-index|list-indices)}
                            {--index= : The index name (required for create-index and delete-index)}
                            {--type= : The document type (optional)}
                            {--settings= : JSON settings for index creation}
                            {--mappings= : JSON mappings for index creation}';

    /**
     * The console command description.
     */
    protected $description = 'Manage Elasticsearch indices and operations';

    /**
     * The Elasticsearch client.
     */
    protected $client;

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
        $this->client = app(Client::class);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'info':
                $this->showClusterInfo();
                break;
            case 'create-index':
                $this->createIndex();
                break;
            case 'delete-index':
                $this->deleteIndex();
                break;
            case 'list-indices':
                $this->listIndices();
                break;
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: info, create-index, delete-index, list-indices');
                return 1;
        }

        return 0;
    }

    /**
     * Show cluster information.
     */
    protected function showClusterInfo()
    {
        try {
            $info = $this->client->info();
            
            $this->info('Elasticsearch Cluster Information:');
            $this->line('');
            $this->line("Cluster Name: {$info['cluster_name']}");
            $this->line("Node Name: {$info['name']}");
            $this->line("Version: {$info['version']['number']}");
            $this->line("Lucene Version: {$info['version']['lucene_version']}");
            $this->line("Status: Connected");
            
        } catch (\Exception $e) {
            $this->error('Failed to connect to Elasticsearch: ' . $e->getMessage());
        }
    }

    /**
     * Create an index.
     */
    protected function createIndex()
    {
        $index = $this->option('index');
        
        if (!$index) {
            $this->error('Index name is required. Use --index=your_index_name');
            return;
        }

        try {
            // Check if index already exists
            if ($this->client->indexExists($index)) {
                $this->error("Index '{$index}' already exists.");
                return;
            }

            $body = [];

            // Add settings if provided
            if ($settings = $this->option('settings')) {
                $body['settings'] = json_decode($settings, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error('Invalid JSON in settings option');
                    return;
                }
            }

            // Add mappings if provided
            if ($mappings = $this->option('mappings')) {
                $body['mappings'] = json_decode($mappings, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error('Invalid JSON in mappings option');
                    return;
                }
            }

            // Create default settings for ES 2.4.6 if none provided
            if (empty($body['settings'])) {
                $body['settings'] = [
                    'number_of_shards' => 1,
                    'number_of_replicas' => 0,
                ];
            }

            $response = $this->client->createIndex($index, $body);
            
            if (isset($response['acknowledged']) && $response['acknowledged']) {
                $this->info("Index '{$index}' created successfully.");
            } else {
                $this->error("Failed to create index '{$index}'.");
            }
            
        } catch (\Exception $e) {
            $this->error('Failed to create index: ' . $e->getMessage());
        }
    }

    /**
     * Delete an index.
     */
    protected function deleteIndex()
    {
        $index = $this->option('index');
        
        if (!$index) {
            $this->error('Index name is required. Use --index=your_index_name');
            return;
        }

        try {
            // Check if index exists
            if (!$this->client->indexExists($index)) {
                $this->error("Index '{$index}' does not exist.");
                return;
            }

            if ($this->confirm("Are you sure you want to delete index '{$index}'? This action cannot be undone.")) {
                $response = $this->client->deleteIndex($index);
                
                if (isset($response['acknowledged']) && $response['acknowledged']) {
                    $this->info("Index '{$index}' deleted successfully.");
                } else {
                    $this->error("Failed to delete index '{$index}'.");
                }
            } else {
                $this->info('Operation cancelled.');
            }
            
        } catch (\Exception $e) {
            $this->error('Failed to delete index: ' . $e->getMessage());
        }
    }

    /**
     * List all indices.
     */
    protected function listIndices()
    {
        try {
            // For ES 2.4.6, we need to use the _cat API
            $response = $this->client->request('GET', '/_cat/indices?v&format=json');
            
            if (empty($response)) {
                $this->info('No indices found.');
                return;
            }

            $this->info('Elasticsearch Indices:');
            $this->line('');
            
            $headers = ['Index', 'Health', 'Status', 'Docs Count', 'Store Size'];
            $rows = [];
            
            foreach ($response as $index) {
                $rows[] = [
                    $index['index'] ?? 'N/A',
                    $index['health'] ?? 'N/A',
                    $index['status'] ?? 'N/A',
                    $index['docs.count'] ?? 'N/A',
                    $index['store.size'] ?? 'N/A',
                ];
            }
            
            $this->table($headers, $rows);
            
        } catch (\Exception $e) {
            $this->error('Failed to list indices: ' . $e->getMessage());
        }
    }
}

/**
 * Usage examples:
 * 
 * # Show cluster info
 * php artisan elasticsearch:manage info
 * 
 * # Create an index
 * php artisan elasticsearch:manage create-index --index=my_index
 * 
 * # Create an index with settings
 * php artisan elasticsearch:manage create-index --index=my_index --settings='{"number_of_shards":2,"number_of_replicas":1}'
 * 
 * # Create an index with mappings
 * php artisan elasticsearch:manage create-index --index=articles --mappings='{"article":{"properties":{"title":{"type":"string","analyzer":"standard"},"content":{"type":"string"}}}}'
 * 
 * # Delete an index
 * php artisan elasticsearch:manage delete-index --index=my_index
 * 
 * # List all indices
 * php artisan elasticsearch:manage list-indices
 */
