<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Elasticsearch Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for Elasticsearch 2.4.6
    | compatible client. You can configure multiple connections and set
    | various options for each connection.
    |
    */

    'default' => env('ELASTICSEARCH_CONNECTION', 'default'),

    'connections' => [
        'default' => [
            'hosts' => explode(',', env('ES_HOSTS', 'http://localhost:9200')),
            'username' => env('ELASTICSEARCH_USERNAME',''),
            'password' => env('ELASTICSEARCH_PASSWORD',''),
            'timeout' => env('ELASTICSEARCH_TIMEOUT', 30),
            'retries' => env('ELASTICSEARCH_RETRIES', 3),
            'ssl_verification' => env('ELASTICSEARCH_SSL_VERIFICATION', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Index Settings
    |--------------------------------------------------------------------------
    |
    | Default settings for creating new indices
    |
    */
    'index_settings' => [
        'number_of_shards' => env('ELASTICSEARCH_SHARDS', 1),
        'number_of_replicas' => env('ELASTICSEARCH_REPLICAS', 0),
        'analysis' => [
            'analyzer' => [
                'default' => [
                    'type' => 'standard',
                    'stopwords' => '_english_'
                ]
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Enable or disable logging of Elasticsearch queries
    |
    */
    'logging' => [
        'enabled' => env('ELASTICSEARCH_LOGGING', false),
        'level' => env('ELASTICSEARCH_LOG_LEVEL', 'info'),
        'channel' => env('ELASTICSEARCH_LOG_CHANNEL', 'default'),
    ],
];
