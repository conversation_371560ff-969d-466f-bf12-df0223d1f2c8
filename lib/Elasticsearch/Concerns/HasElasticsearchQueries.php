<?php

namespace Lib\Elasticsearch\Concerns;

use Lib\Elasticsearch\Builder;

trait HasElasticsearchQueries
{
    /**
     * Find a model by its primary key.
     */
    public static function find($id)
    {
        $instance = new static;
        $client = static::getClient();

        try {
            $response = $client->get(
                $instance->getIndex(),
                $instance->getType(),
                $id
            );

            if (isset($response['_source'])) {
                $model = new static($response['_source']);
                $model->setId($response['_id']);
                $model->exists = true;
                return $model;
            }
        } catch (\Exception $e) {
            // Document not found
        }

        return null;
    }

    /**
     * Find a model by its primary key or throw an exception.
     */
    public static function findOrFail($id)
    {
        $result = static::find($id);

        if (is_null($result)) {
            throw new \Exception("No query results for model [" . static::class . "] {$id}");
        }

        return $result;
    }

    /**
     * Find multiple models by their primary keys.
     */
    public static function findMany($ids)
    {
        $instance = new static;
        return static::query()->whereIn($instance->getKey<PERSON>ame(), $ids)->get();
    }

    /**
     * Get all of the models from the database.
     */
    public static function all($columns = ['*'])
    {
        return static::query()->get();
    }

    /**
     * Create a new model instance and save it to the database.
     */
    public static function create(array $attributes = [])
    {
        $model = new static($attributes);
        $model->save();
        return $model;
    }

    /**
     * Update or create a model matching the attributes.
     */
    public static function updateOrCreate(array $attributes, array $values = [])
    {
        $instance = static::query()->where($attributes)->first();

        if ($instance) {
            $instance->fill($values);
            $instance->save();
            return $instance;
        }

        return static::create(array_merge($attributes, $values));
    }

    /**
     * Create or update a record matching the attributes, and fill it with values.
     */
    public static function firstOrCreate(array $attributes, array $values = [])
    {
        $instance = static::query()->where($attributes)->first();

        if ($instance) {
            return $instance;
        }

        return static::create(array_merge($attributes, $values));
    }

    /**
     * Get the first record matching the attributes or instantiate it.
     */
    public static function firstOrNew(array $attributes, array $values = [])
    {
        $instance = static::query()->where($attributes)->first();

        if ($instance) {
            return $instance;
        }

        return new static(array_merge($attributes, $values));
    }

    /**
     * Execute a query for a single record by ID.
     */
    public static function where($field, $operator = null, $value = null)
    {
        return static::query()->where($field, $operator, $value);
    }

    /**
     * Add a basic where clause to the query.
     */
    public static function whereIn($field, $values)
    {
        return static::query()->whereIn($field, $values);
    }

    /**
     * Add a "where not in" clause to the query.
     */
    public static function whereNotIn($field, $values)
    {
        return static::query()->whereNotIn($field, $values);
    }

    /**
     * Add a where between statement to the query.
     */
    public static function whereBetween($field, array $values)
    {
        return static::query()->whereBetween($field, $values);
    }

    /**
     * Add a where not between statement to the query.
     */
    public static function whereNotBetween($field, array $values)
    {
        return static::query()->whereNotBetween($field, $values);
    }

    /**
     * Add a "where null" clause to the query.
     */
    public static function whereNull($field)
    {
        return static::query()->whereNull($field);
    }

    /**
     * Add a "where not null" clause to the query.
     */
    public static function whereNotNull($field)
    {
        return static::query()->whereNotNull($field);
    }

    /**
     * Add an "or where" clause to the query.
     */
    public static function orWhere($field, $operator = null, $value = null)
    {
        return static::query()->orWhere($field, $operator, $value);
    }

    /**
     * Add an "or where in" clause to the query.
     */
    public static function orWhereIn($field, $values)
    {
        return static::query()->orWhereIn($field, $values);
    }

    /**
     * Add an "or where not in" clause to the query.
     */
    public static function orWhereNotIn($field, $values)
    {
        return static::query()->orWhereNotIn($field, $values);
    }

    /**
     * Add an "or where between" clause to the query.
     */
    public static function orWhereBetween($field, array $values)
    {
        return static::query()->orWhereBetween($field, $values);
    }

    /**
     * Add an "or where not between" clause to the query.
     */
    public static function orWhereNotBetween($field, array $values)
    {
        return static::query()->orWhereNotBetween($field, $values);
    }

    /**
     * Add an "or where null" clause to the query.
     */
    public static function orWhereNull($field)
    {
        return static::query()->orWhereNull($field);
    }

    /**
     * Add an "or where not null" clause to the query.
     */
    public static function orWhereNotNull($field)
    {
        return static::query()->orWhereNotNull($field);
    }

    /**
     * Add a full-text search to the query.
     */
    public static function search($query, $fields = null)
    {
        return static::query()->search($query, $fields);
    }

    /**
     * Add a match query to the query.
     */
    public static function match($field, $query)
    {
        return static::query()->match($field, $query);
    }

    /**
     * Add an "order by" clause to the query.
     */
    public static function orderBy($field, $direction = 'asc')
    {
        return static::query()->orderBy($field, $direction);
    }

    /**
     * Set the "limit" value of the query.
     */
    public static function limit($value)
    {
        return static::query()->limit($value);
    }

    /**
     * Alias to set the "limit" value of the query.
     */
    public static function take($value)
    {
        return static::query()->take($value);
    }

    /**
     * Set the "offset" value of the query.
     */
    public static function offset($value)
    {
        return static::query()->offset($value);
    }

    /**
     * Alias to set the "offset" value of the query.
     */
    public static function skip($value)
    {
        return static::query()->skip($value);
    }

    /**
     * Chunk the results of the query.
     */
    public static function chunk($count, callable $callback)
    {
        return static::query()->chunk($count, $callback);
    }

    /**
     * Chunk the results using scroll API for better performance.
     */
    public static function scrollChunk($count, callable $callback, $scrollTimeout = '1m')
    {
        return static::query()->scrollChunk($count, $callback, $scrollTimeout);
    }

    /**
     * Get all results using scroll API for large datasets.
     */
    public static function scroll($scrollSize = 1000, $scrollTimeout = '1m')
    {
        return static::query()->scroll($scrollSize, $scrollTimeout);
    }

    /**
     * Execute the query and get the first result.
     */
    public static function first()
    {
        return static::query()->first();
    }

    /**
     * Get a single column's value from the first result of a query.
     */
    public static function value($column)
    {
        $result = static::query()->first();
        return $result ? $result->{$column} : null;
    }

    /**
     * Get an array with the values of a given column.
     */
    public static function pluck($column, $key = null)
    {
        $results = static::query()->get();

        if ($key) {
            return $results->pluck($column, $key);
        }

        return $results->pluck($column);
    }

    /**
     * Determine if any rows exist for the current query.
     */
    public static function exists()
    {
        return static::query()->count() > 0;
    }

    /**
     * Determine if no rows exist for the current query.
     */
    public static function doesntExist()
    {
        return !static::exists();
    }

    /**
     * Retrieve the "count" result of the query.
     */
    public static function count()
    {
        return static::query()->count();
    }

    /**
     * Retrieve the minimum value of a given column.
     */
    public static function min($column)
    {
        return static::query()->aggregate('min_' . $column, [
            'min' => ['field' => $column]
        ])->get()->aggregation('min_' . $column)['value'] ?? null;
    }

    /**
     * Retrieve the maximum value of a given column.
     */
    public static function max($column)
    {
        return static::query()->aggregate('max_' . $column, [
            'max' => ['field' => $column]
        ])->get()->aggregation('max_' . $column)['value'] ?? null;
    }

    /**
     * Retrieve the sum of the values of a given column.
     */
    public static function sum($column)
    {
        return static::query()->aggregate('sum_' . $column, [
            'sum' => ['field' => $column]
        ])->get()->aggregation('sum_' . $column)['value'] ?? 0;
    }

    /**
     * Retrieve the average of the values of a given column.
     */
    public static function avg($column)
    {
        return static::query()->aggregate('avg_' . $column, [
            'avg' => ['field' => $column]
        ])->get()->aggregation('avg_' . $column)['value'] ?? null;
    }

    /**
     * Alias for the "avg" method.
     */
    public static function average($column)
    {
        return static::avg($column);
    }

    /**
     * Apply the callback if the given "value" is truthy.
     */
    public static function when($value, callable $callback, callable $default = null)
    {
        return static::query()->when($value, $callback, $default);
    }

    /**
     * Apply the callback if the given "value" is falsy.
     */
    public static function unless($value, callable $callback, callable $default = null)
    {
        return static::query()->unless($value, $callback, $default);
    }
}
