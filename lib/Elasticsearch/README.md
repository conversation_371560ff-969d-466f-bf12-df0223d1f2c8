# Elasticsearch 2.4.6 Laravel Package

A custom Elasticsearch package for Laravel 12 that provides Eloquent-like functionality for Elasticsearch 2.4.6.

## Features

- **Eloquent-like API**: Familiar syntax for Laravel developers
- **Elasticsearch 2.4.6 Compatible**: Specifically designed for older Elasticsearch versions
- **Query Builder**: Fluent query building with method chaining
- **Model Relationships**: Support for basic model operations
- **Collection Support**: Custom collection class with Elasticsearch-specific methods
- **Aggregations**: Support for Elasticsearch aggregations
- **Highlighting**: Search result highlighting support
- **Bulk Operations**: Efficient bulk indexing and updates

## Installation

1. The package is already included in your `lib/` directory
2. Update your `composer.json` autoload section (already done):
```json
"autoload": {
    "psr-4": {
        "Lib\\": "lib/"
    }
}
```

3. Run composer dump-autoload:
```bash
composer dump-autoload
```

4. Register the service provider in `config/app.php`:
```php
'providers' => [
    // Other providers...
    Lib\Elasticsearch\ElasticsearchServiceProvider::class,
],
```

## Configuration

Update your `config/database.php` to include Elasticsearch connection:

```php
'connections' => [
    'elasticsearch' => [
        'driver' => 'elasticsearch',
        'hosts' => explode(',', env('ES_HOSTS', 'http://localhost:9200')),
        'username' => env('ES_USERNAME', ''),
        'password' => env('ES_PASSWORD', ''),
        'timeout' => env('ES_TIMEOUT', 30),
    ],
    // Other connections...
],
```

Add to your `.env` file:
```env
ES_HOSTS=http://localhost:9200
ES_USERNAME=
ES_PASSWORD=
ES_TIMEOUT=30
```

## Usage

### Creating a Model

```php
<?php

namespace App\Models\ES;

use Lib\Elasticsearch\Model;

class Article extends Model
{
    protected $index = 'articles';
    protected $type = 'article';
    
    protected $fillable = [
        'title',
        'content',
        'author',
        'published_at',
    ];
    
    protected $casts = [
        'published_at' => 'datetime',
    ];
}
```

### Basic Operations

```php
// Create
$article = Article::create([
    'title' => 'My Article',
    'content' => 'Article content...',
    'author' => 'John Doe',
]);

// Find by ID
$article = Article::find('document_id');

// Update
$article->title = 'Updated Title';
$article->save();

// Delete
$article->delete();
```

### Querying

```php
// Basic where clauses
$articles = Article::where('author', 'John Doe')->get();
$articles = Article::where('views', '>', 100)->get();
$articles = Article::whereIn('status', ['published', 'featured'])->get();

// OR where clauses
$articles = Article::where('status', 'published')
    ->orWhere('status', 'featured')
    ->get();

$articles = Article::where('author', 'John Doe')
    ->orWhere('author', 'Jane Smith')
    ->get();

// Complex OR conditions
$articles = Article::where('category', 'tech')
    ->where(function ($query) {
        $query->where('status', 'published')
              ->orWhere('status', 'featured');
    })
    ->get();

// Full-text search
$articles = Article::search('laravel elasticsearch')->get();
$articles = Article::match('title', 'Laravel Tutorial')->get();

// Ordering and pagination
$articles = Article::orderBy('published_at', 'desc')
    ->skip(10)
    ->take(5)
    ->get();

// Aggregations
$results = Article::query()
    ->aggregate('avg_views', ['avg' => ['field' => 'views']])
    ->get();

$avgViews = $results->aggregation('avg_views')['value'];
```

### Advanced Queries

```php
// Complex boolean queries
$articles = Article::query()
    ->where('status', 'published')
    ->where('views', '>', 100)
    ->search('laravel', ['title', 'content'])
    ->orderBy('_score', 'desc')
    ->highlight(['title', 'content'])
    ->get();

// Using query builder directly
$builder = Article::query()
    ->where('author', 'John Doe')
    ->whereBetween('published_at', ['2023-01-01', '2023-12-31']);

$count = $builder->count();
$articles = $builder->get();
```

### Collection Methods

```php
$articles = Article::search('laravel')->get();

// Get total hits
$total = $articles->total();

// Get max score
$maxScore = $articles->maxScore();

// Get query execution time
$took = $articles->took();

// Get aggregations
$aggregations = $articles->aggregations();

// Filter by score
$highScoreArticles = $articles->whereScoreAbove(0.5);

// Get highlights
$highlights = $articles->highlights();
```

### Handling Large Datasets

#### Chunk Processing
```php
// Process records in batches of 1000
Article::chunk(1000, function ($articles, $page) {
    echo "Processing page {$page} with " . $articles->count() . " records\n";

    foreach ($articles as $article) {
        // Process each article
        $this->processArticle($article);
    }

    // Return false to stop processing
    // return false;
});
```

#### Scroll API (Better for Large Datasets)
```php
// Get all records using scroll API
$allArticles = Article::where('status', 'published')
    ->scroll(1500); // 1500 records per batch

echo "Total: " . $allArticles->total() . " articles\n";

// Process in chunks using scroll (memory efficient)
Article::scrollChunk(2000, function ($articles, $page) {
    foreach ($articles as $article) {
        $this->processArticle($article);
    }
}, '2m'); // 2 minute scroll timeout
```

#### Smart All Method
```php
// Automatically chooses best method based on dataset size
$articles = Article::where('category', 'tech')->all();
```

#### Export Example
```php
// Export large dataset to CSV
$file = fopen('articles.csv', 'w');
fputcsv($file, ['ID', 'Title', 'Author', 'Published']);

Article::scrollChunk(1000, function ($articles) use ($file) {
    foreach ($articles as $article) {
        fputcsv($file, [
            $article->getKey(),
            $article->title,
            $article->author,
            $article->published_at
        ]);
    }
});

fclose($file);
```

### Conditional Queries (when/unless)

```php
// Basic when usage
$results = Article::query()
    ->when($searchTerm, function ($query, $term) {
        return $query->search($term, ['title', 'content']);
    })
    ->when($status, function ($query, $status) {
        return $query->where('status', $status);
    })
    ->get();

// When with else condition
$results = Article::query()
    ->when($userRole === 'admin',
        function ($query) {
            // Admin can see all articles
            return $query;
        },
        function ($query) use ($userId) {
            // Regular users see only published articles
            return $query->where('status', 'published');
        }
    )
    ->get();

// Unless method (opposite of when)
$results = Article::query()
    ->unless($includeDeleted, function ($query) {
        return $query->where('deleted_at', null);
    })
    ->get();

// Complex filtering
$results = Article::query()
    ->when($filters['search'] ?? null, function ($query, $term) {
        return $query->search($term);
    })
    ->when($filters['category'] ?? null, function ($query, $category) {
        return $query->where('category', $category);
    })
    ->when($filters['date_from'] ?? null, function ($query, $date) {
        return $query->where('published_at', '>=', $date);
    })
    ->get();
```

### Scopes

```php
class Article extends Model
{
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeByAuthor($query, $author)
    {
        return $query->where('author', $author);
    }
}

// Usage
$articles = Article::published()->byAuthor('John Doe')->get();
```

### Mutators and Accessors

```php
class Article extends Model
{
    public function getTitleAttribute($value)
    {
        return ucfirst($value);
    }
    
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = strtolower($value);
    }
}
```

## API Reference

### Model Methods

- `find($id)` - Find by ID
- `create($attributes)` - Create new document
- `save()` - Save document
- `delete()` - Delete document
- `fill($attributes)` - Mass assign attributes

### Query Builder Methods

- `where($field, $operator, $value)` - Add where clause
- `whereIn($field, $values)` - Where in clause
- `whereBetween($field, $range)` - Where between clause
- `whereNull($field)` - Where null clause
- `orWhere($field, $operator, $value)` - Add OR where clause
- `orWhereIn($field, $values)` - OR where in clause
- `orWhereBetween($field, $range)` - OR where between clause
- `orWhereNull($field)` - OR where null clause
- `search($query, $fields)` - Full-text search
- `match($field, $query)` - Match query
- `orderBy($field, $direction)` - Add ordering
- `limit($size)` - Set limit
- `offset($from)` - Set offset
- `aggregate($name, $aggregation)` - Add aggregation
- `highlight($fields, $options)` - Add highlighting

### Conditional Methods

- `when($value, $callback, $default)` - Apply callback if value is truthy
- `unless($value, $callback, $default)` - Apply callback if value is falsy

### Large Dataset Methods

- `chunk($size, $callback)` - Process in chunks using pagination
- `scrollChunk($size, $callback, $timeout)` - Process in chunks using scroll API
- `scroll($size, $timeout)` - Get all data using scroll API
- `all($scrollSize)` - Smart method that chooses best approach

### Debug Methods

- `toQuery()` - Get the generated query array
- `dd()` - Dump the query and exit

### Collection Methods

- `total()` - Get total hits
- `maxScore()` - Get maximum score
- `took()` - Get query execution time
- `aggregations()` - Get all aggregations
- `aggregation($name)` - Get specific aggregation

## Elasticsearch 2.4.6 Compatibility

This package is specifically designed for Elasticsearch 2.4.6 and includes:

- Support for document types (removed in later ES versions)
- Compatible query DSL syntax
- Proper handling of mapping and indexing for ES 2.x
- Support for ES 2.x aggregation syntax

## Contributing

Feel free to submit issues and enhancement requests!

## License

This package is open-sourced software licensed under the MIT license.
