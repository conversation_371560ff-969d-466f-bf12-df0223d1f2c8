# Installation Guide

## Step 1: Autoload the Package

The package is already included in your `lib/` directory and composer.json has been updated. Run:

```bash
composer dump-autoload
```

## Step 2: Register the Service Provider

Add the service provider to your `config/app.php`:

```php
'providers' => [
    // Other providers...
    Lib\Elasticsearch\ElasticsearchServiceProvider::class,
],
```

## Step 3: Update Your Existing Models

Replace your existing ES models. For example, update `app/Models/ES/DemoAudience.php`:

```php
<?php

namespace App\Models\ES;

use Lib\Elasticsearch\Model;

class DemoAudience extends Model
{
    protected $index = 'demo_audiences'; // your actual index name
    protected $type = 'demo_audience';   // your actual type name
    
    protected $fillable = [
        // your fillable fields
    ];
    
    protected $casts = [
        // your field casts
    ];
}
```

## Step 4: Test the Connection

Create a simple test to verify everything works:

```php
// In tinker or a test file
use App\Models\ES\DemoAudience;

// Test connection
php artisan elasticsearch:manage info

// Test model
$count = DemoAudience::count();
echo "Total documents: " . $count;

// Test search
$results = DemoAudience::search('your search term')->get();
echo "Found: " . $results->total() . " documents";
```

## Step 5: Update All Your ES Models

Apply the same changes to all your ES models:

- `app/Models/ES/Ecomerce.php`
- `app/Models/ES/Instagram.php`
- `app/Models/ES/Linkedin.php`
- `app/Models/ES/Monitaz.php`
- `app/Models/ES/Paper.php`
- `app/Models/ES/PhoneSocial.php`
- `app/Models/ES/ReViewapp.php`
- `app/Models/ES/ReviewGoogleBusiness.php`
- `app/Models/ES/RuleAnalytics.php`
- `app/Models/ES/Social.php`
- `app/Models/ES/Threads.php`
- `app/Models/ES/Tiktok.php`
- `app/Models/ES/TopKeyword.php`
- `app/Models/ES/Tv.php`
- `app/Models/ES/Tvplus.php`
- `app/Models/ES/Twitter.php`
- `app/Models/ES/Website.php`
- `app/Models/ES/WebsiteTemp.php`
- `app/Models/ES/Youtube.php`
- `app/Models/ES/Zalo.php`
- `app/Models/ES/ZaloGroup.php`

## Step 6: Remove Old Package (Optional)

If you want to remove the old `pdphilip/elasticsearch` package:

```bash
composer remove pdphilip/elasticsearch
```

## Available Commands

```bash
# Show cluster info
php artisan elasticsearch:manage info

# Create an index
php artisan elasticsearch:manage create-index --index=my_index

# Delete an index
php artisan elasticsearch:manage delete-index --index=my_index

# List all indices
php artisan elasticsearch:manage list-indices
```

## New Features: Scroll and Chunk

The package now includes powerful features for handling large datasets:

### Chunk Processing
```php
// Process records in batches
DemoAudience::chunk(1000, function ($audiences, $page) {
    foreach ($audiences as $audience) {
        // Process each record
    }
});
```

### Scroll API (Better Performance)
```php
// Get all data using scroll
$allData = DemoAudience::scroll(1500);

// Process in chunks using scroll
DemoAudience::scrollChunk(2000, function ($audiences, $page) {
    foreach ($audiences as $audience) {
        // Process each record
    }
}, '2m'); // 2 minute timeout
```

### Smart All Method
```php
// Automatically chooses best method
$data = DemoAudience::where('status', 'active')->all();
```

## Configuration

Your existing Elasticsearch configuration in `config/database.php` should work as-is:

```php
'elasticsearch' => [
    'driver' => 'elasticsearch',
    'hosts' => explode(',', env('ES_HOSTS', 'http://localhost:9200')),
    'username' => env('ES_USERNAME', ''),
    'password' => env('ES_PASSWORD', ''),
    'timeout' => env('ES_TIMEOUT', 30),
],
```

## Troubleshooting

### Connection Issues
- Verify Elasticsearch 2.4.6 is running
- Check your ES_HOSTS environment variable
- Test connection with: `php artisan elasticsearch:manage info`

### Model Issues
- Ensure you've extended `Lib\Elasticsearch\Model`
- Check your index and type names are correct
- Verify your fillable and casts arrays

### Query Issues
- The package uses Elasticsearch 2.4.6 query syntax
- Check the README.md for query examples
- Use the Laravel log to debug queries

## Migration Tips

1. **Start with one model**: Update and test one model at a time
2. **Backup your data**: Always backup before making changes
3. **Test thoroughly**: Use the provided examples to test functionality
4. **Check logs**: Monitor Laravel logs for any errors
5. **Use the command**: The `elasticsearch:manage` command helps with debugging

## Need Help?

Check the examples in:
- `lib/Elasticsearch/Examples/ExampleModel.php`
- `lib/Elasticsearch/Examples/MigrateExistingModel.php`
- `lib/Elasticsearch/README.md`
