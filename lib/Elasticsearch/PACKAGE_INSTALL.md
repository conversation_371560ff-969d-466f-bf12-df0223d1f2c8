# Package Installation Guide

## Quick Installation

Run the installation script:

```bash
./install-elasticsearch-package.sh
```

## Manual Installation

### Step 1: Install the Package

The package is configured as a local Composer package. Install it with:

```bash
composer install
```

This will automatically install the `lib/elasticsearch` package from the local `lib/Elasticsearch` directory.

### Step 2: Register Service Provider

Add to `config/app.php`:

```php
'providers' => [
    // Other providers...
    Lib\Elasticsearch\ElasticsearchServiceProvider::class,
],
```

### Step 3: Publish Configuration (Optional)

```bash
php artisan vendor:publish --tag=elasticsearch-config
```

### Step 4: Update Environment Variables

Add to your `.env` file:

```env
ES_HOSTS=http://localhost:9200
ES_USERNAME=
ES_PASSWORD=
ES_TIMEOUT=30
```

### Step 5: Test Installation

```bash
# Test connection
php artisan elasticsearch:manage info

# List indices
php artisan elasticsearch:manage list-indices
```

## Package Structure

```
lib/Elasticsearch/
├── composer.json              # Package definition
├── Client.php                 # ES 2.4.6 client
├── Model.php                  # Base model class
├── Builder.php                # Query builder
├── Collection.php             # Results collection
├── ElasticsearchServiceProvider.php
├── Commands/
│   └── ElasticsearchCommand.php
├── Concerns/
│   └── HasElasticsearchQueries.php
├── Examples/
│   ├── ExampleModel.php
│   └── MigrateExistingModel.php
└── config/
    └── elasticsearch.php
```

## Composer Configuration

The main `composer.json` has been updated with:

1. **Local Repository**: Points to `./lib/Elasticsearch`
2. **Package Requirement**: `"lib/elasticsearch": "*"`
3. **Removed Old Package**: `pdphilip/elasticsearch` removed

### Repository Configuration

```json
"repositories": [
    {
        "type": "path",
        "url": "./lib/Elasticsearch"
    }
]
```

### Package Requirement

```json
"require": {
    "lib/elasticsearch": "*"
}
```

## Benefits of Package Installation

1. **Proper Dependency Management**: Composer handles dependencies
2. **Auto-discovery**: Laravel automatically discovers the service provider
3. **Version Control**: Easy to version and update
4. **Publishable**: Can be published to Packagist later
5. **Testable**: Includes test structure
6. **Documentation**: Self-contained with docs

## Updating the Package

To update the package:

1. Make changes in `lib/Elasticsearch/`
2. Run `composer dump-autoload`
3. Test changes

## Publishing to Packagist (Future)

To publish this package to Packagist:

1. Create a Git repository for the package
2. Move `lib/Elasticsearch/` to the repository root
3. Update `composer.json` name to your namespace
4. Submit to Packagist

## Troubleshooting

### Package Not Found
```bash
composer dump-autoload
composer install
```

### Service Provider Not Registered
Check `config/app.php` for the service provider entry.

### Commands Not Available
Ensure the service provider is registered and run:
```bash
php artisan config:clear
php artisan cache:clear
```

### Connection Issues
Check your Elasticsearch configuration:
```bash
php artisan elasticsearch:manage info
```

## Migration from Old Package

If you were using `pdphilip/elasticsearch`:

1. The installation script removes it automatically
2. Update your models to extend `Lib\Elasticsearch\Model`
3. Update any custom queries to use the new syntax
4. Test thoroughly

## Development

For package development:

```bash
# Run tests (when implemented)
./vendor/bin/phpunit lib/Elasticsearch/tests/

# Check code style
./vendor/bin/pint lib/Elasticsearch/

# Generate documentation
# (Add documentation generation tools as needed)
```
