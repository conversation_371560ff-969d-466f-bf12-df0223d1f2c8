<?php

namespace Lib\Elasticsearch;

use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use JsonSerializable;
use Lib\Elasticsearch\Concerns\HasElasticsearchQueries;

abstract class Model implements Arrayable, Jsonable, JsonSerializable
{
    use HasElasticsearchQueries;

    /**
     * The connection name for the model.
     */
    protected $connection = 'elasticsearch';

    /**
     * The index associated with the model.
     */
    protected $index;

    /**
     * The type associated with the model.
     */
    protected $type;

    /**
     * The primary key for the model.
     */
    protected $primaryKey = '_id';

    /**
     * The model's attributes.
     */
    protected $attributes = [];

    /**
     * The model's original attributes.
     */
    protected $original = [];

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];

    /**
     * The attributes that aren't mass assignable.
     */
    protected $guarded = ['*'];

    /**
     * The attributes that should be hidden for arrays.
     */
    protected $hidden = [];

    /**
     * The attributes that should be visible in arrays.
     */
    protected $visible = [];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [];

    /**
     * Indicates if the model exists.
     */
    public $exists = false;

    /**
     * The document score from search results.
     */
    protected $score;

    /**
     * The document highlight from search results.
     */
    protected $highlight = [];

    /**
     * The Elasticsearch client instance.
     */
    protected static $client;

    /**
     * Create a new Eloquent model instance.
     */
    public function __construct(array $attributes = [])
    {
        $this->fill($attributes);
    }

    /**
     * Set the Elasticsearch client.
     */
    public static function setClient(Client $client)
    {
        static::$client = $client;
    }

    /**
     * Get the Elasticsearch client.
     */
    public static function getClient()
    {
        if (!static::$client) {
            $config = config('database.connections.elasticsearch', []);
            static::$client = new Client($config);
        }

        return static::$client;
    }

    /**
     * Get the index name for the model.
     */
    public function getIndex()
    {
        return $this->index ?: Str::snake(Str::pluralStudly(class_basename($this)));
    }

    /**
     * Get the type name for the model.
     */
    public function getType()
    {
        return $this->type ?: Str::snake(class_basename($this));
    }

    /**
     * Get the primary key for the model.
     */
    public function getKeyName()
    {
        return $this->primaryKey;
    }

    /**
     * Get the value of the model's primary key.
     */
    public function getKey()
    {
        return $this->getAttribute($this->getKeyName());
    }

    /**
     * Set the primary key value.
     */
    public function setId($id)
    {
        $this->setAttribute($this->getKeyName(), $id);
        return $this;
    }

    /**
     * Get the document score.
     */
    public function getScore()
    {
        return $this->score;
    }

    /**
     * Set the document score.
     */
    public function setScore($score)
    {
        $this->score = $score;
        return $this;
    }

    /**
     * Get the document highlight.
     */
    public function getHighlight()
    {
        return $this->highlight;
    }

    /**
     * Set the document highlight.
     */
    public function setHighlight(array $highlight)
    {
        $this->highlight = $highlight;
        return $this;
    }

    /**
     * Fill the model with an array of attributes.
     */
    public function fill(array $attributes)
    {
        foreach ($attributes as $key => $value) {
            if ($this->isFillable($key)) {
                $this->setAttribute($key, $value);
            }
        }

        return $this;
    }

    /**
     * Determine if the given attribute may be mass assigned.
     */
    public function isFillable($key)
    {
        if (in_array($key, $this->getFillable())) {
            return true;
        }

        if ($this->isGuarded($key)) {
            return false;
        }

        return empty($this->getFillable()) && !Str::startsWith($key, '_');
    }

    /**
     * Determine if the given key is guarded.
     */
    public function isGuarded($key)
    {
        return in_array($key, $this->getGuarded()) || $this->getGuarded() == ['*'];
    }

    /**
     * Get the fillable attributes for the model.
     */
    public function getFillable()
    {
        return $this->fillable;
    }

    /**
     * Get the guarded attributes for the model.
     */
    public function getGuarded()
    {
        return $this->guarded;
    }

    /**
     * Get an attribute from the model.
     */
    public function getAttribute($key)
    {
        if (!$key) {
            return;
        }

        if (array_key_exists($key, $this->attributes)) {
            return $this->getAttributeValue($key);
        }

        return null;
    }

    /**
     * Get a plain attribute (not a relationship).
     */
    public function getAttributeValue($key)
    {
        $value = $this->getAttributeFromArray($key);

        if ($this->hasCast($key)) {
            return $this->castAttribute($key, $value);
        }

        return $value;
    }

    /**
     * Get an attribute from the $attributes array.
     */
    protected function getAttributeFromArray($key)
    {
        return $this->attributes[$key] ?? null;
    }

    /**
     * Set a given attribute on the model.
     */
    public function setAttribute($key, $value)
    {
        $this->attributes[$key] = $value;

        return $this;
    }

    /**
     * Determine if a get mutator exists for an attribute.
     */
    public function hasGetMutator($key)
    {
        return method_exists($this, 'get'.Str::studly($key).'Attribute');
    }

    /**
     * Determine if a set mutator exists for an attribute.
     */
    public function hasSetMutator($key)
    {
        return method_exists($this, 'set'.Str::studly($key).'Attribute');
    }

    /**
     * Determine whether an attribute should be cast to a native type.
     */
    public function hasCast($key, $types = null)
    {
        if (array_key_exists($key, $this->getCasts())) {
            return $types ? in_array($this->getCastType($key), (array) $types, true) : true;
        }

        return false;
    }

    /**
     * Get the casts array.
     */
    public function getCasts()
    {
        return $this->casts;
    }

    /**
     * Get the type of cast for a model attribute.
     */
    protected function getCastType($key)
    {
        return trim(strtolower($this->getCasts()[$key]));
    }

    /**
     * Cast an attribute to a native PHP type.
     */
    protected function castAttribute($key, $value)
    {
        if (is_null($value)) {
            return $value;
        }

        switch ($this->getCastType($key)) {
            case 'int':
            case 'integer':
                return (int) $value;
            case 'real':
            case 'float':
            case 'double':
                return (float) $value;
            case 'string':
                return (string) $value;
            case 'bool':
            case 'boolean':
                return (bool) $value;
            case 'object':
                return $this->fromJson($value, true);
            case 'array':
            case 'json':
                return $this->fromJson($value);
            case 'collection':
                return new Collection($this->fromJson($value));
            case 'date':
            case 'datetime':
                return $this->asDateTime($value);
            case 'timestamp':
                return $this->asTimestamp($value);
            default:
                return $value;
        }
    }

    /**
     * Decode the given JSON back into an array or object.
     */
    public function fromJson($value, $asObject = false)
    {
        return json_decode($value, !$asObject);
    }

    /**
     * Return a timestamp as DateTime object.
     */
    protected function asDateTime($value)
    {
        if ($value instanceof \DateTime) {
            return $value;
        }

        if (is_numeric($value)) {
            return \DateTime::createFromFormat('U', $value);
        }

        return new \DateTime($value);
    }

    /**
     * Return a timestamp as unix timestamp.
     */
    protected function asTimestamp($value)
    {
        return $this->asDateTime($value)->getTimestamp();
    }

    /**
     * Save the model to the database.
     */
    public function save()
    {
        if ($this->exists) {
            return $this->performUpdate();
        } else {
            return $this->performInsert();
        }
    }

    /**
     * Perform a model insert operation.
     */
    protected function performInsert()
    {
        $client = static::getClient();

        $response = $client->index(
            $this->getIndex(),
            $this->getType(),
            $this->getKey(),
            $this->attributesToArray()
        );

        if (isset($response['_id'])) {
            $this->setId($response['_id']);
            $this->exists = true;
            $this->syncOriginal();
            return true;
        }

        return false;
    }

    /**
     * Perform a model update operation.
     */
    protected function performUpdate()
    {
        $client = static::getClient();

        $response = $client->update(
            $this->getIndex(),
            $this->getType(),
            $this->getKey(),
            ['doc' => $this->attributesToArray()]
        );

        if (isset($response['_id'])) {
            $this->syncOriginal();
            return true;
        }

        return false;
    }

    /**
     * Delete the model from the database.
     */
    public function delete()
    {
        if (!$this->exists) {
            return false;
        }

        $client = static::getClient();

        $response = $client->delete(
            $this->getIndex(),
            $this->getType(),
            $this->getKey()
        );

        if (isset($response['found']) && $response['found']) {
            $this->exists = false;
            return true;
        }

        return false;
    }

    /**
     * Sync the original attributes with the current.
     */
    public function syncOriginal()
    {
        $this->original = $this->attributes;

        return $this;
    }

    /**
     * Convert the model's attributes to an array.
     */
    public function attributesToArray()
    {
        $attributes = $this->attributes;

        // Remove the primary key if it's _id and null
        if ($this->getKeyName() === '_id' && is_null($attributes['_id'] ?? null)) {
            unset($attributes['_id']);
        }

        return $attributes;
    }

    /**
     * Convert the model instance to an array.
     */
    public function toArray()
    {
        return $this->attributesToArray();
    }

    /**
     * Convert the model instance to JSON.
     */
    public function toJson($options = 0)
    {
        return json_encode($this->jsonSerialize(), $options);
    }

    /**
     * Convert the object into something JSON serializable.
     */
    public function jsonSerialize(): mixed
    {
        return $this->toArray();
    }

    /**
     * Dynamically retrieve attributes on the model.
     */
    public function __get($key)
    {
        return $this->getAttribute($key);
    }

    /**
     * Dynamically set attributes on the model.
     */
    public function __set($key, $value)
    {
        $this->setAttribute($key, $value);
    }

    /**
     * Determine if an attribute or relation exists on the model.
     */
    public function __isset($key)
    {
        return $this->offsetExists($key);
    }

    /**
     * Unset an attribute on the model.
     */
    public function __unset($key)
    {
        unset($this->attributes[$key]);
    }

    /**
     * Determine if the given attribute exists.
     */
    public function offsetExists($offset): bool
    {
        return !is_null($this->getAttribute($offset));
    }

    /**
     * Get the value for a given offset.
     */
    public function offsetGet($offset): mixed
    {
        return $this->getAttribute($offset);
    }

    /**
     * Set the value for a given offset.
     */
    public function offsetSet($offset, $value): void
    {
        $this->setAttribute($offset, $value);
    }

    /**
     * Unset the value for a given offset.
     */
    public function offsetUnset($offset): void
    {
        unset($this->attributes[$offset]);
    }

    /**
     * Begin querying the model.
     */
    public static function query()
    {
        return (new static)->newQuery();
    }

    /**
     * Get a new query builder for the model's table.
     */
    public function newQuery()
    {
        return new Builder($this, static::getClient());
    }

    /**
     * Create a new instance of the given model.
     */
    public function newInstance($attributes = [], $exists = false)
    {
        $model = new static((array) $attributes);

        $model->exists = $exists;

        return $model;
    }

    /**
     * Handle dynamic static method calls into the method.
     */
    public static function __callStatic($method, $parameters)
    {
        return (new static)->$method(...$parameters);
    }

    /**
     * Handle dynamic method calls into the model.
     */
    public function __call($method, $parameters)
    {
        return $this->newQuery()->$method(...$parameters);
    }
}
