<?php

namespace Lib\Elasticsearch;

use App\Models\ES\Website;
use Illuminate\Support\Arr;

class Builder
{
    protected $model;
    protected $client;
    protected $query = [];
    protected $filters = [];
    protected $must = [];
    protected $mustNot = [];
    protected $should = [];
    protected $sort = [];
    protected $from = 0;
    protected $size = 10;
    protected $source = [];
    protected $aggregations = [];
    protected $highlight = [];

    public function __construct(Model $model, Client $client)
    {
        $this->model = $model;
        $this->client = $client;
    }

    /**
     * Add a where clause
     */
    public function where($field, $operator = null, $value = null)
    {
        if (is_array($field)) {
            foreach ($field as $key => $val) {
                $this->where($key, '=', $val);
            }
            return $this;
        }

        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }

        switch ($operator) {
            case '=':
            case '==':
                $this->must[] = ['term' => [$field => $value]];
                break;
            case '!=':
            case '<>':
                $this->mustNot[] = ['term' => [$field => $value]];
                break;
            case '>':
                $this->must[] = ['range' => [$field => ['gt' => $value]]];
                break;
            case '>=':
                $this->must[] = ['range' => [$field => ['gte' => $value]]];
                break;
            case '<':
                $this->must[] = ['range' => [$field => ['lt' => $value]]];
                break;
            case '<=':
                $this->must[] = ['range' => [$field => ['lte' => $value]]];
                break;
            case 'like':
                $this->must[] = ['wildcard' => [$field => "*{$value}*"]];
                break;
            case 'in':
                $this->must[] = ['terms' => [$field => array_values((array) $value)]];
                break;
            case 'not in':
                $this->mustNot[] = ['terms' => [$field => array_values((array) $value)]];
                break;
        }

        return $this;
    }

    /**
     * Add a where not clause
     */
    public function whereNot($field, $operator = null, $value = null)
    {
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }

        switch ($operator) {
            case '=':
            case '==':
                $this->mustNot[] = ['term' => [$field => $value]];
                break;
            default:
                // For other operators, we need to negate the logic
                $this->where($field, $operator === '!=' ? '=' : '!=', $value);
        }

        return $this;
    }

    /**
     * Add a where in clause (ES 2.4.6 compatible)
     */
    public function whereIn($field, array $values)
    {
        // Ensure values are properly formatted for ES 2.4.6
        $cleanValues = array_values($values);
        $this->must[] = ['terms' => [$field => $cleanValues]];
        return $this;
    }

    /**
     * Add a where not in clause (ES 2.4.6 compatible)
     */
    public function whereNotIn($field, array $values)
    {
        // Ensure values are properly formatted for ES 2.4.6
        $cleanValues = array_values($values);
        $this->mustNot[] = ['terms' => [$field => $cleanValues]];
        return $this;
    }

    /**
     * Add a where between clause
     */
    public function whereBetween($field, array $values)
    {
        $this->must[] = ['range' => [$field => ['gte' => $values[0], 'lte' => $values[1]]]];
        return $this;
    }

    /**
     * Add a where not between clause
     */
    public function whereNotBetween($field, array $values)
    {
        $this->mustNot[] = ['range' => [$field => ['gte' => $values[0], 'lte' => $values[1]]]];
        return $this;
    }

    /**
     * Add a where null clause
     */
    public function whereNull($field)
    {
        $this->mustNot[] = ['exists' => ['field' => $field]];
        return $this;
    }

    /**
     * Add a where not null clause
     */
    public function whereNotNull($field)
    {
        $this->must[] = ['exists' => ['field' => $field]];
        return $this;
    }

    /**
     * Add an OR where clause
     */
    public function orWhere($field, $operator = null, $value = null)
    {
        if (is_array($field)) {
            foreach ($field as $key => $val) {
                $this->orWhere($key, '=', $val);
            }
            return $this;
        }

        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }

        switch ($operator) {
            case '=':
            case '==':
                $this->should[] = ['term' => [$field => $value]];
                break;
            case '!=':
            case '<>':
                $this->should[] = ['bool' => ['must_not' => [['term' => [$field => $value]]]]];
                break;
            case '>':
                $this->should[] = ['range' => [$field => ['gt' => $value]]];
                break;
            case '>=':
                $this->should[] = ['range' => [$field => ['gte' => $value]]];
                break;
            case '<':
                $this->should[] = ['range' => [$field => ['lt' => $value]]];
                break;
            case '<=':
                $this->should[] = ['range' => [$field => ['lte' => $value]]];
                break;
            case 'like':
                $this->should[] = ['wildcard' => [$field => "*{$value}*"]];
                break;
            case 'in':
                $this->should[] = ['terms' => [$field => array_values((array) $value)]];
                break;
            case 'not in':
                $this->should[] = ['bool' => ['must_not' => [['terms' => [$field => array_values((array) $value)]]]]];
                break;
        }

        return $this;
    }

    /**
     * Add an OR where in clause
     */
    public function orWhereIn($field, array $values)
    {
        $cleanValues = array_values($values);
        $this->should[] = ['terms' => [$field => $cleanValues]];
        return $this;
    }

    /**
     * Add an OR where not in clause
     */
    public function orWhereNotIn($field, array $values)
    {
        $cleanValues = array_values($values);
        $this->should[] = ['bool' => ['must_not' => [['terms' => [$field => $cleanValues]]]]];
        return $this;
    }

    /**
     * Add an OR where between clause
     */
    public function orWhereBetween($field, array $values)
    {
        $this->should[] = ['range' => [$field => ['gte' => $values[0], 'lte' => $values[1]]]];
        return $this;
    }

    /**
     * Add an OR where not between clause
     */
    public function orWhereNotBetween($field, array $values)
    {
        $this->should[] = ['bool' => ['must_not' => [['range' => [$field => ['gte' => $values[0], 'lte' => $values[1]]]]]]];
        return $this;
    }

    /**
     * Add an OR where null clause
     */
    public function orWhereNull($field)
    {
        $this->should[] = ['bool' => ['must_not' => [['exists' => ['field' => $field]]]]];
        return $this;
    }

    /**
     * Add an OR where not null clause
     */
    public function orWhereNotNull($field)
    {
        $this->should[] = ['exists' => ['field' => $field]];
        return $this;
    }

    /**
     * Add a full-text search
     */
    public function search($query, $fields = null)
    {
        if ($fields) {
            $this->must[] = [
                'multi_match' => [
                    'query' => $query,
                    'fields' => (array) $fields
                ]
            ];
        } else {
            $this->must[] = [
                'query_string' => [
                    'query' => $query
                ]
            ];
        }

        return $this;
    }

    /**
     * Add a match query
     */
    public function match($field, $query)
    {
        $this->must[] = ['match' => [$field => $query]];
        return $this;
    }

    /**
     * Add ordering
     */
    public function orderBy($field, $direction = 'asc')
    {
        $this->sort[] = [$field => ['order' => strtolower($direction)]];
        return $this;
    }

    /**
     * Set the limit
     */
    public function limit($limit)
    {
        $this->size = $limit;
        return $this;
    }

    /**
     * Set the offset
     */
    public function offset($offset)
    {
        $this->from = $offset;
        return $this;
    }

    /**
     * Set the size (alias for limit)
     */
    public function take($size)
    {
        return $this->limit($size);
    }

    /**
     * Set the from (alias for offset)
     */
    public function skip($from)
    {
        return $this->offset($from);
    }

    /**
     * Select specific fields
     */
    public function select($fields)
    {
        $this->source = is_array($fields) ? $fields : func_get_args();
        return $this;
    }

    /**
     * Add aggregation
     */
    public function aggregate($name, array $aggregation)
    {
        $this->aggregations[$name] = $aggregation;
        return $this;
    }

    /**
     * Add highlight
     */
    public function highlight($fields, array $options = [])
    {
        $fields = is_array($fields) ? $fields : [$fields];

        foreach ($fields as $field) {
            $this->highlight['fields'][$field] = $options;
        }

        return $this;
    }

    /**
     * Get the first result
     */
    public function first()
    {
        $results = $this->limit(1)->get();
        return $results->first();
    }

    /**
     * Get all results
     */
    public function get()
    {
        $body = $this->buildQuery();


        $response = $this->client->search(
            $this->model->getIndex(),
            $this->model->getType(),
            $body
        );
        return $this->parseResponse($response);
    }

    /**
     * Count results
     */
    public function count()
    {
        $body = $this->buildQuery();
        unset($body['from'], $body['size'], $body['sort'], $body['_source'], $body['highlight']);

        $response = $this->client->count(
            $this->model->getIndex(),
            $this->model->getType(),
            $body
        );

        return $response['count'] ?? 0;
    }

    /**
     * Get all results using scroll API for large datasets
     */
    public function scroll($scrollSize = 1000, $scrollTimeout = '1m')
    {
        $body = $this->buildQuery();
        $body['size'] = $scrollSize;
        unset($body['from']); // Remove from for scroll
        // Initial scroll request
        $response = $this->client->search(
            $this->model->getIndex(),
            $this->model->getType(),
            array_merge($body, ['scroll' => $scrollTimeout])
        );

        $allResults = [];
        $scrollId = $response['_scroll_id'] ?? null;

        // Process initial batch
        if (isset($response['hits']['hits'])) {
            $allResults = array_merge($allResults, $this->parseScrollResponse($response));
        }

        // Continue scrolling until no more results
        while ($scrollId && !empty($response['hits']['hits'])) {
            try {
                $response = $this->client->scroll($scrollId, $scrollTimeout);
                $scrollId = $response['_scroll_id'] ?? null;

                if (isset($response['hits']['hits']) && !empty($response['hits']['hits'])) {
                    $allResults = array_merge($allResults, $this->parseScrollResponse($response));
                } else {
                    break; // No more results
                }
            } catch (\Exception $e) {
                // Log error and break
                \Log::warning('Scroll error: ' . $e->getMessage());
                break;
            }
        }

        // Clear scroll context
        if ($scrollId) {
            try {
                $this->client->clearScroll($scrollId);
            } catch (\Exception $e) {
                // Ignore cleanup errors
                \Log::debug('Scroll cleanup error: ' . $e->getMessage());
            }
        }

        // Create collection with metadata from last response
        $meta = [
            'total' => $response['hits']['total'] ?? count($allResults),
            'max_score' => $response['hits']['max_score'] ?? null,
            'took' => $response['took'] ?? 0,
            'timed_out' => $response['timed_out'] ?? false,
            '_shards' => $response['_shards'] ?? [],
            'aggregations' => $response['aggregations'] ?? [],
        ];

        return new Collection($allResults, $meta);
    }

    /**
     * Chunk results and process them with a callback
     */
    public function chunk($size = 1000, callable $callback = null)
    {
        $processed = 0;
        $page = 0;

        do {
            $results = $this->offset($page * $size)->limit($size)->get();
            $count = $results->count();

            if ($count === 0) {
                break;
            }

            if ($callback) {
                $result = $callback($results, $page);
                if ($result === false) {
                    break;
                }
            }

            $processed += $count;
            $page++;

        } while ($count === $size);

        return $processed;
    }

    /**
     * Chunk results using scroll API for better performance with large datasets
     */
    public function scrollChunk($size = 1000, callable $callback = null, $scrollTimeout = '1m')
    {
        $body = $this->buildQuery();
        $body['size'] = $size;
        unset($body['from']); // Remove from for scroll

        // Initial scroll request
        $response = $this->client->search(
            $this->model->getIndex(),
            $this->model->getType(),
            array_merge($body, ['scroll' => $scrollTimeout])
        );

        $scrollId = $response['_scroll_id'] ?? null;
        $processed = 0;
        $page = 0;

        // Process initial batch
        if (isset($response['hits']['hits']) && !empty($response['hits']['hits'])) {
            $results = $this->parseScrollResponse($response);
            $collection = new Collection($results, [
                'total' => $response['hits']['total'] ?? 0,
                'max_score' => $response['hits']['max_score'] ?? null,
                'took' => $response['took'] ?? 0,
                'timed_out' => $response['timed_out'] ?? false,
                '_shards' => $response['_shards'] ?? [],
                'aggregations' => $response['aggregations'] ?? [],
            ]);

            if ($callback) {
                $result = $callback($collection, $page);
                if ($result === false) {
                    if ($scrollId) {
                        $this->client->clearScroll($scrollId);
                    }
                    return $processed;
                }
            }

            $processed += count($results);
            $page++;
        }

        // Continue scrolling
        while ($scrollId && !empty($response['hits']['hits'])) {
            $response = $this->client->scroll($scrollId, $scrollTimeout);
            $scrollId = $response['_scroll_id'] ?? null;

            if (isset($response['hits']['hits']) && !empty($response['hits']['hits'])) {
                $results = $this->parseScrollResponse($response);
                $collection = new Collection($results, [
                    'total' => $response['hits']['total'] ?? 0,
                    'max_score' => $response['hits']['max_score'] ?? null,
                    'took' => $response['took'] ?? 0,
                    'timed_out' => $response['timed_out'] ?? false,
                    '_shards' => $response['_shards'] ?? [],
                    'aggregations' => $response['aggregations'] ?? [],
                ]);

                if ($callback) {
                    $result = $callback($collection, $page);
                    if ($result === false) {
                        break;
                    }
                }

                $processed += count($results);
                $page++;
            }
        }

        // Clear scroll context
        if ($scrollId) {
            $this->client->clearScroll($scrollId);
        }

        return $processed;
    }

    /**
     * Get all results efficiently (uses scroll for large datasets)
     */
    public function all($scrollSize = 1000)
    {
        // For small datasets, use regular get
        $count = $this->count();
        if ($count <= $scrollSize) {
            return $this->limit($count)->get();
        }

        // For large datasets, use scroll
        return $this->scroll($scrollSize);
    }

    /**
     * Debug method to see the generated query
     */
    public function toQuery()
    {
        return $this->buildQuery();
    }

    /**
     * Debug method to dump the query and exit
     */
    public function dd()
    {
        dd($this->buildQuery());
    }

    /**
     * Apply the callback if the given "value" is truthy.
     */
    public function when($value, callable $callback, callable $default = null)
    {
        if ($value) {
            return $callback($this, $value) ?: $this;
        } elseif ($default) {
            return $default($this, $value) ?: $this;
        }

        return $this;
    }

    /**
     * Apply the callback if the given "value" is falsy.
     */
    public function unless($value, callable $callback, callable $default = null)
    {
        if (!$value) {
            return $callback($this, $value) ?: $this;
        } elseif ($default) {
            return $default($this, $value) ?: $this;
        }

        return $this;
    }

    /**
     * Build the Elasticsearch query (ES 2.4.6 compatible)
     */
    protected function buildQuery()
    {
        $query = [];

        // Build bool query
        $bool = [];
        if (!empty($this->must)) {
            $bool['must'] = $this->must;
        }
        if (!empty($this->mustNot)) {
            $bool['must_not'] = $this->mustNot;
        }
        if (!empty($this->should)) {
            $bool['should'] = $this->should;

            // If we only have should clauses (no must clauses), we need minimum_should_match
            if (empty($this->must) && empty($this->mustNot)) {
                $bool['minimum_should_match'] = 1;
            }
        }

        // Only add query if we have conditions
        if (!empty($bool)) {
            $query['query'] = ['bool' => $bool];
        } else {
            // ES 2.4.6 requires a query, use match_all if no conditions
            $query['query'] = ['match_all' => new \stdClass()];
        }

        // Add pagination (ES 2.4.6 compatible)
        if ($this->from > 0) {
            $query['from'] = $this->from;
        }
        if ($this->size !== 10) { // Only add if different from default
            $query['size'] = $this->size;
        }

        // Add sorting (ES 2.4.6 compatible)
        if (!empty($this->sort)) {
            $query['sort'] = $this->sort;
        }

        // Add source filtering (ES 2.4.6 compatible)
        if (!empty($this->source)) {
            $query['_source'] = $this->source;
        }

        // Add aggregations (ES 2.4.6 uses 'aggs' not 'aggregations')
        if (!empty($this->aggregations)) {
            $query['aggs'] = $this->aggregations;
        }

        // Add highlighting (ES 2.4.6 compatible)
        if (!empty($this->highlight)) {
            $query['highlight'] = $this->highlight;
        }

        return $query;
    }

    /**
     * Parse Elasticsearch response
     */
    protected function parseResponse(array $response)
    {
        $hits = $response['hits']['hits'] ?? [];
        $items = $this->parseHitsToModels($hits);

        $meta = [
            'total' => $response['hits']['total'] ?? 0,
            'max_score' => $response['hits']['max_score'] ?? null,
            'took' => $response['took'] ?? 0,
            'timed_out' => $response['timed_out'] ?? false,
            '_shards' => $response['_shards'] ?? [],
            'aggregations' => $response['aggregations'] ?? [],
        ];

        return new Collection($items, $meta);
    }

    /**
     * Parse scroll response (returns array of models, not Collection)
     */
    protected function parseScrollResponse(array $response)
    {
        $hits = $response['hits']['hits'] ?? [];
        return $this->parseHitsToModels($hits);
    }

    /**
     * Convert hits array to model instances
     */
    protected function parseHitsToModels(array $hits)
    {
        $items = [];

        foreach ($hits as $hit) {
            $model = new $this->model;
            $model->fill($hit['_source'] ?? []);
            $model->setId($hit['_id']);
            $model->setScore($hit['_score'] ?? null);
            $model->setHighlight($hit['highlight'] ?? []);
            $model->exists = true;

            $items[] = $model;
        }

        return $items;
    }
}
