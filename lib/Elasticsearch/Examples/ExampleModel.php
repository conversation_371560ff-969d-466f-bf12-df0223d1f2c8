<?php

namespace Lib\Elasticsearch\Examples;

use Lib\Elasticsearch\Model;

class ExampleModel extends Model
{
    /**
     * The index associated with the model.
     */
    protected $index = 'example_index';

    /**
     * The type associated with the model.
     */
    protected $type = 'example_type';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'content',
        'status',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'status' => 'boolean',
    ];

    /**
     * Scope a query to only include active records.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to search by title.
     */
    public function scopeSearchByTitle($query, $title)
    {
        return $query->match('title', $title);
    }

    /**
     * Get the title attribute in uppercase.
     */
    public function getTitleAttribute($value)
    {
        return strtoupper($value);
    }

    /**
     * Set the title attribute in lowercase.
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = strtolower($value);
    }
}
