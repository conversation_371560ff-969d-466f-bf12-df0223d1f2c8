<?php

namespace Lib\Elasticsearch\Examples;

use Lib\Elasticsearch\Model;

/**
 * Examples of using scroll and chunk functionality for large datasets
 */
class ScrollAndChunkExamples
{
    /**
     * Example 1: Using chunk() for processing large datasets in batches
     */
    public function chunkExample()
    {
        // Process 1000 records at a time
        DemoAudience::chunk(1000, function ($audiences, $page) {
            echo "Processing page {$page} with " . $audiences->count() . " records\n";
            
            foreach ($audiences as $audience) {
                // Process each audience member
                $this->processAudience($audience);
            }
            
            // Return false to stop processing
            // return false;
        });
    }

    /**
     * Example 2: Using scrollChunk() for better performance with very large datasets
     */
    public function scrollChunkExample()
    {
        // Process 2000 records at a time using scroll API
        $processed = DemoAudience::scrollChunk(2000, function ($audiences, $page) {
            echo "Scroll processing page {$page} with " . $audiences->count() . " records\n";
            echo "Total hits: " . $audiences->total() . "\n";
            
            foreach ($audiences as $audience) {
                // Process each audience member
                $this->processAudience($audience);
            }
            
            // You can stop processing by returning false
            if ($page >= 10) { // Stop after 10 pages
                return false;
            }
        }, '2m'); // 2 minute scroll timeout
        
        echo "Total processed: {$processed} records\n";
    }

    /**
     * Example 3: Using scroll() to get all data at once
     */
    public function scrollAllExample()
    {
        // Get all records using scroll (good for exports)
        $allAudiences = DemoAudience::where('status', 'active')
            ->scroll(1500); // 1500 records per scroll batch
        
        echo "Total records: " . $allAudiences->total() . "\n";
        echo "Max score: " . $allAudiences->maxScore() . "\n";
        echo "Query took: " . $allAudiences->took() . "ms\n";
        
        // Process all records
        foreach ($allAudiences as $audience) {
            $this->processAudience($audience);
        }
    }

    /**
     * Example 4: Using all() method (automatically chooses best method)
     */
    public function smartAllExample()
    {
        // Automatically uses scroll for large datasets, regular query for small ones
        $audiences = DemoAudience::where('age', '>=', 18)->all();
        
        echo "Retrieved " . $audiences->count() . " adult audience members\n";
    }

    /**
     * Example 5: Complex query with scroll
     */
    public function complexScrollExample()
    {
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->where('age', '>=', 18)
            ->whereIn('location', ['New York', 'Los Angeles', 'Chicago'])
            ->search('technology OR sports', ['interests', 'bio'])
            ->orderBy('created_at', 'desc')
            ->scroll(1000, '5m'); // 5 minute scroll timeout
        
        echo "Found " . $results->total() . " matching records\n";
        
        // Group by location
        $byLocation = [];
        foreach ($results as $audience) {
            $location = $audience->location;
            if (!isset($byLocation[$location])) {
                $byLocation[$location] = 0;
            }
            $byLocation[$location]++;
        }
        
        foreach ($byLocation as $location => $count) {
            echo "{$location}: {$count} people\n";
        }
    }

    /**
     * Example 6: Export to CSV using scroll
     */
    public function exportToCsvExample()
    {
        $filename = 'audience_export_' . date('Y-m-d_H-i-s') . '.csv';
        $file = fopen($filename, 'w');
        
        // Write CSV header
        fputcsv($file, ['ID', 'Name', 'Email', 'Age', 'Location', 'Created At']);
        
        $totalExported = 0;
        
        DemoAudience::scrollChunk(1000, function ($audiences) use ($file, &$totalExported) {
            foreach ($audiences as $audience) {
                fputcsv($file, [
                    $audience->getKey(),
                    $audience->name,
                    $audience->email,
                    $audience->age,
                    $audience->location,
                    $audience->created_at,
                ]);
                $totalExported++;
            }
            
            echo "Exported {$totalExported} records so far...\n";
        });
        
        fclose($file);
        echo "Export complete! {$totalExported} records exported to {$filename}\n";
    }

    /**
     * Example 7: Bulk update using scroll
     */
    public function bulkUpdateExample()
    {
        $updated = 0;
        
        DemoAudience::where('last_login', '<', '2023-01-01')
            ->scrollChunk(500, function ($audiences) use (&$updated) {
                foreach ($audiences as $audience) {
                    $audience->status = 'inactive';
                    $audience->updated_at = now();
                    $audience->save();
                    $updated++;
                }
                
                echo "Updated {$updated} records so far...\n";
            });
        
        echo "Bulk update complete! Updated {$updated} inactive users\n";
    }

    /**
     * Example 8: Memory-efficient aggregation processing
     */
    public function memoryEfficientAggregationExample()
    {
        $locationStats = [];
        $ageGroups = ['18-25' => 0, '26-35' => 0, '36-45' => 0, '46+' => 0];
        
        DemoAudience::scrollChunk(1000, function ($audiences) use (&$locationStats, &$ageGroups) {
            foreach ($audiences as $audience) {
                // Count by location
                $location = $audience->location;
                if (!isset($locationStats[$location])) {
                    $locationStats[$location] = ['count' => 0, 'total_age' => 0];
                }
                $locationStats[$location]['count']++;
                $locationStats[$location]['total_age'] += $audience->age;
                
                // Count by age group
                $age = $audience->age;
                if ($age >= 18 && $age <= 25) {
                    $ageGroups['18-25']++;
                } elseif ($age >= 26 && $age <= 35) {
                    $ageGroups['26-35']++;
                } elseif ($age >= 36 && $age <= 45) {
                    $ageGroups['36-45']++;
                } else {
                    $ageGroups['46+']++;
                }
            }
        });
        
        // Calculate averages
        foreach ($locationStats as $location => &$stats) {
            $stats['avg_age'] = $stats['total_age'] / $stats['count'];
        }
        
        echo "Location Statistics:\n";
        foreach ($locationStats as $location => $stats) {
            echo "{$location}: {$stats['count']} people, avg age: " . round($stats['avg_age'], 1) . "\n";
        }
        
        echo "\nAge Group Distribution:\n";
        foreach ($ageGroups as $group => $count) {
            echo "{$group}: {$count} people\n";
        }
    }

    /**
     * Example 9: Error handling with scroll
     */
    public function errorHandlingExample()
    {
        try {
            $processed = DemoAudience::scrollChunk(1000, function ($audiences, $page) {
                echo "Processing page {$page}...\n";
                
                foreach ($audiences as $audience) {
                    try {
                        $this->processAudience($audience);
                    } catch (\Exception $e) {
                        echo "Error processing audience {$audience->getKey()}: " . $e->getMessage() . "\n";
                        // Continue processing other records
                    }
                }
                
                // Stop if we encounter too many errors
                if ($page > 100) {
                    echo "Stopping after 100 pages\n";
                    return false;
                }
            });
            
            echo "Successfully processed {$processed} records\n";
            
        } catch (\Exception $e) {
            echo "Scroll operation failed: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Example 10: Performance comparison
     */
    public function performanceComparisonExample()
    {
        $start = microtime(true);
        
        // Method 1: Regular pagination (slower for large datasets)
        echo "Testing regular pagination...\n";
        $count1 = 0;
        DemoAudience::chunk(1000, function ($audiences) use (&$count1) {
            $count1 += $audiences->count();
        });
        $time1 = microtime(true) - $start;
        
        $start = microtime(true);
        
        // Method 2: Scroll API (faster for large datasets)
        echo "Testing scroll API...\n";
        $count2 = 0;
        DemoAudience::scrollChunk(1000, function ($audiences) use (&$count2) {
            $count2 += $audiences->count();
        });
        $time2 = microtime(true) - $start;
        
        echo "Regular pagination: {$count1} records in " . round($time1, 2) . " seconds\n";
        echo "Scroll API: {$count2} records in " . round($time2, 2) . " seconds\n";
        echo "Scroll is " . round($time1 / $time2, 2) . "x faster\n";
    }

    /**
     * Helper method to process individual audience member
     */
    private function processAudience($audience)
    {
        // Your processing logic here
        // For example: send email, update external system, etc.
        
        // Simulate some processing time
        usleep(1000); // 1ms delay
    }
}

/**
 * Usage Tips:
 * 
 * 1. Use chunk() for moderate datasets (< 100k records)
 * 2. Use scrollChunk() for large datasets (> 100k records)
 * 3. Use scroll() when you need all data at once
 * 4. Use all() to let the system choose the best method
 * 
 * Performance Guidelines:
 * - Scroll is more efficient for large datasets
 * - Chunk size of 1000-2000 is usually optimal
 * - Longer scroll timeouts for slower processing
 * - Always handle errors gracefully
 * 
 * Memory Considerations:
 * - scroll() loads all data into memory
 * - scrollChunk() processes data in batches (memory efficient)
 * - chunk() uses offset/limit (can be slower for deep pagination)
 */
