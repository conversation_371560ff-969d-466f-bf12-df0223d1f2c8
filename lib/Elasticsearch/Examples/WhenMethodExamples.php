<?php

namespace Lib\Elasticsearch\Examples;

use Lib\Elasticsearch\Model;

/**
 * Examples of using the "when" method for conditional query building
 */
class WhenMethodExamples
{
    /**
     * Example 1: Basic when usage
     */
    public function basicWhenExample($searchTerm = null, $status = null)
    {
        $results = DemoAudience::query()
            ->when($searchTerm, function ($query, $term) {
                return $query->search($term, ['name', 'bio']);
            })
            ->when($status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->get();

        return $results;
    }

    /**
     * Example 2: When with else (default callback)
     */
    public function whenWithElseExample($userRole, $userId)
    {
        $results = DemoAudience::query()
            ->when($userRole === 'admin', 
                function ($query) {
                    // Admin can see all records
                    return $query;
                },
                function ($query) use ($userId) {
                    // Regular users can only see their own records
                    return $query->where('user_id', $userId);
                }
            )
            ->get();

        return $results;
    }

    /**
     * Example 3: Complex filtering with multiple conditions
     */
    public function complexFilteringExample($filters = [])
    {
        $query = DemoAudience::query();

        $results = $query
            ->when($filters['name'] ?? null, function ($query, $name) {
                return $query->match('name', $name);
            })
            ->when($filters['age_min'] ?? null, function ($query, $ageMin) {
                return $query->where('age', '>=', $ageMin);
            })
            ->when($filters['age_max'] ?? null, function ($query, $ageMax) {
                return $query->where('age', '<=', $ageMax);
            })
            ->when($filters['locations'] ?? null, function ($query, $locations) {
                return $query->whereIn('location', $locations);
            })
            ->when($filters['interests'] ?? null, function ($query, $interests) {
                return $query->whereIn('interests', $interests);
            })
            ->when($filters['created_after'] ?? null, function ($query, $date) {
                return $query->where('created_at', '>=', $date);
            })
            ->when($filters['has_email'] ?? false, function ($query) {
                return $query->whereNotNull('email');
            })
            ->when($filters['sort_by'] ?? null, function ($query, $sortBy) {
                $direction = $filters['sort_direction'] ?? 'asc';
                return $query->orderBy($sortBy, $direction);
            })
            ->get();

        return $results;
    }

    /**
     * Example 4: Using unless method (opposite of when)
     */
    public function unlessExample($includeInactive = false, $userRole = 'user')
    {
        $results = DemoAudience::query()
            ->unless($includeInactive, function ($query) {
                // Unless includeInactive is true, filter out inactive records
                return $query->where('status', 'active');
            })
            ->unless($userRole === 'admin', function ($query) {
                // Unless user is admin, apply additional restrictions
                return $query->where('is_public', true);
            })
            ->get();

        return $results;
    }

    /**
     * Example 5: Dynamic search with multiple search types
     */
    public function dynamicSearchExample($searchTerm, $searchType = 'all')
    {
        $results = DemoAudience::query()
            ->when($searchType === 'name', function ($query) use ($searchTerm) {
                return $query->match('name', $searchTerm);
            })
            ->when($searchType === 'email', function ($query) use ($searchTerm) {
                return $query->match('email', $searchTerm);
            })
            ->when($searchType === 'location', function ($query) use ($searchTerm) {
                return $query->match('location', $searchTerm);
            })
            ->when($searchType === 'all', function ($query) use ($searchTerm) {
                return $query->search($searchTerm, ['name', 'email', 'location', 'bio']);
            })
            ->get();

        return $results;
    }

    /**
     * Example 6: Conditional aggregations
     */
    public function conditionalAggregationsExample($includeStats = false, $groupByLocation = false)
    {
        $query = DemoAudience::query()
            ->where('status', 'active')
            ->when($includeStats, function ($query) {
                return $query
                    ->aggregate('avg_age', ['avg' => ['field' => 'age']])
                    ->aggregate('total_users', ['value_count' => ['field' => '_id']]);
            })
            ->when($groupByLocation, function ($query) {
                return $query->aggregate('by_location', [
                    'terms' => ['field' => 'location', 'size' => 10]
                ]);
            });

        $results = $query->get();

        // Access aggregations if they were added
        if ($includeStats) {
            $avgAge = $results->aggregation('avg_age')['value'] ?? null;
            $totalUsers = $results->aggregation('total_users')['value'] ?? null;
            echo "Average age: {$avgAge}, Total users: {$totalUsers}\n";
        }

        if ($groupByLocation) {
            $locationStats = $results->aggregation('by_location')['buckets'] ?? [];
            foreach ($locationStats as $location) {
                echo "Location: {$location['key']}, Count: {$location['doc_count']}\n";
            }
        }

        return $results;
    }

    /**
     * Example 7: Conditional scroll processing
     */
    public function conditionalScrollExample($useScroll = true, $batchSize = 1000)
    {
        $query = DemoAudience::query()
            ->where('status', 'active')
            ->when($useScroll, function ($query) use ($batchSize) {
                // Use scroll for large datasets
                return $query->scrollChunk($batchSize, function ($data, $page) {
                    echo "Scroll - Page {$page}: " . $data->count() . " records\n";
                    $this->processRecords($data);
                });
            }, function ($query) use ($batchSize) {
                // Use regular chunk for smaller datasets
                return $query->chunk($batchSize, function ($data, $page) {
                    echo "Chunk - Page {$page}: " . $data->count() . " records\n";
                    $this->processRecords($data);
                });
            });

        return $query;
    }

    /**
     * Example 8: API endpoint with flexible filtering
     */
    public function apiEndpointExample($request)
    {
        // Simulate request parameters
        $searchTerm = $request['search'] ?? null;
        $status = $request['status'] ?? null;
        $ageMin = $request['age_min'] ?? null;
        $ageMax = $request['age_max'] ?? null;
        $location = $request['location'] ?? null;
        $sortBy = $request['sort_by'] ?? 'created_at';
        $sortDirection = $request['sort_direction'] ?? 'desc';
        $perPage = $request['per_page'] ?? 20;
        $page = $request['page'] ?? 1;

        $results = DemoAudience::query()
            ->when($searchTerm, function ($query, $term) {
                return $query->search($term, ['name', 'email', 'bio']);
            })
            ->when($status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->when($ageMin, function ($query, $min) {
                return $query->where('age', '>=', $min);
            })
            ->when($ageMax, function ($query, $max) {
                return $query->where('age', '<=', $max);
            })
            ->when($location, function ($query, $loc) {
                return $query->where('location', $loc);
            })
            ->orderBy($sortBy, $sortDirection)
            ->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        return [
            'data' => $results->toArray(),
            'meta' => [
                'total' => $results->total(),
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($results->total() / $perPage),
            ]
        ];
    }

    /**
     * Example 9: Conditional highlighting
     */
    public function conditionalHighlightingExample($searchTerm, $enableHighlighting = true)
    {
        $results = DemoAudience::query()
            ->search($searchTerm, ['name', 'bio', 'interests'])
            ->when($enableHighlighting, function ($query) {
                return $query->highlight(['name', 'bio', 'interests'], [
                    'pre_tags' => ['<mark>'],
                    'post_tags' => ['</mark>'],
                    'fragment_size' => 150,
                    'number_of_fragments' => 3
                ]);
            })
            ->get();

        if ($enableHighlighting) {
            foreach ($results as $result) {
                $highlights = $result->getHighlight();
                if (!empty($highlights)) {
                    echo "Highlighted content for {$result->name}:\n";
                    foreach ($highlights as $field => $fragments) {
                        echo "  {$field}: " . implode(' ... ', $fragments) . "\n";
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Example 10: Chaining multiple when conditions
     */
    public function chainedWhenExample($filters)
    {
        $isAdmin = $filters['user_role'] === 'admin';
        $hasSearchTerm = !empty($filters['search']);
        $hasDateRange = !empty($filters['start_date']) && !empty($filters['end_date']);
        $includeDeleted = $filters['include_deleted'] ?? false;

        $results = DemoAudience::query()
            // Base query - always applied
            ->where('type', 'user')
            
            // Conditional search
            ->when($hasSearchTerm, function ($query) use ($filters) {
                return $query->search($filters['search'], ['name', 'email', 'bio']);
            })
            
            // Date range filtering
            ->when($hasDateRange, function ($query) use ($filters) {
                return $query
                    ->where('created_at', '>=', $filters['start_date'])
                    ->where('created_at', '<=', $filters['end_date']);
            })
            
            // Admin-only features
            ->when($isAdmin, function ($query) use ($includeDeleted) {
                return $query
                    ->when($includeDeleted, function ($q) {
                        // Include soft-deleted records for admins
                        return $q->where('deleted_at', '!=', null);
                    })
                    ->aggregate('status_breakdown', [
                        'terms' => ['field' => 'status', 'size' => 10]
                    ]);
            })
            
            // Non-admin restrictions
            ->unless($isAdmin, function ($query) {
                return $query
                    ->where('status', 'active')
                    ->where('is_public', true);
            })
            
            ->orderBy('created_at', 'desc')
            ->get();

        return $results;
    }

    /**
     * Helper method to process records
     */
    private function processRecords($records)
    {
        foreach ($records as $record) {
            // Your processing logic here
            // For example: update external system, send notifications, etc.
        }
    }
}

/**
 * Usage Tips:
 * 
 * 1. Use when() for conditional query building
 * 2. Use unless() for negative conditions
 * 3. Chain multiple when() calls for complex logic
 * 4. Use the default callback for else conditions
 * 5. when() and unless() always return the query builder for chaining
 * 
 * Benefits:
 * - Cleaner code than if/else statements
 * - Maintains query builder chain
 * - More readable conditional logic
 * - Consistent with Laravel Eloquent patterns
 */
