<?php

namespace Lib\Elasticsearch\Examples;

use Lib\Elasticsearch\Model;

/**
 * Example of how to migrate your existing DemoAudience model
 * to use the custom Elasticsearch package
 */
class DemoAudience extends Model
{
    /**
     * The index associated with the model.
     * If not specified, it will use the pluralized snake_case class name
     */
    protected $index = 'demo_audiences'; // or leave empty to auto-generate

    /**
     * The type associated with the model.
     * If not specified, it will use the snake_case class name
     */
    protected $type = 'demo_audience'; // or leave empty to auto-generate

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'email',
        'age',
        'location',
        'interests',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'age' => 'integer',
        'interests' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for arrays.
     */
    protected $hidden = [
        'internal_id',
        'secret_data',
    ];

    /**
     * Scope a query to only include adults.
     */
    public function scopeAdults($query)
    {
        return $query->where('age', '>=', 18);
    }

    /**
     * Scope a query to search by location.
     */
    public function scopeInLocation($query, $location)
    {
        return $query->where('location', $location);
    }

    /**
     * Scope a query to search by interests.
     */
    public function scopeWithInterest($query, $interest)
    {
        return $query->where('interests', $interest);
    }

    /**
     * Get the full name attribute.
     */
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Set the email attribute to lowercase.
     */
    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower($value);
    }
}

/**
 * Usage examples:
 */
class DemoAudienceExamples
{
    public function examples()
    {
        // Create a new audience member
        $audience = DemoAudience::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'age' => 25,
            'location' => 'New York',
            'interests' => ['technology', 'sports'],
        ]);

        // Find by ID
        $audience = DemoAudience::find('some_document_id');

        // Search for adults in New York
        $adults = DemoAudience::adults()
            ->inLocation('New York')
            ->get();

        // Search by interests
        $techLovers = DemoAudience::withInterest('technology')
            ->orderBy('age', 'desc')
            ->get();

        // Full-text search
        $searchResults = DemoAudience::search('john technology')
            ->highlight(['name', 'interests'])
            ->get();

        // Complex query with aggregations
        $results = DemoAudience::query()
            ->where('age', '>=', 18)
            ->aggregate('avg_age', ['avg' => ['field' => 'age']])
            ->aggregate('locations', [
                'terms' => ['field' => 'location', 'size' => 10]
            ])
            ->get();

        $averageAge = $results->aggregation('avg_age')['value'];
        $topLocations = $results->aggregation('locations')['buckets'];

        // Update
        $audience = DemoAudience::find('some_id');
        $audience->age = 26;
        $audience->save();

        // Delete
        $audience->delete();

        // Bulk operations using chunk
        DemoAudience::chunk(100, function ($audiences) {
            foreach ($audiences as $audience) {
                // Process each audience member
                $audience->processed = true;
                $audience->save();
            }
        });

        // Count
        $totalAdults = DemoAudience::adults()->count();

        // Statistical aggregations
        $avgAge = DemoAudience::avg('age');
        $maxAge = DemoAudience::max('age');
        $minAge = DemoAudience::min('age');
        $totalAge = DemoAudience::sum('age');
    }
}
