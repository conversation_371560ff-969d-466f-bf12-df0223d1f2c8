<?php

namespace Lib\Elasticsearch\Examples;

use Lib\Elasticsearch\Model;

/**
 * Examples of using OR where methods for building complex queries
 */
class OrWhereExamples
{
    /**
     * Example 1: Basic OR conditions
     */
    public function basicOrExample()
    {
        // Find users who are either admins OR moderators
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->where(function ($query) {
                $query->where('role', 'admin')
                      ->orWhere('role', 'moderator');
            })
            ->get();

        // Alternative syntax using orWhere directly
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->where('role', 'admin')
            ->orWhere('role', 'moderator')
            ->get();

        return $results;
    }

    /**
     * Example 2: OR with different operators
     */
    public function orWithOperatorsExample()
    {
        // Find users who are either young (< 25) OR senior (> 65)
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->where('age', '<', 25)
            ->orWhere('age', '>', 65)
            ->get();

        return $results;
    }

    /**
     * Example 3: OR with IN clauses
     */
    public function orWhereInExample()
    {
        // Find users from specific cities OR with specific interests
        $results = DemoAudience::query()
            ->whereIn('location', ['New York', 'Los Angeles', 'Chicago'])
            ->orWhereIn('interests', ['technology', 'sports', 'music'])
            ->get();

        return $results;
    }

    /**
     * Example 4: Complex OR conditions with search
     */
    public function complexOrSearchExample($searchTerm)
    {
        // Search in multiple fields with OR logic
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->where(function ($query) use ($searchTerm) {
                $query->match('name', $searchTerm)
                      ->orWhere('email', 'like', $searchTerm)
                      ->orWhere('bio', 'like', $searchTerm);
            })
            ->get();

        return $results;
    }

    /**
     * Example 5: OR with null checks
     */
    public function orWithNullExample()
    {
        // Find users who either have no email OR no phone
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->whereNull('email')
            ->orWhereNull('phone')
            ->get();

        // Find users who have email OR phone (not null)
        $results2 = DemoAudience::query()
            ->where('status', 'active')
            ->whereNotNull('email')
            ->orWhereNotNull('phone')
            ->get();

        return [$results, $results2];
    }

    /**
     * Example 6: OR with date ranges
     */
    public function orWithDateRangesExample()
    {
        // Find users created recently OR updated recently
        $recentDate = date('Y-m-d', strtotime('-30 days'));
        
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->where('created_at', '>=', $recentDate)
            ->orWhere('updated_at', '>=', $recentDate)
            ->get();

        return $results;
    }

    /**
     * Example 7: OR with between clauses
     */
    public function orWithBetweenExample()
    {
        // Find users in young age group OR senior age group
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->whereBetween('age', [18, 25])
            ->orWhereBetween('age', [60, 80])
            ->get();

        return $results;
    }

    /**
     * Example 8: Nested OR conditions with when
     */
    public function nestedOrWithWhenExample($filters = [])
    {
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->when($filters['search'] ?? null, function ($query, $term) {
                // Search in multiple fields with OR
                return $query->where(function ($q) use ($term) {
                    $q->match('name', $term)
                      ->orMatch('email', $term)
                      ->orMatch('bio', $term);
                });
            })
            ->when($filters['locations'] ?? null, function ($query, $locations) {
                return $query->whereIn('location', $locations);
            })
            ->when($filters['age_groups'] ?? null, function ($query, $ageGroups) {
                // OR multiple age ranges
                return $query->where(function ($q) use ($ageGroups) {
                    foreach ($ageGroups as $group) {
                        [$min, $max] = explode('-', $group);
                        $q->orWhereBetween('age', [(int)$min, (int)$max]);
                    }
                });
            })
            ->get();

        return $results;
    }

    /**
     * Example 9: OR with aggregations
     */
    public function orWithAggregationsExample()
    {
        // Find users with specific criteria and get stats
        $results = DemoAudience::query()
            ->where('status', 'active')
            ->where('role', 'user')
            ->orWhere('role', 'premium')
            ->aggregate('role_breakdown', [
                'terms' => ['field' => 'role', 'size' => 10]
            ])
            ->aggregate('avg_age_by_role', [
                'terms' => [
                    'field' => 'role',
                    'size' => 10
                ],
                'aggs' => [
                    'avg_age' => [
                        'avg' => ['field' => 'age']
                    ]
                ]
            ])
            ->get();

        // Access aggregation results
        $roleBreakdown = $results->aggregation('role_breakdown')['buckets'] ?? [];
        $ageByRole = $results->aggregation('avg_age_by_role')['buckets'] ?? [];

        foreach ($roleBreakdown as $role) {
            echo "Role: {$role['key']}, Count: {$role['doc_count']}\n";
        }

        foreach ($ageByRole as $role) {
            $avgAge = $role['avg_age']['value'] ?? 0;
            echo "Role: {$role['key']}, Avg Age: " . round($avgAge, 1) . "\n";
        }

        return $results;
    }

    /**
     * Example 10: Performance comparison - OR vs multiple queries
     */
    public function performanceComparisonExample()
    {
        $start = microtime(true);

        // Method 1: Single query with OR conditions
        $results1 = DemoAudience::query()
            ->where('status', 'active')
            ->where('location', 'New York')
            ->orWhere('location', 'Los Angeles')
            ->orWhere('location', 'Chicago')
            ->get();

        $time1 = microtime(true) - $start;

        $start = microtime(true);

        // Method 2: Using whereIn (more efficient for multiple values)
        $results2 = DemoAudience::query()
            ->where('status', 'active')
            ->whereIn('location', ['New York', 'Los Angeles', 'Chicago'])
            ->get();

        $time2 = microtime(true) - $start;

        echo "OR method: " . round($time1 * 1000, 2) . "ms\n";
        echo "WhereIn method: " . round($time2 * 1000, 2) . "ms\n";
        echo "WhereIn is " . round($time1 / $time2, 2) . "x faster\n";

        return [$results1, $results2];
    }

    /**
     * Example 11: API endpoint with flexible OR filtering
     */
    public function apiEndpointWithOrExample($request)
    {
        $query = DemoAudience::query()->where('status', 'active');

        // Handle multiple search fields with OR
        if ($searchTerm = $request['search'] ?? null) {
            $query->where(function ($q) use ($searchTerm) {
                $q->match('name', $searchTerm)
                  ->orMatch('email', $searchTerm)
                  ->orMatch('bio', $searchTerm);
            });
        }

        // Handle multiple categories with OR
        if ($categories = $request['categories'] ?? null) {
            $query->whereIn('category', $categories);
        }

        // Handle age ranges with OR
        if ($ageRanges = $request['age_ranges'] ?? null) {
            $query->where(function ($q) use ($ageRanges) {
                foreach ($ageRanges as $range) {
                    [$min, $max] = explode('-', $range);
                    $q->orWhereBetween('age', [(int)$min, (int)$max]);
                }
            });
        }

        // Handle multiple statuses with OR
        if ($statuses = $request['statuses'] ?? null) {
            $query->whereIn('account_status', $statuses);
        }

        $results = $query
            ->orderBy('created_at', 'desc')
            ->limit($request['limit'] ?? 20)
            ->get();

        return [
            'data' => $results->toArray(),
            'meta' => [
                'total' => $results->total(),
                'count' => $results->count(),
                'took' => $results->took() . 'ms'
            ]
        ];
    }

    /**
     * Example 12: OR with scroll processing
     */
    public function orWithScrollExample()
    {
        $processed = 0;

        // Process users who are either VIP OR have high activity
        DemoAudience::query()
            ->where('status', 'active')
            ->where('account_type', 'vip')
            ->orWhere('activity_score', '>', 80)
            ->scrollChunk(1000, function ($users, $page) use (&$processed) {
                echo "Processing page {$page} with " . $users->count() . " users\n";
                
                foreach ($users as $user) {
                    // Process VIP or high-activity users
                    $this->processSpecialUser($user);
                    $processed++;
                }
                
                echo "Processed {$processed} users so far...\n";
            });

        echo "Total processed: {$processed} special users\n";
        return $processed;
    }

    /**
     * Helper method to process special users
     */
    private function processSpecialUser($user)
    {
        // Your special processing logic here
        // For example: send premium notifications, update rewards, etc.
    }
}

/**
 * Usage Tips:
 * 
 * 1. Use orWhere() for simple OR conditions
 * 2. Use whereIn() instead of multiple orWhere() for the same field
 * 3. Group complex OR logic in closures for clarity
 * 4. Consider performance implications of OR queries
 * 5. Use minimum_should_match for pure OR queries
 * 
 * Performance Notes:
 * - OR queries can be slower than AND queries
 * - whereIn() is more efficient than multiple orWhere() for same field
 * - Consider using should clauses for optional conditions
 * - Use aggregations to get statistics on OR results
 */
