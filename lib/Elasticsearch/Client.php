<?php

namespace Lib\Elasticsearch;

use Guz<PERSON><PERSON>ttp\Client as HttpClient;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class Client
{
    protected $httpClient;
    protected $hosts;
    protected $username;
    protected $password;
    protected $timeout;

    public function __construct(array $config = [])
    {
        $this->hosts = $config['hosts'] ?? ['http://localhost:9200'];
        $this->username = $config['username'] ?? null;
        $this->password = $config['password'] ?? null;
        $this->timeout = $config['timeout'] ?? 30;

        $this->httpClient = new HttpClient([
            'timeout' => $this->timeout,
            'auth' => $this->username && $this->password ? [$this->username, $this->password] : null,
        ]);
    }

    /**
     * Get cluster info
     */
    public function info()
    {
        return $this->request('GET', '/');
    }

    /**
     * Check if index exists
     */
    public function indexExists($index)
    {
        try {
            $this->request('HEAD', "/{$index}");
            return true;
        } catch (RequestException $e) {
            if ($e->getResponse() && $e->getResponse()->getStatusCode() === 404) {
                return false;
            }
            throw $e;
        }
    }

    /**
     * Create index
     */
    public function createIndex($index, array $body = [])
    {
        return $this->request('PUT', "/{$index}", $body);
    }

    /**
     * Delete index
     */
    public function deleteIndex($index)
    {
        return $this->request('DELETE', "/{$index}");
    }

    /**
     * Index a document
     */
    public function index($index, $type, $id = null, array $body = [])
    {
        $path = "/{$index}/{$type}";
        if ($id) {
            $path .= "/{$id}";
            $method = 'PUT';
        } else {
            $method = 'POST';
        }

        return $this->request($method, $path, $body);
    }

    /**
     * Get a document
     */
    public function get($index, $type, $id)
    {
        return $this->request('GET', "/{$index}/{$type}/{$id}");
    }

    /**
     * Update a document
     */
    public function update($index, $type, $id, array $body)
    {
        return $this->request('POST', "/{$index}/{$type}/{$id}/_update", $body);
    }

    /**
     * Delete a document
     */
    public function delete($index, $type, $id)
    {
        return $this->request('DELETE', "/{$index}/{$type}/{$id}");
    }

    /**
     * Search documents (ES 2.4.6 compatible)
     */
    public function search($index = null, $type = null, array $body = [])
    {
        $path = '';
        if ($index) {
            $path .= "/{$index}";
            if ($type) {
                $path .= "/{$type}";
            }
        }
        $path .= '/_search';

        // Handle scroll parameter for ES 2.4.6
        $queryParams = [];
        if (isset($body['scroll'])) {
            $queryParams['scroll'] = $body['scroll'];
            unset($body['scroll']); // Remove from body
        }

        // Add query parameters to path
        if (!empty($queryParams)) {
            $path .= '?' . http_build_query($queryParams);
        }

        return $this->request('POST', $path, $body);
    }

    /**
     * Count documents
     */
    public function count($index = null, $type = null, array $body = [])
    {
        $path = '';
        if ($index) {
            $path .= "/{$index}";
            if ($type) {
                $path .= "/{$type}";
            }
        }
        $path .= '/_count';

        return $this->request('POST', $path, $body);
    }

    /**
     * Bulk operations
     */
    public function bulk(array $body)
    {
        $bulkBody = '';
        foreach ($body as $operation) {
            $bulkBody .= json_encode($operation) . "\n";
        }

        return $this->request('POST', '/_bulk', $bulkBody, [
            'Content-Type' => 'application/x-ndjson'
        ]);
    }

    /**
     * Scroll search results
     */
    public function scroll($scrollId, $scrollTimeout = '1m')
    {
        return $this->request('POST', '/_search/scroll', [
            'scroll' => $scrollTimeout,
            'scroll_id' => $scrollId
        ]);
    }

    /**
     * Clear scroll context
     */
    public function clearScroll($scrollId)
    {
        try {
            return $this->request('DELETE', '/_search/scroll', [
                'scroll_id' => $scrollId
            ]);
        } catch (\Exception $e) {
            // Ignore errors when clearing scroll
            return null;
        }
    }

    /**
     * Make HTTP request to Elasticsearch
     */
    public function request($method, $path, $body = null, array $headers = [])
    {
        $host = $this->getRandomHost();
        $url = rtrim($host, '/') . $path;

        $options = [
            'headers' => array_merge([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ], $headers),
        ];

        if ($body !== null) {
            if (is_array($body)) {
                $options['json'] = $body;
            } else {
                $options['body'] = $body;
            }
        }

        try {
            $response = $this->httpClient->request($method, $url, $options);
            $content = $response->getBody()->getContents();

            return json_decode($content, true);
        } catch (RequestException $e) {
            Log::error('Elasticsearch request failed', [
                'method' => $method,
                'url' => $url,
                'error' => $e->getMessage(),
                'response' => $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null,
            ]);

            throw $e;
        }
    }

    /**
     * Get random host for load balancing
     */
    protected function getRandomHost()
    {
        return $this->hosts[array_rand($this->hosts)];
    }
}
