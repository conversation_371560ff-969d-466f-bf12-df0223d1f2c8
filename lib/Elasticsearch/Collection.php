<?php

namespace Lib\Elasticsearch;

use Illuminate\Support\Collection as BaseCollection;

class Collection extends BaseCollection
{
    protected $total;
    protected $maxScore;
    protected $took;
    protected $timedOut;
    protected $shards;
    protected $aggregations;

    public function __construct($items = [], array $meta = [])
    {
        parent::__construct($items);
        
        $this->total = $meta['total'] ?? 0;
        $this->maxScore = $meta['max_score'] ?? null;
        $this->took = $meta['took'] ?? 0;
        $this->timedOut = $meta['timed_out'] ?? false;
        $this->shards = $meta['_shards'] ?? [];
        $this->aggregations = $meta['aggregations'] ?? [];
    }

    /**
     * Get total number of hits
     */
    public function total()
    {
        return $this->total;
    }

    /**
     * Get maximum score
     */
    public function maxScore()
    {
        return $this->maxScore;
    }

    /**
     * Get query execution time in milliseconds
     */
    public function took()
    {
        return $this->took;
    }

    /**
     * Check if query timed out
     */
    public function timedOut()
    {
        return $this->timedOut;
    }

    /**
     * Get shards information
     */
    public function shards()
    {
        return $this->shards;
    }

    /**
     * Get aggregations
     */
    public function aggregations()
    {
        return $this->aggregations;
    }

    /**
     * Get aggregation by name
     */
    public function aggregation($name)
    {
        return $this->aggregations[$name] ?? null;
    }

    /**
     * Get the scores of all items
     */
    public function scores()
    {
        return $this->map(function ($item) {
            return $item->getScore();
        });
    }

    /**
     * Get items with scores above threshold
     */
    public function whereScoreAbove($threshold)
    {
        return $this->filter(function ($item) use ($threshold) {
            return $item->getScore() > $threshold;
        });
    }

    /**
     * Sort by score descending
     */
    public function sortByScore($direction = 'desc')
    {
        return $this->sortBy(function ($item) {
            return $item->getScore();
        }, SORT_REGULAR, $direction === 'desc');
    }

    /**
     * Get highlights for all items
     */
    public function highlights()
    {
        return $this->map(function ($item) {
            return $item->getHighlight();
        })->filter();
    }

    /**
     * Convert to array with metadata
     */
    public function toArrayWithMeta()
    {
        return [
            'data' => $this->toArray(),
            'meta' => [
                'total' => $this->total,
                'max_score' => $this->maxScore,
                'took' => $this->took,
                'timed_out' => $this->timedOut,
                'shards' => $this->shards,
                'aggregations' => $this->aggregations,
            ],
        ];
    }
}
