<?php

namespace Lib\Elasticsearch;

use Illuminate\Support\ServiceProvider;

class ElasticsearchServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register()
    {
        $this->app->singleton('elasticsearch.client', function ($app) {
            $config = $app['config']['database.connections.elasticsearch'] ?? [];
            return new Client($config);
        });

        $this->app->alias('elasticsearch.client', Client::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot()
    {
        // Set the client on the Model class
        Model::setClient($this->app->make('elasticsearch.client'));

        // Register commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                Commands\ElasticsearchCommand::class,
            ]);

            // Publish configuration if needed
            $this->publishes([
                __DIR__.'/config/elasticsearch.php' => config_path('elasticsearch.php'),
            ], 'elasticsearch-config');
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides()
    {
        return [
            'elasticsearch.client',
            Client::class,
        ];
    }
}
