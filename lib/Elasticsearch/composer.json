{"name": "lib/elasticsearch", "description": "Elasticsearch 2.4.6 compatible package for Laravel with Eloquent-like functionality", "type": "library", "license": "MIT", "authors": [{"name": "AI", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/support": "^11.0", "illuminate/database": "^11.0", "illuminate/console": "^11.0", "illuminate/contracts": "^11.0", "guzzlehttp/guzzle": "^7.0"}, "require-dev": {"phpunit/phpunit": "^11.0", "orchestra/testbench": "^9.0"}, "autoload": {"psr-4": {"Lib\\Elasticsearch\\": ""}}, "autoload-dev": {"psr-4": {"Lib\\Elasticsearch\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["Lib\\Elasticsearch\\ElasticsearchServiceProvider"]}}, "config": {"sort-packages": true, "preferred-install": "dist"}, "minimum-stability": "dev", "prefer-stable": true}